import{t as Ae,d as et,a5 as Ie,s as ot,bZ as Cs,b as we,o as se,h as _,i as e,r as Rt,Q as je,R as Te,f as Ps,L as Vt,k as o,j as V,S as nt,n as St,a9 as dt,H as ge,a2 as at,a3 as lt,ab as Ft,m as it,A as xs,ak as ye,q as ze,F as ct,G as We,M as Ct,P as Pt,au as _t,e as G,T as xe,at as pt,J as rs,a6 as Me,a7 as Ne,ad as xt,du as mt,ac as Ls,af as gt,N as ds,dv as Lt,ah as us,X as De,aC as Ue,dw as cs,dx as Us,Z as Ve,aJ as Be,dy as qt,dz as es,c7 as Fe,dA as Ut,dB as As,aE as Pe,dC as js,dD as ts,dE as Ts,dF as zt,dG as _s,dH as bt,aF as Kt,dI as At,cb as ps,cc as ms,aA as Qt,ce as Ht,dJ as Es,c1 as ss,c as jt,aw as gs,bd as Rs,E as Gs,dK as kt,bs as ut,a8 as fs,av as ys,dL as Ms,dM as $t,dN as Ee,dO as Ns,dP as Fs,dQ as os,dR as ns,dS as as,dT as Ot,dU as Bt,dV as ws,dW as Yt,dX as Dt,dY as vs,am as It,dZ as Je,d_ as zs,d$ as Os,e0 as Bs,e1 as Ks,e2 as Qs,e3 as Hs,e4 as Ys,e5 as Ds,e6 as Ws,e7 as Js,e8 as Xs,e9 as Zs,ea as qs,eb as eo,ec as to,ed as hs,dr as so,ee as oo,ef as Gt,eg as no,eh as ao,ei as lo,cK as rt,W as io,V as ro}from"./index-CmETSh6Y.js";import{E as Tt,b as uo,a as Wt}from"./el-radio-DoZFrPxL.js";import{u as Ke,E as ks}from"./el-config-provider-DRHX7QJ5.js";import{E as Et}from"./el-space-mudxGYIP.js";import{E as ft}from"./el-input-number-V2-U9i7h.js";import{u as Jt}from"./autoApply-BvulaZhm.js";import{b as Ze}from"./address-DsxfsM69.js";/* empty css                          */import"./el-tooltip-l0sNRNKZ.js";import{u as Xt}from"./useTable-CCLcdu0q.js";import{c as co,p as st,t as Mt,a as _o,b as bs}from"./porpery-DmNwVFIQ.js";import{d as Xe}from"./dayjs.min-Bj8ADfnT.js";import{c as po,a as mo,q as go,g as fo,b as Ss,d as yo,e as $s,s as Is}from"./pddStore-CIiwLQql.js";import{u as wo}from"./task-BrHiRyMn.js";function vo(ie,oe,X={}){var C,Z,v;const f=Ae(oe||{});f.height=ie;const T=Ae({total:0,pageSizes:((C=X.pagination)==null?void 0:C.pageSizes)||[100,200,500,1e3,2e3],limit:((Z=X.pagination)==null?void 0:Z.limit)||100,page:((v=X.pagination)==null?void 0:v.page)||1});return{listProps:f,pagination:T}}const ho=ie=>(at("data-v-f9fef591"),ie=ie(),lt(),ie),ko={class:"log"},bo={class:"between-flex"},So=ho(()=>_("span",{class:"f-s-12"},"任务日志",-1)),$o={class:"right"},Io={class:"wrapper"},Vo={key:0,class:"time"},Co={class:"msg"},Po={class:"wrapper"},xo={key:0,class:"time"},Lo={class:"msg"},Uo=et({__name:"log",props:{list:null,height:{default:"100px"}},emits:["update:list"],setup(ie,{emit:oe}){const X=ie;Ie();const f=Ie({dialog:!1});vo(530,{autoResize:!0,scrollY:{gt:0,sItem:".item",oSize:10}},{});const T=Ie(),C=Ie();return ot(()=>X.list.length,Z=>{Ft(()=>{var h,c,ce,g;const v=(h=T.value)==null?void 0:h.querySelector(".wrapper");(c=T.value)==null||c.scrollTo({top:v==null?void 0:v.clientHeight,behavior:"smooth"});const pe=(ce=C.value)==null?void 0:ce.querySelector(".wrapper");(g=C.value)==null||g.scrollTo({top:pe==null?void 0:pe.clientHeight,behavior:"auto"})})}),Cs(()=>{Ft(()=>{var pe,h,c,ce;const Z=(pe=T.value)==null?void 0:pe.querySelector(".wrapper");(h=T.value)==null||h.scrollTo({top:Z==null?void 0:Z.clientHeight,behavior:"auto"});const v=(c=C.value)==null?void 0:c.querySelector(".wrapper");(ce=C.value)==null||ce.scrollTo({top:v==null?void 0:v.clientHeight,behavior:"auto"})})}),(Z,v)=>{const pe=nt,h=Vt;return se(),we("div",ko,[_("header",bo,[Rt(Z.$slots,"header",{},()=>[Rt(Z.$slots,"header-left",{},()=>[So],!0),_("div",$o,[Rt(Z.$slots,"header-right",{},()=>[e(pe,{type:"primary",size:"small",onClick:v[0]||(v[0]=c=>oe("update:list",[]))},{default:o(()=>[V("清空")]),_:1})],!0),e(pe,{type:"primary",plain:"",onClick:v[1]||(v[1]=c=>f.value.dialog=!0),size:"small"},{default:o(()=>[V("打开日志")]),_:1})])],!0)]),_("div",{class:"container",ref_key:"container",ref:T,style:Ps({height:ie.height})},[_("div",Io,[(se(!0),we(je,null,Te(ie.list.slice(-50),c=>(se(),we("p",{class:St(["item",c.type&&`${c.type}`])},[c.time?(se(),we("span",Vo,ge(c.time),1)):dt("",!0),_("span",Co,ge(`${c.msg}`),1)],2))),256))])],4),e(h,{"destroy-on-close":!0,"append-to-body":!0,class:"log-dialog",modelValue:f.value.dialog,"onUpdate:modelValue":v[2]||(v[2]=c=>f.value.dialog=c),top:"80px",title:"任务日志",width:"1000px"},{default:o(()=>[_("div",{class:"container",ref_key:"container2",ref:C,style:{height:"530px"}},[_("div",Po,[(se(!0),we(je,null,Te(X.list,c=>(se(),we("p",{class:St(["item",c.type&&`${c.type}`])},[c.time?(se(),we("span",xo,ge(c.time),1)):dt("",!0),_("span",Lo,ge(`${c.msg}`),1)],2))),256))])],512)]),_:1},8,["modelValue"])])}}}),yt=it(Uo,[["__scopeId","data-v-f9fef591"]]);function Zt(){return xs().app_info.is_try?(ye.warning({grouping:!0,message:"试用账户不支持"}),!1):!0}function qe(ie,oe,X={}){const f=ze(),{delay:T,berforeInit:C,save:Z=!0}=X;let v,pe=ie;function h(ce){Z&&(T?(v&&clearTimeout(v),v=setTimeout(()=>{v=void 0,f.setAnySetting(oe,ce)},T)):f.setAnySetting(oe,ce))}f.getAnySetting(oe).then(ce=>{if(C){c.value=C(ce);return}typeof ce<"u"&&(c.value=ce)});const c=Ie(pe);return Z&&ot(c,ce=>{h(ce)},{deep:!0}),c}const Qe=ie=>(at("data-v-f4a811a9"),ie=ie(),lt(),ie),Ao={class:"change-sales no-padding"},jo={class:"settings"},To={class:"item account"},Eo=Qe(()=>_("header",null,[_("h3",null,"1.账号配置")],-1)),Ro={class:"content"},Go=Qe(()=>_("span",{class:"primary"},"下单设置",-1)),Mo={class:"item buy"},No=Qe(()=>_("header",null,[_("h3",null,"2.购买配置")],-1)),Fo={class:"content"},zo=Qe(()=>_("span",null,"全类目-20981",-1)),Oo=Qe(()=>_("span",null,"件",-1)),Bo=Qe(()=>_("span",null,"位数",-1)),Ko=Qe(()=>_("span",null,"-",-1)),Qo=Qe(()=>_("p",null,"活动价格：(设置0.8或1.2)",-1)),Ho={class:"item ctrl"},Yo=Qe(()=>_("h3",null,"3.控制台",-1)),Do={class:"content"},Wo=Qe(()=>_("span",null,"催付金额：自动运算最低",-1)),Jo={class:"table-container"},Xo={class:"table-footer"},Zo={key:1},qo={class:"primary"},en={class:"primary",style:{margin:"0 20px"}},tn={class:"success"},sn={class:"danger"},on={class:"danger"},nn=et({__name:"CSActivity",setup(ie){const oe=ze(),X=ct(),f=Ae({start_site:1,activity:"20981",activity_diy:"",activityPrice:.8,goodsBatch:1,num_type:"random",num_times:3,num_range:{v1:100,v2:200},num_appoint:10,isSaveMoney:!1,isCancelActivity:!1,recoverQuantity:"1",lowPriceSku:!0}),T=qe(!1,"changeSalesConfig.isResetSku");ot(f,l=>{oe.setAnySetting("changeSalesConfig",{...l})},{deep:!0}),oe.getAnySetting("changeSalesConfig").then(l=>{l&&(f.activity=l.activity,f.activity_diy=l.activity_diy,f.goodsBatch=l.goodsBatch)});const C=Ae({tableList:[],tableSelection:[],logList:[],goods_id:""});async function Z(){let l=C.goods_id;await De({title:"修改商品ID筛选",showCancelButton:!0,message:Ue("div",{},[Ue("p",{class:"m-b-5 primary"},"请输入商品ID,多个用逗号或换行隔开！"),Ue(cs,{modelValue:C.goods_id,onChange:({value:n})=>{l=n},resize:"none",rows:10,placeholder:"请输入商品ID,多个用逗号或换行隔开！"})])}),C.goods_id=l.split(/[,，\n]/).filter(n=>n.trim()).join(",")}const v=Ae({page:1,limit:100,pageSizes:[100,500,1e3],total:0}),pe=Ie();ot(()=>C.logList.length,l=>{var r,a;let n=((a=(r=pe.value)==null?void 0:r.wrapRef)==null?void 0:a.scrollHeight)||0;n&&Ft(()=>{var p;(p=pe.value)==null||p.setScrollTop(n<0?0:n)})},{immediate:!0});const h=Ie(new Map);function c(l,n){if(C.logList.push({...l,date:Date.now()}),n){const r=n.id||n.goods_id,a=l.msg||"";h.value.has(r)?(h.value.get(r).msg=a,h.value.get(r).type=l.type):h.value.set(r,{msg:a,type:l.type})}}function ce(){c({msg:"  ①：停掉当前产品的限时限量购活动",type:"danger"}),c({msg:"  ②：当前产品在营销活动里面报的活动需要下架",type:"danger"}),c({msg:"  ③：当前产品的物流模板需要改为默认模板（新疆西藏包邮）",type:"danger"}),c({msg:"  ④：如果开通了运费险的需要先暂停掉，不介意的可以不停(店铺管理页面)",type:"danger"}),c({msg:"  ⑤：如果下单遇到风控导致失败，会缓存商品在后面重复操作，最多重复3次，提高成功率",type:"danger"})}ce();const g=Ae({getGoodsList:!1,start:!1,stopFlag:!0});async function Se(l){const{mallId:n}=f,r=X.tableList.find(u=>u.mallId==n);if(!r)return ye.warning({message:"请选择店铺",grouping:!0}),Promise.reject();if(!X.checkAvailable(r))return ye.warning({message:"当前店铺已过期，请重新登录",grouping:!0}),Promise.reject();if(!l)return ye.warning({message:"请选择活动"}),Promise.reject();c({msg:"正在检测店铺是否可参加活动"});const p=await Us(r,l);return p.result.pass_mall_rules?(c({msg:"当前店铺可以参加选中活动",type:"success"}),r):(De({title:"不可参加活动",message:Ue("div",{},p.result.mall_requirement.filter(u=>!u.passed).map((u,w)=>Ue("p",{class:u.passed?"success":"danger"},`(${w+1})${u.requirement}`)))}),Promise.reject())}async function ke(){const{mallId:l}=f,n=X.tableList.find(K=>K.mallId==l);if(!n)return ye.warning({message:"请选择店铺",grouping:!0});if(!X.checkAvailable(n))return ye.warning({message:"当前店铺已过期，请重新登录",grouping:!0});const{goods_id:r}=C;r&&(v.page=1),g.getGoodsList=!0;const a=Math.round(v.limit/100);let p=(v.page-1)*a,u=1;const w=v.page*a,$=[];let M=0;await new Promise(K=>{function J(){c({msg:`正在获取第${v.page}-${u}页商品`}),bt(n,p+u,r).then(s=>{if(s.success){C.tableList=[];const t=s.result.goods_list;M+=t.length,t.forEach(i=>{i.sku_list=i.sku_list.filter(y=>y.isOnsale),i.sku_list.length&&(i._skuSelect=i.sku_list.sort((y,j)=>y.groupPrice-j.groupPrice)[0].skuId,$.push(i))}),v.total=s.result.total,c({msg:`获取商品第${v.page}-${u}页成功`,type:"success"}),s.result.total>M&&p+u<w?g.getGoodsList?(u++,J()):(c({msg:"停止获取",type:"warning"}),K(!0)):K(!0)}else{let t=s.errorMsg||s.error_msg||"";t=="会话已过期"&&(t+=",请重新登录店铺"),c({msg:"获取失败："+t,type:"danger"}),K(!0)}}).catch(s=>{c({msg:"意外的错误,停止获取",type:"danger"}),K(!0)})}J()}),C.tableList=$,c({msg:"获取商品列表结束"}),g.getGoodsList=!1}function Oe(){g.stopFlag=!0,c({msg:"已发送停止指令",type:"danger"}),c({msg:"请不要关闭软件,等待任务停止,",type:"danger"}),De({title:"提示",type:"warning",message:Ue("div",{},[Ue("p",{class:"danger mt-5 fs-18"},"请不要关闭软件"),Ue("p",{class:"mt-5"},"等待系统自动停止,意外的中断需要手动去店铺取消活动")])})}function H(l){if(!l)return;const n=ze(),{chance:r,zfb_pass:a}=n.pay;if(r==="immediate"){if(!a){c({msg:"没有设置支付密码，不会开启自动支付",type:"warning"});return}c({msg:`订单${l},开始获取支付链接`}),Ht({order_sn:l}).then(p=>{const u=Jt();c({msg:`订单${l},已进入自动支付队列`}),u.addToPendding([{...p.data,type:"auto"}])}).catch(p=>{c({msg:`订单${l},获取支付链接失败，不会自动支付`})})}}const L=Ie();async function W(l){var n,r;switch(l){case"check-all":{C.tableList.forEach(a=>{var p;(p=L.value)==null||p.setAllCheckboxRow(!0)});break}case"check-reverse":{const a=new Set(C.tableSelection.map(p=>p.id));console.log(a),C.tableList.forEach(p=>{var u;(u=L.value)==null||u.setCheckboxRow(p,!a.has(p.id))});break}case"cancel-check":{(n=L.value)==null||n.clearCheckboxRow();break}case"area-check":{const a=await De.prompt(`请输入需要选中的商品(用-隔开),留空表示起始值或末尾值
(例：20-30 ; -30 ; 20- ; -)`,"区域选择",{showCancelButton:!0,inputPlaceholder:"请输入范围"}),{value:p}=a;let[u,w]=p.split("-").map($=>Number($.trim()));if(u=u-1<0?0:u-1,w=w||C.tableList.length,u>w)return ye.warning({message:"最小值大于最大值",grouping:!0});await W("cancel-check"),C.tableList.slice(u,w).forEach($=>{var M;(M=L.value)==null||M.setCheckboxRow($,!0)});break}case"id-check":{const a=await De.prompt("请输入需要选中的商品id(多个用,逗号隔开)","id选择",{showCancelButton:!0,inputPlaceholder:"请输入id"}),{value:p}=a,u=new Set(p.split(/[,，]/).filter(w=>w).map(w=>w.trim()));C.tableList.forEach(w=>{var $;($=L.value)==null||$.setCheckboxRow(w,u.has(String(w.id)))});break}}C.tableSelection=((r=L.value)==null?void 0:r.getCheckboxRecords())||[]}oe.getAnySetting("start_site").then(l=>{l&&(f.start_site=l)}),ot(()=>f.start_site,l=>{oe.setAnySetting("start_site",l)});const E=Ae({success:0,max:0,get_group_id_false:0,current:0,activityReject:0});let q=new Set;async function ee(l,n){if(await Ve(2e3),!q.size)return;const r=[...q.values()];q.clear(),c({msg:`开始尝试取消${r.length}个未成功取消活动的商品`});const p=(await ts(l,r)).result.result.filter(u=>u.activity_id==Number(n));if(!p.length){c({msg:"没有获取到活动列表,请前往pdd后台检查并取消活动",type:"danger"});return}p.forEach(u=>{P(l,u.activity_goods_id,u.goods_id,!1).catch(w=>{c({msg:"取消活动失败,请前往PDD后台营销活动管理查看并取消",type:"danger"})})})}const ne={reapeat:{},groupId:{}};async function de(){if(!Zt())return;ne.reapeat={},ne.groupId={};const l=[...C.tableSelection],n=l.length;if(!l.length)return ye.warning({message:"请选择商品",grouping:!0});const{activity:r,activity_diy:a,goodsBatch:p=1}=f;let u=r==="other"?a:r;if(!u)return ye.warning({message:"请选择活动",grouping:!0});const{type:w}=oe.address;if(w==="diy"){const s=await Ze();if(!(s&&s.id))return ye.warning({message:"检测到自定义地址列表为空,请确保有发货地址",grouping:!0})}const $=await Se(u),M=[];for(;l.length;)M.push(l.splice(0,1));g.start=!0,g.stopFlag=!1,C.logList=[],E.current=0,E.get_group_id_false=0,E.success=0,E.max=n,E.activityReject=0,J();async function K(){if(g.stopFlag)return Promise.reject("stop")}async function J(s){g.stopFlag&&(M.length=0);const t=M.shift();if(!t){ye.success({message:"任务结束",grouping:!0}),g.start=!1,g.stopFlag=!1,ee($,u);return}E.current+=t.length;const i={rows:t.map(y=>({row:y,goods_id:y.id,group_id:0,account:"",sku_select_id:y._skuSelect,skuList:[],num:le(u)})),activity_id:u,mall:$};try{await K(),await _e(i),await K(),await Ve(5e3),await K(),await te(i),await K(),await re(i),await K(),await Ce(i),await K(),await B(i),await K(),await k(i),await z(i),await I(i,y=>M.push([y.row]))}catch(y){console.log("error",y)}J()}}function le(l){const{num_appoint:n,num_type:r,num_times:a=3,num_range:p}=f;let u=1;switch(r){case"appoint":{u=n||1;break}case"random":{u=Be(Math.pow(10,a-1),Math.pow(10,a));break}case"random-range":{const{v1:w,v2:$}=p;u=Be(w,$);break}}return u<=0&&(u=1),l=="21365"&&(u>300||u<100)&&(r==="appoint"?(u=u<100?100:300,c({msg:`当前活动只能下单100-300,已自动修正到${u}`,type:"warning"})):(u=Be(100,300),c({msg:`当前活动只能下单100-300,已自动修正到${u}`,type:"warning"}))),u}async function re(l){const n=Ke();if(l.rows.forEach(r=>{const a=n.getAccount(f.start_site);if(!a.status){c({msg:"分配小号出错："+a.msg},r.row);return}r.account=a.data,f.start_site=a.ind+1}),l.rows=l.rows.filter(r=>r.account),!l.rows.length)return c({msg:"没有商品通过小号分配,已跳过",type:"warning"}),Promise.reject()}async function _e(l){const n=[];if(await Promise.allSettled(l.rows.map(async r=>{await fe(r,l),n.push(r)})),l.rows=n,!n.length)return c({msg:"没有商品通过库存操作,已跳过",type:"warning"}),Promise.reject()}async function fe(l,n){let r=l.num;const a=n.mall,{recoverQuantity:p}=f;if(p=="2"){const w=(await qt(a,l.goods_id)).result.find($=>$.skuId==$._skuSelect);w!=null&&w.skuQuantity&&w.skuQuantity-10<r&&(r=w.skuQuantity-10,c({msg:"当前商品库存不足,已自动修正购买数量,",type:"warning"},l.row),l.num=r)}else f.recoverQuantity=="1"&&(await es([{goodsId:l.goods_id,quantity:r,skuId:l.sku_select_id}],a),c({msg:"自动加库存成功",type:"success"},l.row))}async function te(l){if(await Fe(l.rows,{batch:1,request:async([n])=>{const r=ne.groupId[n.goods_id];if(r){c({msg:`${n.row.goods_name},获取拼团id成功(缓存),${r}`},n.row),n.group_id=r;return}const a=await ae(n,l.mall);n.group_id=Number(a),ne.groupId[n.goods_id]=n.group_id}}),l.rows=l.rows.filter(n=>n.group_id),!l.rows.length)return c({msg:"当前次序没有商品获取到拼团ID,将跳过",type:"danger"}),Promise.reject()}async function ae(l,n){return new Promise(async(r,a)=>{c({msg:`${l.row.goods_name},正在获取拼团id`},l.row);const p=async()=>{const $=await Qt({mallId:n.mallId,goods_id:l.goods_id});return $.code?c({msg:`${l.row.goods_name},自动供货失败：${$.msg}`,type:"danger"},l.row):c({msg:`${l.row.goods_name},自动供货成功`,type:"success"},l.row),$};let u=!1;await Ve(2e3);const w=($=10)=>{Pe(async()=>Kt({goods_id:l.goods_id,sku_id:l.sku_select_id},{showErrorMsg:!1}),$).then(M=>{c({msg:`${l.row.goods_name},获取拼团id成功:${M.data}`,type:"success"},l.row),r(M.data)}).catch(async M=>{if(!u&&(u=!0,!(await p()).code)){await Ve(5e3),w();return}a(),E.get_group_id_false++,c({msg:`${l.row.goods_name},获取拼团id失败:${M.msg}`,type:"danger"},l.row)})};w(3)})}async function Ce(l){const{rows:n}=l;if(c({msg:"正在处理下单地址"}),await Promise.allSettled(n.map(async r=>{await Y(r)})),l.rows=l.rows.filter(r=>r.addressData),!l.rows.length)return c({msg:"没有商品获取到地址,已跳过",type:"warning"}),Promise.reject()}async function Y(l,n){const r=ze(),{nameCode_active:a,nameCode_position:p,nameCode_str:u,addr_active:w,addr_position:$,addr_str:M,filterStr:K,type:J,appoint_address:s}=r.address;let t={appoint_address:s||void 0};if(J=="diy"){const i=await Ze();Reflect.deleteProperty(i,"id"),t=i}return Ut({account:l.account,...t},{showErrorMsg:!1}).then(i=>{t={...t,...i.data,filter_address:K.replace("，",","),address_cipher:w?M:void 0,address_site:w?$:void 0,name_cipher:a?u:void 0,name_site:a?p:void 0},l.addressData=t}).catch(i=>(l.addressData=void 0,Ke().recover(l.account),c({msg:"添加地址出错:"+i.msg,type:"danger"},l.row),Promise.reject()))}async function k(l){await b(l);const{mall:n,activity_id:r,rows:a}=l;if(await Promise.allSettled(a.map(async w=>{const{result:$}=await qt(n,w.goods_id);w.skuList=$.filter(M=>M.is_onsale)})),l.rows=a.filter(w=>w.skuList.length),!l.rows.length){c({msg:"当前次序没有商品没有成功获取到sku列表的商品,将跳过",type:"danger"});return}let p="";l.activity_id=="21365"?p=d(l,"clear"):await d(l,"clear");const u=await As(n,r,l.rows.map(w=>({goods_id:w.goods_id,create_discount_vo:{hit_multi_percent:null,multi_percent_discount:null},create_material_vo:{create_goods_image_vo:{},create_goods_title_vo:{}},view_confirmed_goods_protocol_vo:{confirm_copy_goods_for_customer_num:!1,confirm_hit_high_commission:!1},create_finance_cost_vo:{},create_goods_coupon_vo:{},create_activity_price_vo:{create_time_vo:{},create_quantity_vo:{},create_sku_vos:w.row.sku_list.map(M=>{const K=Math.round(f.activityPrice*100),J={sku_id:M.skuId,sku_activity_price:K,sku_front_ctx_vo:{algorithm_decorate_sku_price:null,filling_sku_price:381,filling_sku_price_gear_rule:1001,gear_rule:23,require_price:381,tags:null}};return r=="21365"&&Reflect.deleteProperty(J,"sku_front_ctx_vo"),J}),create_traffic_protect_vo:{max_cut_price_amount:19}}})));if(u.success){const w=u.result.enroll_fail_goods_list,$=u.result.enroll_success_goods_list,M=new Map;if(l.rows.forEach(K=>{M.set(Number(K.goods_id),K)}),w.forEach(K=>{const J=M.get(Number(K.goods_id));J&&c({msg:`${J==null?void 0:J.row.goods_name}参与活动失败：${K.error_msg}`,type:"danger"},J.row)}),$.forEach(K=>{const J=M.get(Number(K.goods_id));J&&(c({msg:`${J==null?void 0:J.row.goods_name}参与活动成功`,type:"success"},J.row),J.activity_goods_id=K.activity_goods_id,J.isActivitySuccess=!0)}),!l.rows.some(K=>K.isActivitySuccess)){c({msg:"没有商品成功参加活动",type:"warning"});return}}else c({msg:"参与活动失败-"+(u.error_msg||u.errorMsg||""),type:"danger"});await p}async function z(l){const{mall:n}=l;l.rows.forEach(r=>{r.isActivitySuccess&&!r.activity_goods_id&&(c({msg:"成功参与活动但未获取到活动管理ID",type:"danger"},r.row),d(l,"recover",r.goods_id),q.add(r.goods_id)),r.isActivitySuccess&&!r.isClearStockSuccess?(c({msg:"成功参与活动但未成功清除sku",type:"danger"},r.row),P(n,r.activity_goods_id,r.goods_id)):!r.isActivitySuccess&&r.isClearStockSuccess&&(c({msg:"成功清除sku但未成功参与活动",type:"danger"},r.row),d(l,"recover",r.goods_id))}),console.log(l),l.rows=l.rows.filter(r=>r.isActivitySuccess&&r.activity_goods_id&&r.isClearStockSuccess),await Promise.allSettled(l.rows.map(async r=>{if(r.isActivitySuccess)return Pe(async a=>{var w,$;const p=await js(n,Number(r.activity_goods_id),r.goods_id);if(console.log("activityDetails",a,p),c({msg:`${r.row.goods_name}获取活动信息第${a}次,`},r.row),a>=60)return p;const u=((w=p.result.resource_price)==null?void 0:w.activity_price_detail_id)||(($=p.result.big_sale_price)==null?void 0:$.activity_price_detail_id);return u?(r.activity_price_detail_id=u,p):p.result.status==104?p:Promise.reject(p)},60,0).then(async a=>{var p,u,w,$;r.activity_price_detail_id||(r.activity_price_detail_id=((p=a.result.resource_price)==null?void 0:p.activity_price_detail_id)||((u=a.result.big_sale_price)==null?void 0:u.activity_price_detail_id)),r.activity_price_detail_id||(a.result.status==104?(E.activityReject++,c({msg:r.row.goods_name+":活动被驳回:"+(($=(w=a.result.audit_list)==null?void 0:w[0])==null?void 0:$.action_desc),type:"danger"},r.row)):(c({msg:"获取活动购买id失败",type:"danger"},r.row),P(n,r.activity_goods_id,r.goods_id)),d(l,"recover",r.goods_id))}).catch(a=>{c({msg:"-获取活动详情失败",type:"danger"},r.row),P(n,r.activity_goods_id,r.goods_id),c({msg:String(a),type:"danger"},r.row)})}))}async function b(l){const{mall:n,activity_id:r,rows:a}=l,u=(await ts(n,a.map(w=>w.goods_id))).result.result.filter(w=>w.activity_id==Number(r));u.length&&(c({msg:"检测到当前商品已经参加活动，将先取消活动",type:"success"}),await Promise.allSettled(u.map(async w=>{await P(n,w.activity_goods_id,w.goods_id)})))}async function d(l,n,r){if(!T.value)return n==="clear"&&l.rows.forEach(w=>{w.isClearStockSuccess=!0}),{success:!0};c({msg:`正在执行${n==="clear"?"清除库存":"恢复库存"}操作`});const{rows:a,mall:p}=l;await Promise.allSettled(a.filter(w=>r?w.goods_id===r:!0).map(async w=>{const $=await es(w.skuList.map(M=>{const K={goodsId:M.goods_id,quantity:M.quantity,skuId:M.sku_id};return n==="clear"&&(K.quantity=-Number(K.quantity)),K}),p);if($.success)n==="recover"||(w.isClearStockSuccess=!0);else switch(n){case"clear":{c({msg:"清除库存失败:"+($.errorMsg||""),type:"danger"},w.row),w.skuList=[],w.isClearStockSuccess=!1;break}case"recover":c({msg:"恢复库存失败:"+($.errorMsg||""),type:"danger"},w.row)}}));let u=15;n==="recover"&&(u=0),await Ve(u*1e3)}async function P(l,n,r,a=!0){c({msg:`准备取消活动:${n}`});const p=await Ts(l,n,r);if(p.success)c({msg:`${n},取消活动成功`,type:"success"});else return a&&q.add(r),c({msg:`${n},取消活动失败：${p.error_msg||p.errorMsg||""}`,type:"danger"}),Promise.reject()}async function I(l,n){console.log(l);const{mall:r,rows:a}=l,p=a.filter(u=>u.activity_price_detail_id);if(g.stopFlag)return p.forEach(u=>{u.activity_goods_id&&P(r,u.activity_goods_id,u.goods_id)}),Promise.reject("停止执行");if(!p.length)return Promise.reject("没有参加活动的商品");await new Promise(u=>{const w=async()=>{const $=p.shift();if(!$){u(!0);return}setTimeout(()=>{d(l,"recover",$.goods_id)},10);const M=$.row.sku_list.find(J=>J.skuId==$.sku_select_id),K={account:$.account,sku:$.sku_select_id,sku_spec:(M==null?void 0:M.spec)||$.row.goods_name,shop_id:r.mallId,shop_name:r.mallName,goods_id:$.goods_id,num:$.num,activity_id:$.activity_price_detail_id,goods_name:$.row.goods_name,group_id:$.group_id.toString(),use_coupon:!1,type:"activity",...$.addressData};return Pe(async()=>g.stopFlag?Promise.reject({msg:"停止执行"}):At(K,{showErrorMsg:!1}).then(J=>{E.success++,c({msg:`${K.goods_name}:下单成功。订单号:${J.data.order_sn}`,type:"success"},$.row),J.data.shop_id=J.data.shop_id||r.mallId,A(J.data,0)}),3).catch(J=>{c({msg:`${K.goods_name}:下单失败:${J.msg}`,type:"danger"},$.row);const s=ne.reapeat[K.goods_id];(!s||s<3)&&(n($),ne.reapeat[K.goods_id]=s?s+1:1)}).finally(async()=>{await Promise.allSettled([P(r,$.activity_goods_id,$.goods_id)]),w()})};w()})}async function A(l,n){const{type:r,order_amount:a,shop_id:p,shop_name:u,order_sn:w,id:$}=l;let M;r==="pifa"?M=Number(a-.01*100):M=Number(a-(a+n*100)*.1),c({msg:`订单${w},将执行改价操作`}),await Ve(3e3),ps({store_id:p,shop_name:u,goodsDiscount:M,order_sn:w}).then(K=>{c({type:"success",msg:`订单${w},改价成功`}),ms({ids:w},{showErrorMsg:!1}).catch(J=>{console.log(J)}),H(w)}).catch(K=>{c({type:"danger",msg:`订单${w},改价失败:${K.msg}`});const{chance:J}=oe.pay;J==="immediate"&&c({msg:"改价失败不会启动自动支付",type:"warning"})})}async function B(l){if(!f.isCancelActivity)return;const{mall:n,rows:r}=l,a=await zt(n,{goods_id:r.map(u=>u.goods_id)});c({msg:"开始检测商品是否在参与限时限量购"});const p=new Map;l.rows.forEach(u=>{p.set(u.goods_id,u)});try{if(a.success){const u=a.result.marketing_activity_list;await Promise.allSettled(u.map(async w=>{const{goods_id:$,activity_type:M,activity_id:K,activity_name:J}=w;if((M==3||M==12)&&p.get($)){c({msg:`检测到参与${J},准备取消`,type:"warning"},w.row);const t=await _s(n,{goods_id:$,activity_id:K});t.success?c({msg:`${J}结束成功`,type:"success"},w.row):c({msg:`${J}结束失败,${t.error_msg}`,type:"danger"},w.row)}}))}else c({msg:`获取限时限量购活动列表失败:${a.error_msg}`,type:"danger"})}catch{c({msg:"限时限量购意外的错误",type:"danger"})}}return(l,n)=>{const r=pt,a=_t,p=Pt,u=ft,w=Ct,$=rs,M=Et,K=uo,J=Tt,s=xt,t=nt,i=We("VxeColumn"),y=We("VxeTable"),j=Ls,U=gt,x=us;return se(),we("div",Ao,[e(x,{size:"default"},{default:o(()=>[_("div",jo,[_("div",To,[Eo,_("div",Ro,[e(w,{"label-position":"top",size:"small"},{default:o(()=>[e(p,{label:"店铺:"},{default:o(()=>[e(a,{modelValue:f.mallId,"onUpdate:modelValue":n[0]||(n[0]=m=>f.mallId=m)},{default:o(()=>[(se(!0),we(je,null,Te(G(X).tableList,m=>(se(),xe(r,{label:m.mallName,value:m.mallId},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"小号位置:"},{default:o(()=>[e(u,{modelValue:f.start_site,"onUpdate:modelValue":n[1]||(n[1]=m=>f.start_site=m),controls:!1,style:{"flex-grow":"1"}},null,8,["modelValue"])]),_:1}),e(p,{label:"收货信息:"},{default:o(()=>[V(" 跟随"),Go,V("的配置 ")]),_:1})]),_:1})])]),_("div",Mo,[No,_("div",Fo,[e(w,{"label-position":"top",inline:!0,size:"small"},{default:o(()=>[e(p,{label:"下单模式"},{default:o(()=>[e(J,{modelValue:f.activity,"onUpdate:modelValue":n[3]||(n[3]=m=>f.activity=m)},{default:o(()=>[e(K,{label:"20981"},{default:o(()=>[e(M,null,{default:o(()=>[zo,e($,{onClick:n[2]||(n[2]=m=>Se("20981")),underline:!1,type:"primary"},{default:o(()=>[V(" 检查")]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"购买数量"},{default:o(()=>[e(J,{modelValue:f.num_type,"onUpdate:modelValue":n[4]||(n[4]=m=>f.num_type=m)},{default:o(()=>[e(K,{label:"random"},{default:o(()=>[V("随机购买")]),_:1}),e(K,{label:"random-range"},{default:o(()=>[V("范围随机")]),_:1}),e(K,{label:"appoint"},{default:o(()=>[V("指定数量")]),_:1})]),_:1},8,["modelValue"]),Me(e(M,null,{default:o(()=>[e(u,{style:{width:"70px"},controls:!1,modelValue:f.num_appoint,"onUpdate:modelValue":n[5]||(n[5]=m=>f.num_appoint=m),precision:0,min:10},null,8,["modelValue"]),Oo]),_:1},512),[[Ne,f.num_type==="appoint"]]),Me(e(M,null,{default:o(()=>[e(u,{style:{width:"58px"},controls:!1,modelValue:f.num_times,"onUpdate:modelValue":n[6]||(n[6]=m=>f.num_times=m),precision:0,min:1,max:5},null,8,["modelValue"]),Bo]),_:1},512),[[Ne,f.num_type==="random"]]),Me(e(M,null,{default:o(()=>[e(u,{style:{width:"58px"},controls:!1,modelValue:f.num_range.v1,"onUpdate:modelValue":n[7]||(n[7]=m=>f.num_range.v1=m),precision:0,min:1},null,8,["modelValue"]),Ko,e(u,{style:{width:"58px"},controls:!1,modelValue:f.num_range.v2,"onUpdate:modelValue":n[8]||(n[8]=m=>f.num_range.v2=m),precision:0,min:1},null,8,["modelValue"])]),_:1},512),[[Ne,f.num_type==="random-range"]])]),_:1}),e(p,{label:"库存设置"},{default:o(()=>[e(J,{modelValue:f.recoverQuantity,"onUpdate:modelValue":n[9]||(n[9]=m=>f.recoverQuantity=m)},{default:o(()=>[e(K,{label:"1"},{default:o(()=>[V("自动加库存(不影响原有库存)")]),_:1}),e(K,{label:"2"},{default:o(()=>[V("按现有库存(会影响原有库存)")]),_:1}),e(K,{label:"3",class:"no-visible",disabled:!0},{default:o(()=>[V("按现有库存(会影响原有库存)")]),_:1})]),_:1},8,["modelValue"]),Qo,_("p",null,[e(u,{precision:2,min:.01,controls:!1,modelValue:f.activityPrice,"onUpdate:modelValue":n[10]||(n[10]=m=>f.activityPrice=m)},null,8,["min","modelValue"])])]),_:1})]),_:1})])]),_("div",Ho,[_("header",null,[Yo,e(s,{label:"突破风控",modelValue:G(T),"onUpdate:modelValue":n[11]||(n[11]=m=>mt(T)?T.value=m:null)},null,8,["modelValue"]),e(s,{title:"开启后会自动检测并关闭商品是否参与限时限量购活动",label:"关闭其他活动",modelValue:f.isCancelActivity,"onUpdate:modelValue":n[12]||(n[12]=m=>f.isCancelActivity=m)},null,8,["modelValue"])]),_("div",Do,[_("p",null,[Wo,e(s,{modelValue:f.lowPriceSku,"onUpdate:modelValue":n[13]||(n[13]=m=>f.lowPriceSku=m),label:"获取最低价SKU"},null,8,["modelValue"])]),_("p",null,[e(t,{loading:g.getGoodsList,type:"success",onClick:ke},{default:o(()=>[V("获取商品")]),_:1},8,["loading"])]),_("p",null,[e(t,{type:"primary",disabled:!C.tableSelection.length,onClick:de,loading:g.start},{default:o(()=>[V("开始")]),_:1},8,["disabled","loading"])]),_("p",null,[e(t,{type:"primary",onClick:Oe,disabled:g.stopFlag},{default:o(()=>[V("停止")]),_:1},8,["disabled"])])])])]),_("div",Jo,[e(y,{"scroll-y":{enabled:!0},loading:g.getGoodsList,"checkbox-config":{checkField:"checkField"},onCheckboxChange:n[14]||(n[14]=()=>{var m;C.tableSelection=((m=L.value)==null?void 0:m.getCheckboxRecords())||[]}),onCheckboxAll:n[15]||(n[15]=()=>{var m;C.tableSelection=((m=L.value)==null?void 0:m.getCheckboxRecords())||[]}),ref_key:"vxeTableEl",ref:L,data:C.tableList,height:310,stripe:!0,"column-config":{resizable:!0},"show-overflow":"title"},{default:o(()=>[e(i,{type:"checkbox",width:50}),e(i,{type:"seq",title:"序号",width:50}),e(i,{title:"商品ID",field:"id",width:130}),e(i,{title:"商品名称",field:"goods_name"}),e(i,{title:"SKU"},{default:o(m=>[e(a,{modelValue:m.row._skuSelect,"onUpdate:modelValue":R=>m.row._skuSelect=R},{default:o(()=>[(se(!0),we(je,null,Te(m.row.sku_list,R=>(se(),xe(r,{label:R.spec||m.row.goods_name,value:R.skuId},null,8,["label","value"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(i,{title:"SKUID",width:140},{default:o(m=>{var R;return[V(ge((R=m.row.sku_list.find(N=>N.skuId==m.row._skuSelect))==null?void 0:R.skuId),1)]}),_:1}),e(i,{title:"SKU拼团价格"},{default:o(m=>{var R;return[V(ge(((R=m.row.sku_list.find(N=>N.skuId==m.row._skuSelect))==null?void 0:R.groupPrice)/100),1)]}),_:1}),e(i,{title:"库存"},{default:o(m=>{var R;return[V(ge((R=m.row.sku_list.find(N=>N.skuId==m.row._skuSelect))==null?void 0:R.skuQuantity),1)]}),_:1}),e(i,{title:"商品销量",field:"sold_quantity",sortable:"",width:100}),e(i,{title:"状态",field:"",width:150},{default:o(m=>{var R,N;return[_("span",{class:St((R=h.value.get(m.row.id))==null?void 0:R.type)},ge((N=h.value.get(m.row.id))==null?void 0:N.msg),3)]}),_:1})]),_:1},8,["loading","data"]),_("footer",Xo,[_("p",null,[_("span",null,[V(" 运行状态 ： "),!g.start&&E.max?(se(),xe(j,{key:0,type:"success"},{default:o(()=>[V("任务已完成")]),_:1})):(se(),we("span",Zo,[_("span",qo,ge(`商品总数：${E.max}`),1),_("span",en,ge(`正在运行第${E.current}次`),1)]))]),e(M,null,{default:o(()=>[e($,{underline:!1,onClick:n[16]||(n[16]=m=>W("check-all"))},{default:o(()=>[V("全选")]),_:1}),e($,{underline:!1,onClick:n[17]||(n[17]=m=>W("check-reverse"))},{default:o(()=>[V("反选")]),_:1}),e($,{underline:!1,onClick:n[18]||(n[18]=m=>W("area-check"))},{default:o(()=>[V("区域选择")]),_:1}),e($,{underline:!1,onClick:n[19]||(n[19]=m=>W("id-check"))},{default:o(()=>[V("商品id选择")]),_:1}),e($,{underline:!1,onClick:n[20]||(n[20]=m=>W("cancel-check"))},{default:o(()=>[V("取消选择")]),_:1}),e(U,{onKeyup:n[21]||(n[21]=ds(m=>ke(),["enter"])),placeholder:"ID搜索,多个逗号隔开",modelValue:C.goods_id,"onUpdate:modelValue":n[22]||(n[22]=m=>C.goods_id=m)},null,8,["modelValue"]),e($,{underline:!1,onClick:Z},{default:o(()=>[V("大量输入")]),_:1})]),_:1})]),_("p",null,[_("div",null,[V(" 已完成： "),_("span",tn,ge(E.success),1),V(", 获取拼团ID失败 ："),_("span",sn,ge(E.get_group_id_false),1),V(", 商品无法报活动： "),_("span",on,ge(E.activityReject),1)]),e(Lt,{onSizeChange:n[23]||(n[23]=()=>{v.page=1,ke()}),onCurrentChange:ke,total:v.total,"page-sizes":v.pageSizes,"current-page":v.page,"onUpdate:current-page":n[24]||(n[24]=m=>v.page=m),"page-size":v.limit,"onUpdate:page-size":n[25]||(n[25]=m=>v.limit=m)},null,8,["total","page-sizes","current-page","page-size"])])])]),e(yt,{list:C.logList,"onUpdate:list":n[26]||(n[26]=m=>C.logList=m),height:"90px"},null,8,["list"])]),_:1})])}}}),an=it(nn,[["__scopeId","data-v-f4a811a9"]]);function ln(ie,oe,X="img"){return new Promise(f=>{let T=document.createElement("canvas");if(X=="img"){const C=new Image;C.crossOrigin="Anonymous",C.onload=function(){T.width=C.width,T.height=C.height,T.getContext("2d").drawImage(C,0,0);const v=Es.extname(ie).replace(".",""),pe=T.toDataURL("image/"+v,oe);f({base64:pe}),T=null},C.src=ss(ie)}else if(X=="video"){const C=document.createElement("video");document.body.appendChild(C),C.crossOrigin="Anonymous",C.currentTime=1,C.oncanplay=function(){T.width=C.clientWidth,T.height=C.clientHeight,T.getContext("2d").drawImage(C,0,0);const v=T.toDataURL("image/png",oe);f({base64:v,duration:C.duration}),document.body.removeChild(C),T=null},C.src=ss(ie)}else f({base64:""})})}const he=ie=>(at("data-v-2028af49"),ie=ie(),lt(),ie),rn={class:"change-sales c-s-gift no-padding"},dn={class:"settings"},un={class:"item account"},cn=he(()=>_("header",null,[_("h3",null,"1.账号配置")],-1)),_n={class:"content"},pn={class:"item buy"},mn=he(()=>_("header",null,[_("h3",null,"2.购买配置")],-1)),gn={class:"content"},fn=he(()=>_("span",null,"件",-1)),yn=he(()=>_("span",null,"位数",-1)),wn=he(()=>_("span",null,"-",-1)),vn=he(()=>_("span",null,"同步老商品信息",-1)),hn=he(()=>_("p",null,"将炮灰链接信息同步至老商品信息",-1)),kn=he(()=>_("p",null,"只有旗舰店下单方式才可同步",-1)),bn=he(()=>_("div",null,"每单最大销量",-1)),Sn={class:"m-t-10"},$n={style:{"margin-top":"10px"}},In=he(()=>_("p",null,"默认：旗舰店为选中商品分类，其他为默认下的第一个分类",-1)),Vn=he(()=>_("p",null,"如果提示【商品发布失败】【商品检测失败】【商品价格检测失败】等商品操作相关的错误，请切换到其他或默认分类",-1)),Cn={class:"between-flex",style:{margin:"10px 0"}},Pn=he(()=>_("p",null,"随机生成：系统自动在店铺生成炮灰链接",-1)),xn=he(()=>_("p",null,"指定炮灰：用户自行选择店铺【已上架】的商品作为炮灰链接",-1)),Ln=he(()=>_("p",null,"注意：请不要将正常出售的商品作为炮灰链接。",-1)),Un=he(()=>_("p",null,"炮灰商品要求:商品价格大于4000",-1)),An=he(()=>_("p",null,"sku数量为1",-1)),jn=he(()=>_("p",null,"启动后每个订单sku价格5.5元,单个订单最多拍359销量",-1)),Tn=he(()=>_("p",null,"如果销量需求太高会造成下单量过多",-1)),En={class:"item ctrl"},Rn=he(()=>_("header",null,[_("h3",null,"3.控制台")],-1)),Gn={class:"content"},Mn=he(()=>_("p",null,null,-1)),Nn={class:"table-container"},Fn={class:"table-footer"},zn=he(()=>_("span",null,null,-1)),On=he(()=>_("div",null,null,-1)),Bn={class:"contaier",style:{height:"520px","overflow-y":"auto"}},Kn={class:"card",style:{"margin-bottom":"12px",border:"var(--el-border)","border-radius":"5px",padding:"12px"}},Qn={class:"msg",style:{padding:"12px"}},Hn=he(()=>_("p",null,[V("请选择 "),_("strong",null,"【收货地址】"),V("所在省，【没选择】的地区将无法下单")],-1)),Yn=he(()=>_("p",null,"注意：如果是自定义地址下单，请确保你的自定义地址所在省份被勾选",-1)),Dn=he(()=>_("p",null,"注意：【全选和不选】都相当于【不开启】此功能",-1)),Wn=he(()=>_("p",null,"注意：开启此功能后，下单设置中的【过滤省份】【指定省份】会【失效】，指定省份以此处勾选的为主",-1)),Jn=he(()=>_("p",{class:"success"},"请选择 收货地址 所在省份",-1)),Xn=et({__name:"CSGift",setup(ie){const oe=ct(),X=Ie(new Map);function f(){h.tempLoopType="random",h.isTempLoop=!1,h.appointTempGoodsId="",Ce.value=[]}const T=()=>{const{mallId:s}=h,t=oe.tableList.find(i=>i.mallId===s);if(t){const i=/(旗舰|专卖|专营)/.test(t.mallName);h.isFlagshipStore=i,i||(h.recover=!1)}f()};async function C(){const s=L.selection.map(y=>y.id);if(!s.length){g({msg:"没有可下架商品",type:"danger"});return}const t=oe.tableList.find(y=>y.mallId===h.mallId);if(!t){g({msg:"没有找到店铺信息",type:"danger"});return}g({msg:"执行批量下架商品",type:"info"}),(await Ms(t,s)).success?g({msg:"下架成功",type:"success"}):g({msg:"下架失败",type:"danger"}),E.page=1,E.limit=100,await Ve(1500),le()}const Z=Ie(!1),v=qe({active:!1,appointAddrList:[]},"cs-gift-defenseConfig");function pe(s){switch(s){case"all":v.value.appointAddrList=st.map(i=>i.id);break;case"reverse":const t=new Set([...v.value.appointAddrList]);v.value.appointAddrList=[],st.forEach(i=>{t.has(i.id)||v.value.appointAddrList.push(i.id)});break;case"clear":v.value.appointAddrList=[]}}const h=Ae({goodsBatch:1,num_type:"appoint",randomRage:{v1:101,v2:2e3},num_times:3,num_appoint:7663,recover:!1,isFlagshipStore:!1,useDefaultImage:!0,isTempLoop:!1,tempLoopDelay:100,tempLoopType:"random",appointTempGoodsId:"",auto_increase_price:!1,order_type:"gift",line2:!1,order_per_max_count:9505,cat_id:8932});ot(()=>h.isFlagshipStore,s=>{s?(h.order_per_max_count>9505&&(h.order_per_max_count=9505),h.cat_id==8932&&(h.cat_id=0)):(h.order_per_max_count=200001,h.cat_id=8932)});const c=jt(()=>{const{appointTempGoodsId:s}=h,t=s.split(/[,，\n]/),i=new Set;return t.forEach(y=>{if(y.length<6)return;const j=Number(y.trim());j&&Number.isInteger(j)&&i.add(j)}),[...i]}),ce=Ae({logList:[],goods_id:""}),g=(s,t)=>{if(ce.logList.push({date:Date.now(),msg:"",...s}),t){const i=s.msg||"";X.value.has(t.id)?(X.value.get(t.id).msg=i,X.value.get(t.id).type=s.type):X.value.set(t.id,{msg:i,type:s.type})}},Se=g,ke=qe(1,"start_site"),Oe=Ie(),{tableProps:H,tableState:L,tableEvents:W,pagination:E,tableAction:q}=Xt(310,{id:"change-sales-gift",columnConfig:{resizable:!0}},{tableRef:Oe,pagination:{pageSizes:[100,1e3,2e3]}});async function ee(){const{mallId:s}=h,t=oe.tableList.find(j=>j.mallId==s);if(!t)return ye.warning({message:"请选择店铺",grouping:!0});H.loading=!0;const i=[...new Set(h.isFlagshipStore?Mt.flagshipStore:Mt.common)],y=[];for(let j=0;j<i.length;j++){const U=i[j];let x=await $t({request:async m=>{const R=await bt(t,m,void 0,U);if(!R.success)return Promise.reject();const{goods_list:N,total:O}=R.result;return N.forEach(D=>{D.sku_list=D.sku_list.filter(Q=>Q.isOnsale),D.sku_list.length&&(D._skuSelect=D.sku_list.sort((Q,F)=>Q.groupPrice-F.groupPrice)[0].skuId,D._sku_list=[D.sku_list[0]])}),{list:N,total:O}}});y.push(...x)}E.total=y.length,E.page=1,H.data=y,H.loading=!1}const ne=Ie({show:!1,errors:[]});async function de(s,t){if(!c.value.length)return[];te.start=!0;const i=await $t({request:async U=>{const x=await bt(s,U,c.value.join(","));if(!x.success)return Promise.reject();const{goods_list:m,total:R}=x.result;return{list:m,total:R}}});let y=[];const j=i.filter(U=>{let x=!0,m=[];const{sku_list:R=[]}=U;return R.length!==1&&(m.push("当前临时商品的sku数量不为1"),x=!1),(R[0]||{groupPrice:0}).groupPrice<4e3*100&&(m.push("当前临时商品的价格低于4000元"),x=!1),x||y.push({goodsInfo:U,msg:m.join(";")}),x});return y.length&&(ne.value.errors=y,ne.value.show=!0),te.start=!1,g({msg:`检测到${j.length}个符合要求的临时商品,${y.length}个不符合要求的商品`}),j}async function le(s=""){const{mallId:t}=h,i=oe.tableList.find(O=>O.mallId==t);if(!i)return ye.warning({message:"请选择店铺",grouping:!0});if(!oe.checkAvailable(i))return ye.warning({message:"当前店铺已过期，请重新登录",grouping:!0});const{goods_id:y}=ce;y&&(E.page=1),H.loading=!0;const j=Math.round(E.limit/100);let U=(E.page-1)*j,x=1;const m=E.page*j,R=[];let N=0;await new Promise(O=>{function D(){Se({msg:`正在获取第${E.page}-${x}页商品`}),bt(i,U+x,y,s).then(Q=>{if(Q.success){const F=Q.result.goods_list;N+=F.length,F.forEach(ue=>{ue.sku_list=ue.sku_list.filter(me=>me.isOnsale),ue.sku_list.length&&(ue._skuSelect=ue.sku_list.sort((me,ve)=>me.groupPrice-ve.groupPrice)[0].skuId,ue._sku_list=[ue.sku_list[0]],R.push(ue))}),E.total=Q.result.total,Se({msg:`获取商品第${E.page}-${x}页成功`,type:"success"}),Q.result.total>N&&U+x<m?H.loading?(x++,D()):(Se({msg:"停止获取",type:"warning"}),O(!0)):O(!0)}else{let F=Q.errorMsg||Q.error_msg||"";F=="会话已过期"&&(F+=",请重新登录店铺"),Se({msg:"获取失败："+F,type:"danger"}),O(!0)}}).catch(Q=>{Se({msg:"意外的错误,停止获取",type:"danger"}),O(!0)})}D()}),H.data=R,q("clear"),Se({msg:"获取商品列表结束"}),H.loading=!1}async function re(){let s=ce.goods_id;await De({title:"修改商品ID筛选",showCancelButton:!0,message:Ue("div",{},[Ue("p",{class:"m-b-5 primary"},"请输入商品ID,多个用逗号或换行隔开！"),Ue(cs,{modelValue:ce.goods_id,onChange:({value:t})=>{s=t},resize:"none",rows:10,placeholder:"请输入商品ID,多个用逗号或换行隔开！"})])}),ce.goods_id=s.split(/[,，\n]/).filter(t=>t.trim()).join(",")}function _e(s,t){let i={num_per_order:0,price_per_order:0,max_num_per_order:9505};const y=Number(h.order_per_max_count);return h.auto_increase_price||y==359?(i.max_num_per_order=359,i.price_per_order=5.5):y===9505?(i.price_per_order=.21,i.max_num_per_order=9505):y===3851?(i.price_per_order=.51,i.max_num_per_order=3851):y===200001&&(i.price_per_order=.01,i.max_num_per_order=200001),s<=100?i.num_per_order=101:s>=i.max_num_per_order?i.num_per_order=i.max_num_per_order:i.num_per_order=s,i}function fe(s){const{num_appoint:t,num_type:i,num_times:y=3}=h;let j=1;switch(i){case"appoint":{j=t||1;break}case"random":{j=Be(Math.pow(10,y-1),Math.pow(10,y));break}case"random-range":{const{v1:U,v2:x}=h.randomRage;j=Be(U,x);break}}return j<=0&&(j=1),j<101&&(j=101,g({msg:"商品数量不能小于101,已自动调整到101",type:"primary"})),g({msg:`商品数量:${j}`}),j}const te=Ae({start:!1,stop:!1});async function ae(){if(te.stop)return g({msg:"检测到停止执行",type:"warning"}),Promise.reject()}const Ce=Ie([]);function Y(){let{tempLoopDelay:s,isTempLoop:t}=h;if(s=60,!!t)return Ce.value.find(i=>i.lastUsedTime+s*1e3<=Date.now())}let k=!1;async function z(){if(!Zt())return;Ce.value.length&&(g({msg:"清空缓存"}),Ce.value=[]);const{mallId:s,tempLoopType:t}=h,i=oe.tableList.find(m=>m.mallId==s);if(!i){ye.warning({message:"请选择店铺",grouping:!0});return}if(!oe.checkAvailable(i))return ye.warning({message:"当前店铺已过期，请重新登录",grouping:!0});const y=[...L.selection];if(!y.length){ye.warning({message:"请选择商品",grouping:!0});return}ce.logList=[],te.start=!0;const j=await b(i);te.start=!1,h.auto_increase_price&&!k&&(await De({title:"警告",message:Ue("div",{style:"padding:5px"},[Ue("h4","检测到开启规避低价限制"),Ue("p",{class:"danger"},"开启后，每个订单SKU价格为5.5元，如果需购买数量过大，则需要【大量小号次数】"),Ue("p",{calss:"warning"},"当购买数量大于359,则需要 （购买数量÷359）小号次数"),Ue("p","确认继续吗？(点击确定后，本次运行【不再提示】！)")]),showCancelButton:!0}),k=!0);let U=[];if(h.isTempLoop&&h.tempLoopType==="appoint"){if(U=await de(i,{isFlagshipStore:h.isFlagshipStore}),!U.length){ye.warning({message:"检测后没有可用的炮灰商品ID"});return}h.appointTempGoodsId=U.map(m=>m.id).join(",")}X.value.clear();const x=async()=>{if(te.stop){g({msg:"停止执行",type:"warning"}),te.start=!1;return}const m=y.shift();if(!m){Se({msg:"结束执行"}),te.start=!1;return}const R=fe(h.isFlagshipStore);let N={row:m,num:R,..._e(R,h.isFlagshipStore),mall:i,coupon_code:"",isFlagshipStore:h.isFlagshipStore,costTemplateInfo:j};try{if(g({msg:"初始化",row:m}),await P(N),await ae(),await d(N),await ae(),h.isTempLoop&&t==="appoint"){const D=U.shift();if(!D)return g({msg:"没有可用炮灰商品"}),te.start=!1,Promise.reject("");N.goods_id=D.id,N.tempGoodsInfo=D,N.isCacheTempGoods=!0,await ae()}else{const D=Y();D?(g({msg:"从缓存中获取临时数据"}),N.isCacheTempGoods=!0,N.tempGoodsInfo=D.tempGoodsInfo,N.goodsInfo=D.goodsInfo,D.lastUsedTime=Date.now(),N.goods_id=N.goodsInfo.goods_id):(await I(N),await ae(),await A(N),await ae(),console.log("requestData",N),h.recover&&h.isFlagshipStore&&K(N))}g({msg:"准备创建优惠券，大约15秒"}),await Ve(1e4),await ae(),await B(N),await l(N),await r(N),h.recover&&await M(N),x()}catch(D){console.log(D),g({msg:N.row.goods_name+"处理失败",type:"danger"}),x()}N.goods_id&&h.isTempLoop&&h.tempLoopType==="appoint"&&U.push(N.tempGoodsInfo),console.log("useAppointTempGoodsIds",U)};te.stop=!1,te.start=!0,x()}async function b(s){const{active:t,appointAddrList:i}=v.value;if(!t||!i.length||i.length===st.length){g({msg:"未启动防薅羊毛"});return}const y=new Map;for(const U of st)y.set(U.id,U);let j=await new Promise(U=>{let x=1;const m=async()=>{const R=await mo(s,{pageNo:x++});if(R.success){const{list:N,total:O}=R.result,D=N.find(Q=>{const F=Q.dispatchFree.areaList;if(F.length!==i.length)return!1;const ue=new Set([...i]);return F.forEach(({province:me})=>{ue.delete(me)}),!ue.size});if(D)return g({msg:"检测到可用运费模板"+D.costTemplateName}),U(D.costTemplateId);O>=x*10?m():U(void 0)}else U(void 0)};m()});if(!j){const U=await po(s,{dispatchFree:{sfFreeType:0,areaList:i.map(x=>({province:x}))},noShipConfig:st.filter(x=>!i.includes(x.id)).map(x=>({province:x.id,noShipReasonType:2}))});U.success?(j=U.result.costTemplateId,g({msg:"创建运费模板成功",type:"success"})):g({msg:"创建运费模板失败"+Ee(U),type:"danger"})}if(j)return{id:j,province:i.map(U=>{var x;return{id:U,name:((x=y.get(U))==null?void 0:x.regionName)??""}})}}async function d(s){Se({msg:"正在分配小号"},s.row);const t=Ke(),i=s.num;let y=s.max_num_per_order;const j=Math.ceil(i/y),U=[];g({msg:`需要可用小号次数：${j},一单${y},目标${i}`});for(let x=0;x<j;x++){const m=t.getAccount(ke.value-1);if(m.status){U.push(m.data);const R=(m.ind+2)%t.list.length;ke.value=R===0?t.list.length:R}else{g({type:"danger",msg:"获取小号出错"+m.msg},s.row);break}}if(U.length<j)return g({type:"danger",msg:"没有获取到足够小号"}),U.forEach(x=>{t.recover(String(x))}),Promise.reject();s.accounts=U}async function P(s){const{mall:t,row:i}=s;g({type:"info",msg:"正在获取原商品详情"},i);const y=await Ns(t,i.id);return y.success?(s.cloneGoodsDetails=y.result,y.result):(g({msg:"获取商品详情失败："+(y.error_msg||y.errorMsg),type:"danger"},i),Promise.reject())}async function I(s){const{mall:t,isFlagshipStore:i,row:y}=s,j=await Fs(t);if(!j.success)return g({msg:`获取商品ID失败${j.error_msg||""}`,type:"danger"},y),Promise.reject();const{goods_commit_id:U,goods_id:x}=j.result;s.goods_commit_id=U,s.goods_id=x;const{detail_gallery:m,cat_id:R}=s.cloneGoodsDetails,N={};if(s.costTemplateInfo&&(N.cost_template_id=s.costTemplateInfo.id),i){const ve=function(){const be=Mt.flagshipStore;return be[Be(0,be.length)]||be[0]}();N.goods_name=ve,N.goods_desc=ve}let O=h.cat_id;s.price_per_order==.01&&O!=8932&&(g({msg:"当前每单最大销量为200001,仅能使用固定分类《工商注册》,自动调整",type:"warning"}),O=8932);const D={mall:t,goods_commit_id:U,goods_id:x,gallery:m,detailsInfo:s.cloneGoodsDetails,cat_id:O||(i?R:void 0),otherData:N,only_sku_property:!!O||!i,useDefaultImage:h.useDefaultImage};!i&&!h.cat_id&&(D.useDefaultImage=!0),O==8932&&(D.useDefaultImage=!0);const Q=await _o(D);console.log("info",Q,s);const F=await os(t,Q);if(!F.success)return g({msg:`创建商品失败${F.error_msg||""}`,type:"danger"},y),Promise.reject();const ue=await new Promise((ve,be)=>{ns().then(Le=>{const{anti_content1:Ye,anti_content2:Re}=Le.data;ve([Ye,Re])}).catch(Le=>{g({msg:"获取anticontent失败"+Le.msg}),be()})}),me=await as(t,{goods_commit_id:U,goods_id:x},ue);if(console.log("submitRes",me,s),!me.success)return g({msg:`提交商品失败${me.error_msg||""}`,type:"danger"},y),Promise.reject();g({msg:"已成功提交创建商品"+x,type:"success"},y)}async function A(s){const{row:t}=s;g({msg:"准备检测临时商品创建状态"},t),await Ve(5e3);const{goods_id:i,mall:y}=s;await Pe(async j=>{var m;g({msg:`正在检测创建的临时商品状态，第${j}次`},t);const U=await Ot(y,{goods_id_list:[String(i)],pre_sale_type:4});if(console.log("checkTempGoods",U),!U.success)return g({msg:`检测创建的临时商品失败${U.error_msg||""}`,type:"danger"}),Promise.reject(U);if(U&&U.result&&U.result.total){const R=U.result.goods_list[0];return R.sku_list?(s.tempGoodsInfo=R,g({msg:"临时商品已上架",type:"success"},t),U):Promise.reject()}await Ve(1e3);const x=await Bt(y,[String(i)]);return x&&x.success&&((m=x.result)!=null&&m.total)?(g({msg:"检测到临时商品被驳回",type:"danger"},t),!1):(j>=10&&g({msg:"检测创建的临时商品状态超过10次",type:"danger"}),Promise.reject(U))},10,3e3)}async function B(s){const{mall:t,isFlagshipStore:i,row:y,price_per_order:j}=s;function U(Q=Date.now()){const F=new Date(Q);return F.setHours(0),F.setMinutes(0),F.setSeconds(0),F.setMilliseconds(0),F.valueOf()}const x=await go({goods_id:s.goods_id.toString()},t);if(x.success){const Q=x.result.goods_list.find(F=>F.id==s.goods_id);if(Q&&Q.coupon_value_limit){const F=Q.coupon_value_limit[0];if(F){const ue=Math.ceil(F/(j*100));ue>s.num_per_order&&(g({msg:`当前每单购买数量${s.num_per_order},商品立减券不满足最低购买数量${ue}，已自动调整`,type:"warning"},y),s.num_per_order=ue)}}}const m="商品立减券"+Date.now(),R=s.num_per_order*j;Number.isInteger(R)&&s.num_per_order++;let N=Math.floor(R)*100;N>2e5&&(N=2e5);const O=[{goods_id:s.goods_id,batch_desc:m,batch_start_time:U(),batch_end_time:U(Date.now()+3600*1e3*48)-1,discount:N,init_quantity:1e5,user_limit:10}],D=await ws(t,O);if(console.log("createCoupon",D,O),D.success)if(D.result.has_error_msg){let Q=!1;if(D.result.error_msg_list.forEach(F=>{g({msg:`创建优惠券失败:${F.error_msg}`,type:"danger"},y),Q=!0}),Q)return Promise.reject()}else g({msg:"提交创建优惠券成功",type:"success"},y);else{const Q=D.error_msg||D.errorMsg||"";return g({msg:`创建优惠券失败:${Q}`,type:"danger"},y),Promise.reject()}g({msg:"准备获取优惠券信息",type:"info"}),await Ve(15*1e3),await Pe(async Q=>{g({msg:`获取优惠券列表第${Q}次--`});const me=(await Yt(t,{goods_list:[String(s.goods_id||"")],batch_status:1})).result.data_list.find(ve=>ve.batch_desc===m);if(me)s.coupon_code=me.batch_sn,s.coupon_batch_id=me.batch_id,g({msg:"成功获取到优惠券码"+s.coupon_code,type:"success"});else return Q>=30&&g({msg:"没有获取到优惠券码",type:"danger"}),Promise.reject()},30,10*1e3)}async function l(s){if(s.goodsInfo){g({msg:"已从缓存中获取下单商品信息",type:"success"});return}const{tempGoodsInfo:t,mall:i,row:y}=s,{id:j=0,sku_list:U=[],goods_name:x,mall_id:m,thumb_url:R}=t||{},N=await Qt({mallId:i.mallId,goods_id:j});if(N.code)return g({msg:"自动供货失败"+N.msg,type:"danger"},y),Promise.reject();g({msg:"自动供货成功,将获取拼团ID"}),await Ve(3e3),await Pe(async O=>(g({msg:`正在获取拼团ID第${O}次`},y),Kt({goods_id:j,sku_id:U[0].skuId},{showErrorMsg:!1})),3).then(async O=>{const D=O.data;if(D){const Q={goods_name:x,goods_id:j,skus:U.map(F=>({skuId:F.skuId,groupPrice:F.groupPrice/100,normalPrice:F.normalPrice/100,skuImg:F.skuThumbUrl,spec:F.spec})),group_id:{alone:D,multiple:[D]},mallId:m,mallName:i.mallName,normalPrice:0,group_order_ids:[],groupPrice:0,goods_img:R,activity_id:0,coupon_code:""};return s.goodsInfo=Q,O}else return Promise.reject()}).catch(async O=>(g({msg:`获取拼团ID失败${O.msg}`,type:"danger"},y),await n(s),Promise.reject(O)))}async function n(s){const{mall:t,coupon_batch_id:i}=s;if(t&&i)return Dt(t,{batch_id:i}).then(y=>{y.success?g({msg:"成功关闭优惠券领取",type:"success"}):g({msg:`关闭优惠券领取失败${y.error_msg||y.errorMsg||"-"}`,type:"danger"})})}async function r(s){const t=s.goodsInfo;if(!t)return await n(s),Promise.reject({msg:"没有商品信息"});try{const{accounts:i=[],row:y}=s;if(!i.length)return await n(s),Promise.reject({msg:"没有小号"});g({msg:"正在强制绑定商品"},y),await Promise.allSettled([w(s)]);const{coupon_code:j,mall:U,promotion_event_sn:x,giftInfo:m}=s;if(!x){g({msg:"没有成功绑定商品,",type:"danger"},y),await n(s);return}const{skus:R,goods_id:N,goods_name:O,group_id:D}=t,Q=new Map;i.forEach(ue=>{const me=String(ue);Q.has(me)?Q.get(me).count++:Q.set(me,{account:ue,count:1})}),await p({goodsInfo:t,requestData:s}),g({msg:"准备执行领取优惠券并下单"},y);let F=0;await Fe([...Q.values()],{batch:25,request:async ue=>{await Promise.allSettled(ue.map(async me=>{const ve=new Array(me.count).fill(me.account);console.log("accountList",ve),await Fe(ve,{batch:1,request:async be=>{const Le=be[0],Ye=await a(Le,s),Re={account:Le,sku:R[0].skuId,sku_spec:R[0].spec,shop_id:U.mallId,shop_name:U.mallName,goods_id:N,num:s.num_per_order,mode:"open_group",goods_name:O,group_id:D.multiple[0]||D.alone,type:"gift",line:h.line2?"guoyuan":void 0,use_coupon:!0,...Ye};return await Pe(async()=>vs({coupon_code:j,account:Le},{showErrorMsg:!1}),5,2e3).then(Ge=>{const ht=Ge.data&&Ge.data.promotion_identity_vo;if(h.line2){g({msg:`${Le}领取优惠券成功`,type:"success"});return}if(!ht)return Promise.reject({msg:"promotion_identity_vo不存在"});const{promotion_id:S,promotion_type:$e,extension:He}=ht;Re.single_promotion_list=[{promotion_id:S,promotion_type:$e,sku_ids:[m.mainGoodsSkuId],mall_id:U.mallId}],g({msg:`${Le}领取优惠券成功`,type:"success"},y)}).catch(Ge=>(console.log(Ge,"getMerchantCouponV2 -error"),g({msg:`${Le}领取优惠券失败${Ge.msg}`,type:"danger"},y),Promise.reject())),x&&(Re.goods_promotion_list=[{promotion_type:28,promotion_id:x,sku_ids:[m.mainGoodsSkuId],extension:{gift_use_list:JSON.stringify([{mallId:U.mallId,goodsId:m==null?void 0:m.giftGoodsId,skuId:m==null?void 0:m.giftGoodsSkuId,goodsNumber:s.num_per_order}])}}]),console.log("createOrderRequestData",Re),Pe(async()=>At(Re,{showErrorMsg:!1}).then(Ge=>{h.isTempLoop&&!s.isCacheTempGoods&&Ce.value.push({goodsInfo:t,mallId:U.mallId,tempGoodsInfo:s.tempGoodsInfo,status:"pending",lastUsedTime:Date.now()}),F++,g({msg:`${Re.goods_name}:下单成功。订单号:${Ge.data.order_sn},已成功${F}次`,type:"success"}),J(Ge.data)}),3).catch(Ge=>{g({msg:`${Re.goods_name}:下单失败:${Ge.msg}`,type:"danger"})})}})}))}}),g({msg:`下单结束，成功下单数量${F}`,type:F>0?"success":"danger"},y),await n(s),await p({goodsInfo:t,requestData:s,type:"recover"}),await $(s)}catch(i){console.log("orderCreate - catch",i)}}async function a(s,t){const i=ze(),{nameCode_active:y,nameCode_position:j,nameCode_str:U,addr_active:x,addr_position:m,addr_str:R,filterStr:N,type:O,appoint_address:D}=i.address;let Q={appoint_address:D||void 0};if(t.costTemplateInfo){const F=t.costTemplateInfo.province;Q.appoint_address=F.reduce((ue,me,ve)=>ue+=me.name+(ve===F.length-1?"":","),"")}if(O=="diy"){const F=await Ze();Reflect.deleteProperty(F,"id"),Q=F}else Q={...Q,filter_address:N.replace("，",","),address_cipher:x?R:void 0,address_site:x?m:void 0,name_cipher:y?U:void 0,name_site:y?j:void 0};return Ut({account:s,...Q},{showErrorMsg:!1}).then(F=>F.data).catch(F=>(Ke().recover(s),g({msg:"添加地址出错:"+F.msg,type:"danger"},t.row),Promise.reject()))}async function p(s){const{goodsInfo:t,requestData:i,type:y="reduce"}=s,{mall:j,price_per_order:U}=i,{skus:x}=t;let m={success:!1,error_msg:"--"};if(y==="recover"){let R={goods_id:i.goods_id,market_price_in_yuan:"5002.00",market_price:500200,sku_prices:x.map(O=>({sku_id:O.skuId,multi_price_in_yuan:"4000",single_price_in_yuan:"5001.00",multi_price:4e5,single_price:500100}))},N=It.cloneDeep(R);R.market_price=50200,R.market_price_in_yuan="502.00",R.sku_prices.forEach(O=>{O.multi_price=5e4,O.multi_price_in_yuan="500.00",O.single_price=50100,O.single_price_in_yuan="501.00"}),m=await Je(j,R),m.success&&setTimeout(()=>{Je(j,N)},25*1e3)}else{const R=bs.find(O=>O.multi_price===U*100);if(!R)return g({msg:"--修改价格失败，没有找到修改价格参数",type:"danger"}),n(i),Promise.reject();const N={goods_id:i.goods_id,market_price_in_yuan:R.market_price_in_yuan,market_price:R.market_price,sku_prices:x.map(O=>({sku_id:O.skuId,multi_price_in_yuan:R.multi_price_in_yuan,single_price_in_yuan:R.single_price_in_yuan,multi_price:R.multi_price,single_price:R.single_price}))};m=await Je(j,N),m.success&&await new Promise(O=>{u(i).then(O).catch(()=>{m={success:!1,error_msg:"价格检测未通过"},O(!0)})})}if(m.success)g({msg:"修改价格成功",type:"success"});else return g({msg:"修改价格失败"+Ee(m),type:"danger"},i.row),await n(i),await $(i),Promise.reject()}async function u(s){var m;const{mall:t,row:i,goods_id:y}=s,j=20;if(g({msg:"检查商品sku价格",type:"info"},i),await Ve(2e3),await Pe(async R=>{var O,D;g({msg:`检查商品价格第${R}次`,type:"info"});const N=await Ot(t,{goods_id_list:[String(y)],pre_sale_type:4});if(!N.success)return g({msg:`获取商品信息失败${N.error_msg||""}`,type:"danger"}),R>=j?!1:Promise.reject(!1);if(N&&N.result&&N.result.total){const Q=N.result.goods_list[0];if(!Q.sku_list)return R>=j?!1:Promise.reject(!1);if(((D=(O=Q.sku_list)==null?void 0:O[0])==null?void 0:D.groupPrice)<550)return g({msg:"商品价格已更改",type:"success"},i),!0}return te.stop?(g({msg:"检查商品价格失败-用户停止",type:"danger"}),!1):R>=j?!1:Promise.reject(!1)},j,3e3))return{success:!0};await Ve(1e3);const x=await Bt(t,[String(y)]);if(x&&x.success&&((m=x.result)!=null&&m.total)){const R=x.result.list[0],{reject_comment:N}=R;return N.includes("商品SKU价格异常")?Promise.reject({success:!1,error_msg:"商品sku价格异常"}):Promise.reject(x)}else return{success:!0}}async function w(s){const{mall:t,tempGoodsInfo:i,row:y}=s;let j=i==null?void 0:i.id,U="";const x=y.id;let m=y._skuSelect;if(!x){const O=await zs(t,{goods_name:x});if(!O.success)return g({msg:"获取赠品列表失败"+O.error_msg||O.errorMsg,type:"danger"},y),Promise.reject(O);m=O.result.gift_display_info_list[0].sku_id}U=(await Os(t,{goods_id:Number(j),gift_goods_id:Number(x)})).result.sku_display_info_list[0].sku_id;const N={event_name:"送赠品",start_time:Xe(Date.now()).startOf("day").valueOf(),gift_goods_id:x,gift_sku_id:m,effective_time_type:1,main_goods_enroll_list:[{goods_id:j,enroll_sku_ids:[U],quantity:"2000000"}]};if(!j||!U||!x||!m)return g({msg:"信息不全-无法操作",type:"danger"}),Promise.reject();s.giftInfo={mainGoodsId:Number(j),mainGoodsSkuId:Number(U),giftGoodsId:Number(x),giftGoodsSkuId:Number(m)},await Pe(async O=>{g({msg:`第${O}次尝试绑定商品`});const D=await Bs(t,N);if(console.log(D,"createGiftGoodsApi",O),!D.success)return Promise.reject(D);const{success_event_info_list:Q}=D.result,F=Q.find(ue=>ue.goods_id==j);return F&&F.promotion_event_sn?(g({msg:"绑定成功"+F.promotion_event_sn}),s.promotion_event_sn=F.promotion_event_sn,!0):Promise.reject("绑定失败")},6,2e3).catch(O=>(g({msg:"绑定失败",type:"danger"}),Promise.reject(O)))}async function $(s){const{mall:t,promotion_event_sn:i}=s;if(!i)return;const y=await Ks(t,{promotion_event_sn:i});y.success?g({msg:"取消绑定成功"}):g({msg:"取消绑定失败"+y.error_msg||y.errorMsg,type:"danger"})}async function M(s){if(s.isCacheTempGoods){g({msg:"循环使用的炮灰商品,无法复原",type:"warning"});return}if(!s.isFlagshipStore){g({msg:"非旗舰店下单方式商品,无法复原",type:"warning"});return}try{g({msg:"正在复原商品信息"}),await Ve(15*1e3);const{mall:t}=s,{goods_commit_id:i,goods_id:y,detail_gallery:j}=s.cloneGoodsDetails,U=await Qs(t,{goods_commit_id:i,goods_id:y,cat_id:s.cloneGoodsDetails.cat_id});g({msg:"正在复原商品信息-"});const x=await Pe(async Q=>{g({msg:`正在申请编辑商品-第${Q}次`});const F=await Hs(t,{goods_id:s.goods_id});return Q>=5||F.success?F:Promise.reject(F)},5,10*1e3);if(!x.success)return g({msg:"申请编辑商品失败"+x.error_msg,type:"danger"}),Promise.reject();const m=x.result.goods_commit_id;g({msg:"正在复原商品信息--"});const R=await Ys({...U,detail:s.cloneGoodsDetails,goods_commit_id:i,goods_id:y,new_goods_id:s.goods_id,new_goods_commit_id:m,decoration:{floor_list:j.filter(Q=>Q.type==2).map(Q=>({content_list:[{img_url:Q.url}]}))}});s.newGoodsCloneInfo=R.data,g({msg:"正在复原商品信息---"});const N=await os(t,It.cloneDeep(s.newGoodsCloneInfo));console.log("editGoods - createRes",N);const O=await new Promise((Q,F)=>{ns().then(ue=>{const{anti_content1:me,anti_content2:ve}=ue.data;Q([me,ve])}).catch(ue=>{g({msg:"获取anticontent失败"+ue.msg}),F()})});g({msg:"正在复原商品信息----"});const D=await as(t,{goods_commit_id:s.newGoodsCloneInfo.goods_commit_id,goods_id:s.newGoodsCloneInfo.goods_id},O);console.log("editGoods - submitRes",D),D.success?g({msg:"商品信息已复原",type:"success"}):g({msg:"商品复原失败"+(D.error_msg||D.errorMsg),type:"danger"})}catch(t){let i="";typeof t=="object"?t.msg&&(i=t.msg):i=String(t),g({msg:"商品信息复原失败:"+String(i),type:"danger"})}}async function K(s){const{row:t,mall:i,goods_id:y,cloneGoodsDetails:j}=s,U=t.id,x=[1,3],{cat_id_1:m,cat_id_2:R,cat_id_3:N,cat_id_4:O}=j;await Promise.allSettled(x.map(async D=>{const Q=D===1?"白底图":"长图",F=await Ds(i,{type:D,goods_id:U});if(!F.success){g({msg:`获取${Q}失败，${F.error_msg||F.errorMsg}`});return}const{check_status:ue,latest_material:me}=F.result||{};if(ue==2&&me){g({msg:`正在处理${Q}`});const{base64:ve}=await ln(me);g({msg:`正在上传${Q}`});const be=await Ws(i,ve);if(console.log("uploadRes",be),!be.url){g({msg:`上传${Q}失败，${F.error_msg||F.errorMsg}`});return}const Le=await Js(i,{url:be.url});if(console.log("createRes",Le),Le.error_msg){g({msg:`创建${Q}失败，${Le.error_msg}`});return}const Ye=Le.result,Re=await Xs(i,{type:D,mall_file_id:Ye,goods_id:y,cat_id_1:m,cat_id_2:R,cat_id_3:N,cat_id_4:O,content:be.url});console.log(Re),g({msg:`结束${Q}操作`})}else g({msg:`该商品${Q}不存在或没有有效的${Q}`})}))}async function J(s){const{order_sn:t}=s,i=ze(),{chance:y,zfb_pass:j}=i.pay;if(y==="immediate"){if(!j){Se({msg:"没有设置支付密码，不会开启自动支付",type:"warning"});return}g({msg:"获取支付链接"}),Ht({order_sn:t}).then(U=>{const x=Jt();Se({msg:`订单${t},已进入自动支付队列`}),x.addToPendding([{...U.data,type:"auto"}])}).catch(U=>{Se({msg:`订单${t},获取支付链接失败，不会自动支付`})})}}return(s,t)=>{const i=pt,y=_t,j=Pt,U=gs,x=ft,m=Ct,R=Wt,N=Tt,O=Et,D=Gs,Q=Rs,F=nt,ue=xt,me=gt,ve=rs,be=We("VxeColumn"),Le=We("VxeTable"),Ye=Vt,Re=ks,Ge=ys,ht=us;return se(),we("div",rn,[e(ht,{size:"default"},{default:o(()=>[_("div",dn,[_("div",un,[cn,_("div",_n,[e(m,{"label-position":"top",size:"small"},{default:o(()=>[e(j,{label:"店铺:"},{default:o(()=>[e(y,{modelValue:h.mallId,"onUpdate:modelValue":t[0]||(t[0]=S=>h.mallId=S),onChange:T,disabled:te.start},{default:o(()=>[(se(!0),we(je,null,Te(G(oe).tableList,S=>(se(),xe(i,{label:S.mallName,value:S.mallId},null,8,["label","value"]))),256))]),_:1},8,["modelValue","disabled"])]),_:1}),e(j,{label:"是否旗舰店"},{default:o(()=>[e(U,{modelValue:h.isFlagshipStore,"onUpdate:modelValue":t[1]||(t[1]=S=>h.isFlagshipStore=S),"active-text":"是","inactive-text":"否",onChange:t[2]||(t[2]=S=>{S||(h.recover=!1)})},null,8,["modelValue"])]),_:1}),e(j,{label:"小号位置:"},{default:o(()=>[e(x,{modelValue:G(ke),"onUpdate:modelValue":t[3]||(t[3]=S=>mt(ke)?ke.value=S:null),controls:!1,style:{"flex-grow":"1"}},null,8,["modelValue"])]),_:1})]),_:1})])]),_("div",pn,[mn,_("div",gn,[e(m,{"label-position":"top",inline:!1,size:"small",style:{"justify-content":"flex-start"}},{default:o(()=>[e(j,{label:"购买数量",style:{"flex-shrink":"0"}},{default:o(()=>[e(N,{modelValue:h.num_type,"onUpdate:modelValue":t[4]||(t[4]=S=>h.num_type=S),style:{"margin-bottom":"10px"}},{default:o(()=>[e(R,{label:"random"},{default:o(()=>[V("随机")]),_:1}),e(R,{label:"random-range"},{default:o(()=>[V("范围随机")]),_:1}),e(R,{label:"appoint"},{default:o(()=>[V("指定")]),_:1})]),_:1},8,["modelValue"]),Me(e(O,null,{default:o(()=>[e(x,{style:{width:"77px"},controls:!1,modelValue:h.num_appoint,"onUpdate:modelValue":t[5]||(t[5]=S=>h.num_appoint=S),precision:0,min:101},null,8,["modelValue"]),fn]),_:1},512),[[Ne,h.num_type==="appoint"]]),Me(e(O,null,{default:o(()=>[e(x,{style:{width:"58px"},controls:!1,modelValue:h.num_times,"onUpdate:modelValue":t[6]||(t[6]=S=>h.num_times=S),precision:0,min:3,max:5},null,8,["modelValue"]),yn]),_:1},512),[[Ne,h.num_type==="random"]]),Me(e(O,{size:2},{default:o(()=>[e(x,{style:{width:"77px"},controls:!1,modelValue:h.randomRage.v1,"onUpdate:modelValue":t[7]||(t[7]=S=>h.randomRage.v1=S),precision:0,min:101},null,8,["modelValue"]),wn,e(x,{style:{width:"77px"},controls:!1,modelValue:h.randomRage.v2,"onUpdate:modelValue":t[8]||(t[8]=S=>h.randomRage.v2=S),precision:0,min:101},null,8,["modelValue"])]),_:1},512),[[Ne,h.num_type==="random-range"]]),e(Q,{"show-after":500,placement:"right"},{content:o(()=>[hn,kn]),default:o(()=>[e(O,{style:{"margin-top":"5px"}},{default:o(()=>[vn,e(D,null,{default:o(()=>[e(G(kt))]),_:1})]),_:1})]),_:1}),e(U,{disabled:!h.isFlagshipStore,modelValue:h.recover,"onUpdate:modelValue":t[9]||(t[9]=S=>h.recover=S),"active-text":"同步","inactive-text":"不同步"},null,8,["disabled","modelValue"]),e(O,null,{default:o(()=>[bn,e(y,{modelValue:h.order_per_max_count,"onUpdate:modelValue":t[10]||(t[10]=S=>h.order_per_max_count=S),style:{width:"80px"}},{default:o(()=>[h.isFlagshipStore?dt("",!0):(se(),xe(i,{key:0,label:"200001(仅工商注册分类)",value:200001})),e(i,{label:"9505",value:9505}),e(i,{label:"3851",value:3851}),e(i,{label:"359",value:359})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(j,{label:"其他"},{default:o(()=>[_("p",null,[e(F,{style:{width:"150px"},type:"success",plain:"",onClick:ee},{default:o(()=>[V("获取炮灰商品")]),_:1})]),_("p",Sn,[e(F,{loading:G(H).loading,disabled:!G(L).selection.length,style:{width:"150px"},type:"danger",plain:"",onClick:C},{default:o(()=>[V("下架选中")]),_:1},8,["loading","disabled"])]),_("p",$n,[e(ue,{title:"【炮灰商品的主图sku图详情图使用内置图片】。如果主链接主图，详情图有问题(例如带价格)，导致改销量链接被驳回，可以使用软件内置图片发布新商品",modelValue:h.useDefaultImage,"onUpdate:modelValue":t[11]||(t[11]=S=>h.useDefaultImage=S),label:"创建商品使用内置图片"},null,8,["modelValue"])]),_("p",null,[e(ue,{label:"下单使用线路2",modelValue:h.line2,"onUpdate:modelValue":t[12]||(t[12]=S=>h.line2=S)},null,8,["modelValue"])]),_("p",null,[e(y,{modelValue:h.cat_id,"onUpdate:modelValue":t[13]||(t[13]=S=>h.cat_id=S),style:{width:"120px"}},{default:o(()=>[e(i,{label:"商品分类：默认",value:0}),(se(!0),we(je,null,Te(G(co),S=>(se(),xe(i,{label:S.cats.join("-"),value:S.cat_id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"]),e(Q,{placement:"top"},{content:o(()=>[In,Vn]),default:o(()=>[e(D,{style:{"margin-left":"5px"}},{default:o(()=>[e(G(kt))]),_:1})]),_:1})])]),_:1}),e(j,{label:"炮灰循环"},{default:o(()=>[_("p",null,[e(U,{"active-text":"开启",modelValue:h.isTempLoop,"onUpdate:modelValue":t[14]||(t[14]=S=>h.isTempLoop=S),"inactive-text":"关闭"},null,8,["modelValue"])]),Me(_("div",null,[_("p",Cn,[e(N,{modelValue:h.tempLoopType,"onUpdate:modelValue":t[15]||(t[15]=S=>h.tempLoopType=S)},{default:o(()=>[e(R,{label:"random"},{default:o(()=>[V("随机生成")]),_:1}),e(R,{label:"appoint"},{default:o(()=>[V("指定炮灰("+ge(G(c).length)+")",1)]),_:1})]),_:1},8,["modelValue"]),e(Q,null,{content:o(()=>[Pn,xn,Ln,Un,An]),default:o(()=>[e(D,{style:{"margin-left":"5px"}},{default:o(()=>[e(G(kt))]),_:1})]),_:1})]),Me(_("div",null,[e(me,{type:"textarea",rows:2,resize:"none",placeholder:"请输入炮灰商品ID，多个用逗号隔开,建议至少保证2个可用",modelValue:h.appointTempGoodsId,"onUpdate:modelValue":t[16]||(t[16]=S=>h.appointTempGoodsId=S)},null,8,["modelValue"])],512),[[Ne,h.tempLoopType==="appoint"]]),Me(_("div",null,[e(F,{disabled:Ce.value.length===0,onClick:t[17]||(t[17]=S=>Ce.value=[])},{default:o(()=>[V("清空炮灰商品缓存("+ge(Ce.value.length)+")",1)]),_:1},8,["disabled"])],512),[[Ne,h.tempLoopType==="random"]])],512),[[Ne,h.isTempLoop]]),_("p",null,[e(Q,null,{content:o(()=>[jn,Tn]),default:o(()=>[e(O,null,{default:o(()=>[e(ue,{modelValue:h.auto_increase_price,"onUpdate:modelValue":t[18]||(t[18]=S=>h.auto_increase_price=S),disabled:te.start,label:"规避低价限制"},null,8,["modelValue","disabled"]),e(D,null,{default:o(()=>[e(G(kt))]),_:1})]),_:1})]),_:1})])]),_:1})]),_:1})])]),_("div",En,[Rn,_("div",Gn,[Mn,_("p",null,[e(F,{type:"success",onClick:t[19]||(t[19]=S=>le()),loading:G(H).loading},{default:o(()=>[V("获取商品")]),_:1},8,["loading"])]),_("p",null,[e(F,{type:"primary",onClick:z,loading:te.start},{default:o(()=>[V("开始")]),_:1},8,["loading"])]),_("p",null,[e(F,{type:"primary",disabled:te.stop||!te.start,onClick:t[20]||(t[20]=S=>{g({msg:"已发送停止指令"}),te.stop=!0})},{default:o(()=>[V("停止")]),_:1},8,["disabled"])]),_("p",null,[e(O,null,{default:o(()=>[e(ue,{onChange:t[21]||(t[21]=S=>S&&(Z.value=!0)),label:`防薅羊毛(${G(v).appointAddrList.length})`,modelValue:G(v).active,"onUpdate:modelValue":t[22]||(t[22]=S=>G(v).active=S)},null,8,["label","modelValue"]),e(ve,{underline:!1,onClick:t[23]||(t[23]=S=>Z.value=!0)},{default:o(()=>[V("打开设置")]),_:1})]),_:1})])])])]),_("div",Nn,[e(Le,ut({ref_key:"tableRef",ref:Oe},{...G(H),...G(W)}),{default:o(()=>[e(be,{type:"checkbox",width:50}),e(be,{type:"seq",title:"序号",width:50}),e(be,{title:"商品ID",field:"id",width:130}),e(be,{title:"商品名称",field:"goods_name"}),e(be,{title:"SKU"},{default:o(S=>[e(y,{modelValue:S.row._skuSelect,"onUpdate:modelValue":$e=>S.row._skuSelect=$e,onClick:$e=>S.row._sku_list=S.row.sku_list||[]},{default:o(()=>[(se(!0),we(je,null,Te(S.row._sku_list||[],$e=>(se(),xe(i,{label:$e.spec||S.row.goods_name,value:$e.skuId},null,8,["label","value"]))),256)),e(i,{label:"系统自己选",value:0})]),_:2},1032,["modelValue","onUpdate:modelValue","onClick"])]),_:1}),e(be,{title:"SKUID",width:140},{default:o(S=>{var $e;return[V(ge(($e=S.row.sku_list.find(He=>He.skuId==S.row._skuSelect))==null?void 0:$e.skuId),1)]}),_:1}),e(be,{title:"SKU拼团价格"},{default:o(S=>{var $e;return[V(ge((($e=S.row.sku_list.find(He=>He.skuId==S.row._skuSelect))==null?void 0:$e.groupPrice)/100),1)]}),_:1}),e(be,{title:"创建时间",field:"created_at",formatter:({row:S})=>G(fs)(S.created_at),sortable:""},null,8,["formatter"]),e(be,{title:"库存"},{default:o(S=>{var $e;return[V(ge(($e=S.row.sku_list.find(He=>He.skuId==S.row._skuSelect))==null?void 0:$e.skuQuantity),1)]}),_:1}),e(be,{title:"商品销量",field:"sold_quantity",sortable:"",width:80}),e(be,{title:"状态",field:"",width:150,sortable:"","sort-by":({row:S})=>{var $e;return(($e=X.value.get(S.id))==null?void 0:$e.msg)||""}},{default:o(S=>{var $e,He;return[_("span",{class:St(($e=X.value.get(S.row.id))==null?void 0:$e.type)},ge((He=X.value.get(S.row.id))==null?void 0:He.msg),3)]}),_:1},8,["sort-by"])]),_:1},16),_("footer",Fn,[_("p",null,[e(O,null,{default:o(()=>[zn]),_:1}),e(O,null,{default:o(()=>[e(ve,{underline:!1,onClick:t[24]||(t[24]=S=>G(q)("check-all"))},{default:o(()=>[V("全选")]),_:1}),e(ve,{underline:!1,onClick:t[25]||(t[25]=S=>G(q)("reverse"))},{default:o(()=>[V("反选")]),_:1}),e(ve,{underline:!1,onClick:t[26]||(t[26]=S=>G(q)("area-select"))},{default:o(()=>[V("区域选择")]),_:1}),e(ve,{underline:!1,onClick:t[27]||(t[27]=S=>G(q)("clear"))},{default:o(()=>[V("取消选择")]),_:1}),e(me,{onKeyup:t[28]||(t[28]=ds(S=>le(),["enter"])),placeholder:"ID搜索,多个逗号隔开",modelValue:ce.goods_id,"onUpdate:modelValue":t[29]||(t[29]=S=>ce.goods_id=S)},null,8,["modelValue"]),e(ve,{underline:!1,onClick:re},{default:o(()=>[V("大量输入")]),_:1})]),_:1})]),_("p",null,[On,e(Lt,{onSizeChange:t[30]||(t[30]=()=>{G(E).page=1,le()}),onCurrentChange:t[31]||(t[31]=S=>le()),total:G(E).total,"page-sizes":G(E).pageSizes,"current-page":G(E).page,"onUpdate:current-page":t[32]||(t[32]=S=>G(E).page=S),"page-size":G(E).limit,"onUpdate:page-size":t[33]||(t[33]=S=>G(E).limit=S)},null,8,["total","page-sizes","current-page","page-size"])])])]),e(yt,{list:ce.logList,"onUpdate:list":t[34]||(t[34]=S=>ce.logList=S),height:"90px"},null,8,["list"]),e(Ye,{width:1e3,modelValue:ne.value.show,"onUpdate:modelValue":t[35]||(t[35]=S=>ne.value.show=S),title:"检测结果",onClose:t[36]||(t[36]=S=>ne.value.errors=[]),"close-on-press-escape":!1},{default:o(()=>[_("div",Bn,[(se(!0),we(je,null,Te(ne.value.errors,S=>(se(),we("div",Kn,[_("h4",null,ge(S.goodsInfo.id)+"-"+ge(S.goodsInfo.goods_name),1),_("div",Qn,ge(S.msg),1)]))),256))])]),_:1},8,["modelValue"]),e(Ye,{modelValue:Z.value,"onUpdate:modelValue":t[42]||(t[42]=S=>Z.value=S),title:"防薅羊毛设置","append-to-body":"","close-on-press-escape":!1},{footer:o(()=>[e(F,{onClick:t[41]||(t[41]=S=>Z.value=!1)},{default:o(()=>[V("关闭弹窗")]),_:1})]),default:o(()=>[e(Re,{type:"error",closable:!1},{default:o(()=>[Hn,Yn,Dn,Wn]),_:1}),e(O,{style:{margin:"12px 0"}},{default:o(()=>[e(ve,{underline:!1,type:"primary",onClick:t[37]||(t[37]=S=>pe("all"))},{default:o(()=>[V("全选")]),_:1}),e(ve,{underline:!1,type:"primary",onClick:t[38]||(t[38]=S=>pe("reverse"))},{default:o(()=>[V("反选")]),_:1}),e(ve,{underline:!1,type:"primary",onClick:t[39]||(t[39]=S=>pe("clear"))},{default:o(()=>[V("取消")]),_:1})]),_:1}),Jn,e(Ge,{modelValue:G(v).appointAddrList,"onUpdate:modelValue":t[40]||(t[40]=S=>G(v).appointAddrList=S)},{default:o(()=>[(se(!0),we(je,null,Te(G(st),S=>(se(),xe(ue,{label:S.id},{default:o(()=>[V(ge(S.regionName),1)]),_:2},1032,["label"]))),256))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})])}}}),Zn=it(Xn,[["__scopeId","data-v-2028af49"]]);var Nt=void 0,qn=function(){},Vs=["assert","clear","count","debug","dir","dirxml","error","exception","group","groupCollapsed","groupEnd","info","log","markTimeline","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"],ls=Vs.length;for(;ls--;)Nt=Vs[ls],console[Nt]||(console[Nt]=qn);const is=console,tt=ie=>(at("data-v-b5a6e263"),ie=ie(),lt(),ie),ea={class:"cs-limit"},ta=tt(()=>_("span",null,"每单购买：",-1)),sa=tt(()=>_("span",null,"订单数量：",-1)),oa=tt(()=>_("span",null,"每单购买：",-1)),na=tt(()=>_("span",null,"-",-1)),aa=tt(()=>_("span",null,"订单数量：",-1)),la=tt(()=>_("span",null,"-",-1)),ia=tt(()=>_("span",null,"小号位置：",-1)),ra=et({__name:"CSLimit",setup(ie){const oe=Ie({create:!1}),X=qe(1,"start_site"),f=Ae({mallId:"",num_type:"appoint",num:{appoint:{order_num:1,num_per_order:1},random:{order_num_1:1,order_num_2:1,num_per_order_1:1,num_per_order_2:1}},logList:[],goodsIds:"",useLine2:!1});function T(H){f.logList.push({time:Xe().format("YYYY-MM-DD HH:mm:ss"),msg:"",...H})}const C=jt(()=>{const{goodsIds:H}=f,L=H.split(/[,，\n]/),W=new Set;return L.forEach(E=>{if(E.length<6)return;const q=Number(E.trim());q&&Number.isInteger(q)&&W.add(q)}),[...W]}),Z=ct();async function v(){const H=Z.tableList.find(W=>W.mallId==Number(f.mallId));if(!H)return ye.warning({message:"请选择店铺",grouping:!0});if(!Z.checkAvailable(H))return ye.warning({message:"当前店铺不可用",grouping:!0});const L=[...C.value];if(!L.length)return ye.warning({message:"请输入商品ID",grouping:!0});f.logList=[],oe.value.create=!0,await Fe(L,{batch:1,isStop:()=>!oe.value.create,request:async W=>{await Promise.allSettled(W.map(async E=>{const q=Se(),ee={mall:H,goodsId:E,skuList:[],num:q,goodsName:""};await g(ee),await pe(ee),await h(ee),await pe(ee,!1),await Promise.allSettled([c(ee)]),pe(ee)}))}}),oe.value.create=!1,T({msg:"执行结束"})}async function pe(H,L=!0){const{goodsId:W,mall:E}=H;if(L){T({msg:`${W}开始检测商品立减券`});{const de=await Yt(E,{goods_list:[String(W||"")],batch_status:1});if(!de.success)return T({msg:`${W}获取获取商品立减券列表失败:${Ee(de)}`,type:"danger"}),Promise.reject();const le=de.result.data_list;await Promise.allSettled(le.map(async re=>{const _e=re.batch_id;if(_e){const fe=await Dt(E,{batch_id:_e});fe.success?T({msg:"成功关闭优惠券领取",type:"success"}):T({msg:`关闭优惠券领取失败${Ee(fe)}`,type:"danger"})}}))}T({msg:`${W}开始并关闭检测活动`});const q=await zt(E,{goods_id:W,activity_types:[12]});if(!q.success)return T({msg:`${W}获取获取列表失败:${Ee(q)}`,type:"danger"}),Promise.reject();const{marketing_activity_list:ee}=q.result,ne=ee==null?void 0:ee[0];if(ne){const{activity_id:de,activity_name:le}=ne;T({msg:`${W}检测到活动${le}`});const re=await _s(E,{goods_id:W,activity_id:de});if(re)T({msg:`${W}关闭活动成功`});else return T({msg:`${W}关闭活动失败:${Ee(re)}`,type:"danger"}),Promise.reject()}}else return Pe(async q=>{var de,le,re;T({msg:`${W}检测活动第${q}次`});const ee=await zt(E,{goods_id:W,activity_types:[12]});if(!ee.success)return Promise.reject(ee);const ne=(de=ee.result.marketing_activity_list)==null?void 0:de[0];if(ne){const _e=(re=(le=ne.price_info)==null?void 0:le[0])==null?void 0:re.activity_detail_id;_e&&(T({msg:`${W}检测到活动ID${_e}`}),H.activity_detail_id=_e)}else return Promise.reject()},10,2e3).catch(q=>(T({msg:`${W}检测活动失败,请前往后台关闭限时活动`,type:"danger"}),Promise.reject(q)))}async function h(H){var E,q;const{goodsId:L,mall:W}=H;{const ee=await Zs(W,{goods_id_list:[L],query_activity_type:12});if(!ee.success)return T({msg:`${L}获取商品列表失败:${Ee(ee)}`,type:"danger"}),Promise.reject();H.goodsName=(q=(E=ee.result.goods_list)==null?void 0:E[0])==null?void 0:q.goods_name}{const ee=await qs(W,{search_marketing_tool_sku_request_list:[{goods_id:L}]});if(!ee.success)return T({msg:`${L}获取商品sku列表失败:${Ee(ee)}`,type:"danger"}),Promise.reject();const{search_marketing_tool_sku_result_list:ne}=ee.result,de=ne.find(re=>re.goods_id==L);if(!de)return T({msg:`${L}获取商品sku列表失败:未找到该商品`,type:"danger"}),Promise.reject();const le=de.valid_sku_volist;return le.length?new Promise((re,_e)=>{let fe=!1;const te=async(ae=!0)=>{const Ce=await eo(W,{batch_stage_activities:[{stage_activities:[{activity_name:"限量"+Xe().format("YYYY-MM-DD"),activity_type:12,auto_create_after_finished:!1,tool_full_channel:"10921_77271__normal",goods_id:L,pattern:11,check_low_price:ae,goods_unified_activity:!0,start_time:Xe().startOf("day").unix(),end_time:Xe().add(1,"day").endOf("day").unix(),price_list:[{user_activity_limit:0,quantity:function(){const b=le.reduce((d,P)=>d+P.quantity,0);return b>1e5?1e5:b}(),sku_price_dtos:le.map(b=>{let d=b.sku_group_price;return d=d*.1,{sku_id:b.sku_id,activity_price:d}})}]}]}]});if(!Ce.success)return T({msg:`${L}创建活动失败:${Ee(Ce)}`,type:"danger"}),_e();const{success_goods_id_list:Y,fail_list:k}=Ce.result,z=k.find(b=>b.goods_id==L);if(z){if(z.fail_reason.includes("低价校验失败")&&!fe){const b=z.low_price_promotion_info_volist.map(d=>d.token);await to(W,{token_list:b}),fe=!0,te(!1);return}return T({msg:`${L}创建活动失败:${z.fail_reason}`,type:"danger"}),_e()}else is.log(Ce),T({msg:`提交${L}创建活动成功`}),H.skuList=le,re(!0)};try{te()}catch{_e()}}):(T({msg:`${L}获取商品sku列表失败:该商品无可参加活动sku`,type:"danger"}),Promise.reject())}}async function c(H){await Ve(10*1e3);const{mall:L,skuList:W,goodsId:E,goodsName:q}=H,ee=await Qt({mallId:L.mallId,goods_id:E});ee.code&&T({msg:`${E}供货失败:${ee.msg}`,type:"danger"});let ne=0;await Pe(async de=>{T({msg:`正在获取拼团ID第${de}次`});const le=H.skuList.map(re=>re.sku_id);return Kt({goods_id:E,sku_id:le[Be(0,le.length)]||le[0]},{showErrorMsg:!1})},5).then(de=>{if(ne=de.data,ne)T({msg:`${E}获取拼团ID成功${ne}`});else return Promise.reject({msg:"没有返回拼团ID"})}).catch(de=>(T({msg:`${E}获取拼团ID失败:${de}`,type:"danger"}),Promise.reject(de))),W.sort((de,le)=>de.sku_group_price-le.sku_group_price),is.log(H,"reqData"),await Fe(H.num,{batch:25,async request(de){await Promise.allSettled(de.map(async({num_per_order:le,account:re})=>{const _e=await ce(String(re)),fe={account:re,sku:W[0].sku_id,sku_spec:W[0].sku_name,shop_id:L.mallId,shop_name:L.mallName,goods_id:E,num:le,mode:"open_group",goods_name:q,group_id:ne,use_coupon:!1,activity_id:H.activity_detail_id,type:f.useLine2?"guoyuan":"paidan",..._e};return Pe(async()=>At(fe,{showErrorMsg:!1}).then(te=>{T({msg:`${fe.goods_name}:下单成功。订单号:${te.data.order_sn}`,type:"success"}),ke(te.data)}),3).catch(te=>{T({msg:`${fe.goods_name}:下单失败:${te.msg}`,type:"danger"})})}))}})}async function ce(H){T({msg:"正在获取地址信息"});const L=ze(),{nameCode_active:W,nameCode_position:E,nameCode_str:q,addr_active:ee,addr_position:ne,addr_str:de,filterStr:le,type:re,appoint_address:_e}=L.address;let fe={appoint_address:_e||void 0};if(re=="diy"){const te=await Ze();te?(Reflect.deleteProperty(te,"id"),fe=te):T({msg:"本机地址,没有找到可用地址，将使用系统随机地址",type:"warning"})}return Ut({account:H,...fe},{showErrorMsg:!1}).then(te=>(fe={...fe,...te.data,filter_address:le.replace("，",","),address_cipher:ee?de:void 0,address_site:ee?ne:void 0,name_cipher:W?q:void 0,name_site:W?E:void 0},T({msg:"获取地址信息成功"}),fe)).catch(te=>(Ke().recover(H),T({msg:"添加地址出错:"+te.msg,type:"danger"}),Promise.reject()))}async function g(H){T({msg:"正在分配小号"});const L=Ke();let W=!1;return H.num.some(E=>{const q=L.getAccount(X.value-1);if(q.status){E.account=q.data;const ee=(q.ind+2)%L.list.length;X.value=ee===0?L.list.length:ee}else T({type:"danger",msg:"获取小号出错"+q.msg}),W=!0;return!E.account}),W?(T({msg:"小号可用次数不足,当前需要"+H.num.length}),oe.value.create=!1,H.num.forEach(E=>{E.account&&L.recover(E.account)}),Promise.reject()):!0}function Se(){const{num_type:H}=f,L=[];if(H==="appoint"){const{num_per_order:W,order_num:E}=f.num.appoint;for(let q=0;q<E;q++)L.push({num_per_order:W})}else{const{num_per_order_1:W,num_per_order_2:E,order_num_1:q,order_num_2:ee}=f.num.random,ne=Be(q,ee);for(let de=0;de<ne;de++)L.push({num_per_order:Be(W,E)})}return L}async function ke(H){const{type:L,order_amount:W,shop_id:E,shop_name:q,order_sn:ee}=H,ne=ze();let de;L==="pifa"?de=Number(W-.01*100):de=Number(W-W*.1),T({msg:`订单${ee},将执行改价操作`}),await Ve(3e3),await ps({store_id:E,shop_name:q,goodsDiscount:de,order_sn:ee}).then(le=>{T({type:"success",msg:`订单${ee},改价成功`}),ms({ids:ee},{showErrorMsg:!1}),Oe(ee)}).catch(le=>{T({type:"danger",msg:`订单${ee},改价失败:${le.msg}`});const{chance:re}=ne.pay;re==="immediate"&&T({msg:"改价失败不会启动自动支付",type:"warning"})})}function Oe(H){const L=ze(),{chance:W,zfb_pass:E}=L.pay;if(W==="immediate"){if(!E){T({msg:"没有设置支付密码，不会开启自动支付",type:"warning"});return}T({msg:"获取支付链接"}),Ht({order_sn:H}).then(q=>{const ee=Jt();T({msg:`订单${H},已进入自动支付队列`}),ee.addToPendding([{...q.data,type:"auto"}])}).catch(q=>{T({msg:`订单${H},获取支付链接失败，不会自动支付`})})}}return(H,L)=>{const W=pt,E=_t,q=nt,ee=Et,ne=Pt,de=gt,le=Wt,re=Tt,_e=ft,fe=xt,te=Ct;return se(),we("div",ea,[e(te,{"label-position":"top"},{default:o(()=>[e(ne,{label:"店铺选择"},{default:o(()=>[e(ee,null,{default:o(()=>[e(E,{modelValue:f.mallId,"onUpdate:modelValue":L[0]||(L[0]=ae=>f.mallId=ae)},{default:o(()=>[(se(!0),we(je,null,Te(G(Z).tableList,ae=>(se(),xe(W,{key:ae.mallId,label:ae.mallName,value:ae.mallId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e(q,{type:"primary",onClick:L[1]||(L[1]=ae=>G(Z).addPddStore())},{default:o(()=>[V("登录店铺")]),_:1})]),_:1})]),_:1}),e(ne,{label:`商品ID(${G(C).length}个)`},{default:o(()=>[e(de,{type:"textarea",placeholder:"多个用换行和逗号隔开",modelValue:f.goodsIds,"onUpdate:modelValue":L[2]||(L[2]=ae=>f.goodsIds=ae),rows:3,resize:"none"},null,8,["modelValue"])]),_:1},8,["label"]),e(ne,{label:"修改销量"},{default:o(()=>[e(ee,null,{default:o(()=>[e(re,{modelValue:f.num_type,"onUpdate:modelValue":L[3]||(L[3]=ae=>f.num_type=ae)},{default:o(()=>[e(le,{label:"appoint"},{default:o(()=>[V("固定")]),_:1}),e(le,{label:"random"},{default:o(()=>[V("随机")]),_:1})]),_:1},8,["modelValue"]),f.num_type==="appoint"?(se(),xe(ee,{key:0},{default:o(()=>[ta,e(_e,{modelValue:f.num.appoint.num_per_order,"onUpdate:modelValue":L[4]||(L[4]=ae=>f.num.appoint.num_per_order=ae),controls:!1,min:1,precision:0,key:"appoint-1"},null,8,["modelValue"]),sa,e(_e,{modelValue:f.num.appoint.order_num,"onUpdate:modelValue":L[5]||(L[5]=ae=>f.num.appoint.order_num=ae),controls:!1,min:1,precision:0,key:"appoint-2"},null,8,["modelValue"])]),_:1})):f.num_type==="random"?(se(),xe(ee,{key:1},{default:o(()=>[oa,e(_e,{modelValue:f.num.random.num_per_order_1,"onUpdate:modelValue":L[6]||(L[6]=ae=>f.num.random.num_per_order_1=ae),controls:!1,min:1,precision:0,key:"random-1"},null,8,["modelValue"]),na,e(_e,{modelValue:f.num.random.num_per_order_2,"onUpdate:modelValue":L[7]||(L[7]=ae=>f.num.random.num_per_order_2=ae),controls:!1,min:1,precision:0,key:"random-2"},null,8,["modelValue"]),aa,e(_e,{modelValue:f.num.random.order_num_1,"onUpdate:modelValue":L[8]||(L[8]=ae=>f.num.random.order_num_1=ae),controls:!1,min:1,precision:0,key:"random-3"},null,8,["modelValue"]),la,e(_e,{modelValue:f.num.random.order_num_2,"onUpdate:modelValue":L[9]||(L[9]=ae=>f.num.random.order_num_2=ae),controls:!1,min:1,precision:0,key:"random-4"},null,8,["modelValue"])]),_:1})):dt("",!0)]),_:1})]),_:1}),e(ne,{label:"其他"},{default:o(()=>[e(ee,null,{default:o(()=>[ia,e(_e,{min:1,precision:0,modelValue:G(X),"onUpdate:modelValue":L[10]||(L[10]=ae=>mt(X)?X.value=ae:null)},null,8,["modelValue"]),e(fe,{modelValue:f.useLine2,"onUpdate:modelValue":L[11]||(L[11]=ae=>f.useLine2=ae),label:"果园下单"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),_("p",null,[e(q,{type:"danger",disabled:!oe.value.create,onClick:L[12]||(L[12]=ae=>oe.value.create=!1)},{default:o(()=>[V("停止")]),_:1},8,["disabled"]),e(q,{type:"primary",loading:oe.value.create,onClick:v},{default:o(()=>[V("执行任务")]),_:1},8,["loading"])]),e(yt,{list:f.logList,"onUpdate:list":L[13]||(L[13]=ae=>f.logList=ae),height:"360px"},null,8,["list"])])}}}),da=it(ra,[["__scopeId","data-v-b5a6e263"]]),wt=ie=>(at("data-v-bd86d088"),ie=ie(),lt(),ie),ua={class:"mall-taks"},ca=wt(()=>_("br",null,null,-1)),_a=wt(()=>_("span",{class:"color-danger"},"请确保商品sku有足够库存",-1)),pa={class:"table-header between-flex"},ma={class:"center-flex"},ga={class:"center-flex"},fa={style:{color:"var(--el-color-success)"}},ya={style:{color:"var(--el-color-primary)"}},wa={style:{color:"var(--el-color-warning)"}},va=wt(()=>_("p",null,"批发下单可随意修改价格,填入的值为最终订单价格",-1)),ha=wt(()=>_("p",null,"其他下单只能按折扣修改，且最低为1折,填入的值为订单折扣",-1)),ka=wt(()=>_("p",null,"系统默认值为1折和0.01",-1)),ba=et({__name:"order",setup(ie){const X=qe(1,"start_site"),f={notice:"_notice"},T=ze(),C=qe({num:1,type:"pifa",order_goods_count:2,skuType:"6",changePrice:!1,otherSetting:["closeInsurance"]},"mallTask-taskCreate"),Z=Ae({goods_id:"",goods_id_textarea:"",is_show_textarea:!1,mallId:""}),v=Ie({exec:!1,addStock:!1,delPifa:!1}),pe=jt(()=>C.value.type==="pifa"?2:1),h=Ie([]);function c(b){h.value.push({time:fs(Date.now()),msg:"",...b})}function ce(b){b==="pifa"?C.value.order_goods_count<2&&(C.value.order_goods_count=2):C.value.order_goods_count===2&&(C.value.order_goods_count=1)}const g=Ie(),Se=Xt(380,{id:"mallTask-table"},{tableRef:g,pagination:{limit:1e3,pageSizes:[100,1e3,2e3]},clearWhenDataChange:!1}),{tableProps:ke,tableEvents:Oe,pagination:H,tableState:L,tableAction:W}=Se,E=ct();async function q(){const b=E.tableList.find(l=>l.mallId==Z.mallId);if(!b){ye.warning("请先选择店铺");return}if(!E.checkAvailable(b)){ye.warning("店铺不可用");return}const{goods_id:d}=Z,{limit:P,page:I}=H,A=Math.round(P/100);ke.loading=!0,d&&(H.page=1);const B=await $t({delay:1e3,pageStart:(I-1)*A+1,pageEnd:I*A,tableInstance:Se,isStop:()=>!ke.loading,request:async l=>{const{data:n}=await Is({goods_id_list:d.split(",").filter(p=>p),page:l},b);if(!n.success)return Promise.reject();const r=n.result.goods_list;r.forEach(p=>{if(p.sku_list=p.sku_list.filter(w=>w.isOnsale),!p.sku_list.length)return;const u=p.sku_list.sort((w,$)=>w.groupPrice-$.groupPrice)[0];p._skuSelect=u.skuId,p._sku_select_list=[u]});const a=n.result.total;return H.total=a,{list:r,total:a}}});ke.data=B,ke.loading=!1}function ee(b){var l,n,r,a,p,u,w;const{skuType:d}=C.value;let P=0,I=[...b.sku_list];const A=I[0].groupPrice,B=I[I.length-1].groupPrice;if(d=="1")P=b._skuSelect;else if(d=="2")P=(l=I[0])==null?void 0:l.skuId;else if(d=="3"){const $=I.filter(M=>M.groupPrice<=A);P=(n=$[Gt(0,$.length)])==null?void 0:n.skuId}else if(d=="4")P=(r=I[I.length-1])==null?void 0:r.skuId;else if(d=="5"){const $=I.filter(M=>M.groupPrice>=B);P=(a=$[Gt(0,$.length)])==null?void 0:a.skuId}else P=(p=I[Gt(0,I.length)])==null?void 0:p.skuId;return P||(P=b._skuSelect||((u=I[0])==null?void 0:u.skuId)),{sku_id:P,sku_spec:((w=I.find($=>$.skuId==P))==null?void 0:w.spec)||""}}async function ne(){const{map:b}=re.value,d=[...b.values()];if(!v.value.exec){d.forEach(B=>{B.current<B.target&&(B.row[f.notice]="停止")});return}if(!d.some(B=>!["finish","failed"].includes(B.status))){c({type:"success",msg:"检测到所有任务已完成"}),v.value.exec=!1;return}let A=30-d.reduce((B,l)=>B+l.requestCount,0);d.some(B=>{const{target:l,current:n,requestCount:r,status:a,groupId:p}=B;if(!p||a!=="loading")return!1;let u=l-n-r;const w=Math.min(u,A);return w>0&&(A-=w,de(B,w)),A<=0})}function de(b,d){if(b.taskId!==re.value.currentTaskId)return;if(!b.groupId){re.value.map.delete(b.goodsId);return}const P=E.tableList.find(u=>u.mallId==Z.mallId);let I=!1;b.requestCount+=d;const{goodsId:A,row:B,groupId:l}=b;B[f.notice]="开始下单";const n=T.address.type,{order_goods_count:r,type:a,otherSetting:p}=C.value;for(let u=0;u<d;u++)(async()=>{let $={goods_id:A,...ee(B),spell_num:r,mode:"open_group",type:a,group_id:l,shop_id:(P==null?void 0:P.mallId)||Z.mallId,goods_name:B.goods_name,shop_name:(P==null?void 0:P.mallName)||""};if(a==="pifa"&&$.spell_num<2&&c({msg:`批发下单每次购买不能小于2,已自动调整为${$.spell_num=2}`,type:"warning"}),n==="diy"){const J=await Ze();$={...$,...J}}const M=Ke(),K=M.getAccount(X.value);if(!K.status){!I&&c({msg:`小号获取失败:${K.msg},将终止执行`,type:"danger"}),v.value.exec=!1,I=!0,re.value.map.delete(A),B[f.notice]="小号获取失败"+K.msg;return}X.value=(X.value+1)%M.list.length,$.account=K.data,c({msg:"已分配小号"+K.data}),oo(le($),{showErrorMsg:!1,isStop:()=>!v.value.exec,async beforeRequest(J){J.data.anti_content=window.get_anti_content(),J.data.store_anti_content=await no(),a==="pifa"&&(J.data.singleBuy=void 0,await new Promise(async s=>{try{await Pe(async()=>{const t=[{goodsId:$.goods_id,skuId:$.sku_id,skuNum:2}];let i;if(await Promise.allSettled([ao({subScene:1,createOrderDTO:{orderType:1,createOrderItemList:t}}).then(y=>{y.success&&y.result&&(i={result:{curl:"https://mobile.yangkeduo.com/transac_order_coupon.html?_t_timestamp=transac_volume_checkout&secret_key="+y.result.secretKey,qrKey:y.result.secretKey}})}),lo({createOrderItemList:t}).then(y=>{y.success&&y.result&&y.result.curl&&y.result.qrKey&&(i=y)})]),i)J.data.singleBuy=i;else return Promise.reject()},50),s(!0)}catch(t){console.log(t),s(!0)}s(!0)})),console.log(J,"***************")}}).then(J=>{if(J.code)return Promise.reject();b.fail_consecutive=0,b.current++;const{order_sn:s}=J.data;c({msg:"下单成功!订单号:"+s,type:"success"});const t=wo();C.value.changePrice?t.changeOrderPrice(J.data,c):t.execAutoApply(J.data.order_sn,c)}).catch(J=>{c({msg:`下单失败:${J.msg}`,type:"danger"})}).finally(()=>{b.fail_consecutive++,b.failCount++,b.requestCount--,b.current>=b.target&&(B[f.notice]="完成目标",b.status="finish"),b.fail_consecutive>=5&&(b.status="failed",B[f.notice]="该任务连续失败超10次"),ne()})})()}function le(b){const d=ze(),{nameCode_active:P,nameCode_position:I,nameCode_str:A,addr_active:B,addr_position:l,addr_str:n,filterStr:r,type:a,appoint_address:p}=d.address,u={filter_address:r.replace("，",","),address_cipher:B?n:void 0,address_site:B?l:void 0,name_cipher:P?A:void 0,name_site:P?I:void 0,...b};return a==="random"&&(u.appoint_address=p||void 0),u}const re=Ie({map:new Map,currentTaskId:""});async function _e(){if(!v.value.exec)return c({msg:"检测到停止任务",type:"warning"}),Promise.reject()}async function fe(){if(!Zt())return;const b=[...L.selection];if(!b.length){c({msg:"请先选择商品",type:"warning"});return}const d=E.tableList.find(A=>A.mallId==Z.mallId);if(!d){c({msg:"请先选择店铺",type:"warning"});return}v.value.exec=!0,await _e();const P=Date.now()+"";if(re.value.currentTaskId=P,C.value.otherSetting.includes("closeInsurance"))try{so(d,{showErrorMsg:!1})}catch{}await _e();const{type:I}=T.address;if(I==="diy"&&!await Ze()){c({msg:"检测到使用本机地址下单，但没有检测到至少一个地址，请先添加地址",type:"warning"}),v.value.exec=!1;return}re.value.map.clear(),b.forEach(A=>{A[f.notice]="开始排队",re.value.map.set(A.id,{goodsId:A.id,row:A,target:C.value.num,current:0,requestCount:0,taskId:P,status:"pending",fail_consecutive:0,failCount:0})}),c({msg:"正在重置小号信息"}),await Ke().getList(),await _e(),await Fe(b,{delay:1e3,batch:20,isStop:()=>!v.value.exec,request:async A=>{const B=new Map(A.map(p=>[p.id,p]));c({msg:"开始设置供货"});const l=await fo(A.map(p=>p.id),d);if(l.code){c({msg:"供货失败："+l.msg,type:"danger"}),v.value.exec=!1;return}await _e();const{failed:n,reason:r,success:a}=l;n.forEach(p=>{const u=B.get(p);re.value.map.delete(p),u&&(u[f.notice]="供货失败",c({msg:`${p}-供货失败：${r[p]}`,type:"danger"}))}),a.size&&(c({msg:"开始获取拼团ID"}),await Fe([...a],{batch:5,isStop:()=>!v.value.exec,request:async p=>{await Promise.allSettled(p.map(async u=>{const w=B.get(u);w&&(await new Promise(async $=>{Ss({goods_id:u,sku_id:w.sku_list.filter(M=>M.skuQuantity).map(M=>M.skuId)}).then(async M=>{if(!v.value.exec){w[f.notice]="检测到停止任务",c({msg:"检测到停止任务",type:"primary"});return}if(M.data){c({msg:`${u},成功获取到拼团ID,${M.data}`,type:"success"}),w[f.notice]="成功获取到拼团ID";const K=re.value.map.get(u);K&&(K.groupId=M.data,K.status="loading")}}).catch(M=>{re.value.map.delete(u),c({msg:`${u}-获取拼团ID失败:${M.msg}`,type:"danger"}),w[f.notice]="获取拼团ID失败"}).finally(()=>$(!0))}),ne())}))},onStop:p=>{p.forEach(u=>{const w=B.get(u);w&&(w[f.notice]="检测到停止任务")})}}))}})}const te=Ae({configShow:!1,price:.01,discount:1});function ae(b){if(b&&C.value.otherSetting.includes("changePrice")){te.configShow=!0;const{pifaTargetPrice:d,othersTargetDiscount:P}=T.taskCreate;te.discount=P||1,te.price=d||.01}}function Ce(){const{discount:b,price:d}=te;T.taskCreate.othersTargetDiscount=b,T.taskCreate.pifaTargetPrice=d,te.configShow=!1}async function Y(){ke.loading=!1;const b=Object.keys(v.value);for(const d of b)v.value[d]=!1}async function k(b=L.selection){if(!b.length){c({msg:"请先选择商品",type:"warning"});return}const d=E.tableList.find(P=>P.mallId==Z.mallId);if(!d){c({msg:"请先选择店铺",type:"warning"});return}v.value.delPifa=!0,await Fe(b,{batch:1,isStop:()=>!v.value.delPifa,request:async P=>{await Promise.allSettled(P.map(async I=>{const A=await yo(d,{goodsId:I.id});if(A.success)c({msg:"删除批发成功",type:"success"}),I[f.notice]="删除批发成功";else{const B=Ee(A);c({msg:B,type:"danger"}),I[f.notice]=B}}))}}),v.value.delPifa=!1}async function z(b=L.selection){if(!b.length){c({msg:"请先选择商品",type:"warning"});return}const d=E.tableList.find(A=>A.mallId==Z.mallId);if(!d){c({msg:"请先选择店铺",type:"warning"});return}const{value:P}=await De.prompt("请输入要增加的库存","提示",{inputPattern:/^[0-9]+$/});let I=Number(P);if(Number.isNaN(I)){c({msg:"非数字",type:"warning"});return}if(I=Math.round(I),I<=0){c({msg:"请输入大于0的数字",type:"warning"});return}v.value.addStock=!0,await Fe(b,{batch:10,isStop:()=>!v.value.addStock,async request(A){const B={edit_list:[]};A.forEach(n=>{const{id:r,sku_list:a=[]}=n;a.forEach(p=>{B.edit_list.push({goods_id:r,sku_id:p.skuId,quantity_delta:I})})});const l=await $s(d,B);l.success?c({msg:"已提交修改",type:"success"}):c({msg:"修改失败"+Ee(l),type:"danger"})}}),v.value.addStock=!1}return(b,d)=>{const P=ft,I=Pt,A=pt,B=_t,l=Et,n=xt,r=ys,a=nt,p=Ct,u=gt,w=We("VxeColumn"),$=We("vxe-table"),M=yt,K=Vt,J=ks;return se(),we("div",ua,[e(p,{inline:!0},{default:o(()=>[e(I,{label:"订单数量："},{default:o(()=>[e(P,{min:1,precision:0,controls:!1,modelValue:G(C).num,"onUpdate:modelValue":d[0]||(d[0]=s=>G(C).num=s)},null,8,["modelValue"])]),_:1}),e(I,{label:"每次购买："},{default:o(()=>[e(P,{min:G(pe),precision:0,controls:!1,modelValue:G(C).order_goods_count,"onUpdate:modelValue":d[1]||(d[1]=s=>G(C).order_goods_count=s)},null,8,["min","modelValue"])]),_:1}),e(I,{label:"小号开始位置："},{default:o(()=>[e(P,{modelValue:G(X),"onUpdate:modelValue":d[2]||(d[2]=s=>mt(X)?X.value=s:null),controls:!1,min:0,precision:0,style:{width:"80px"}},null,8,["modelValue"])]),_:1}),ca,e(I,{label:"操作店铺："},{default:o(()=>[e(l,null,{default:o(()=>[e(B,{modelValue:Z.mallId,"onUpdate:modelValue":d[3]||(d[3]=s=>Z.mallId=s),disabled:v.value.exec},{default:o(()=>[(se(!0),we(je,null,Te(G(E).tableList,s=>(se(),xe(A,{label:s.mallName,value:s.mallId},null,8,["label","value"]))),256))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(I,{label:"下单类型："},{default:o(()=>[e(B,{style:{width:"170px"},modelValue:G(C).type,"onUpdate:modelValue":d[4]||(d[4]=s=>G(C).type=s),onChange:ce},{default:o(()=>[e(A,{label:"多多果园",value:"guoyuan"}),e(A,{label:"多多批发",value:"pifa"})]),_:1},8,["modelValue"])]),_:1}),e(I,{label:"sku"},{default:o(()=>[e(B,{modelValue:G(C).skuType,"onUpdate:modelValue":d[5]||(d[5]=s=>G(C).skuType=s)},{default:o(()=>[e(A,{label:"按选择",value:"1"}),e(A,{label:"按最低价",value:"2"}),e(A,{label:"最低价随机",value:"3"}),e(A,{label:"按最高价",value:"4"}),e(A,{label:"最高价随机",value:"5"}),e(A,{label:"随机",value:"6"})]),_:1},8,["modelValue"])]),_:1}),e(I,{label:"其他设置"},{default:o(()=>[e(r,{modelValue:G(C).otherSetting,"onUpdate:modelValue":d[6]||(d[6]=s=>G(C).otherSetting=s)},{default:o(()=>[e(n,{label:"follow"},{default:o(()=>[V("店铺关注")]),_:1}),e(n,{label:"collect"},{default:o(()=>[V("商品收藏")]),_:1}),e(n,{label:"closeInsurance"},{default:o(()=>[V("自动关闭运费险")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(I,{label:""},{default:o(()=>[e(l,null,{default:o(()=>[e(n,{onChange:ae,modelValue:G(C).changePrice,"onUpdate:modelValue":d[7]||(d[7]=s=>G(C).changePrice=s)},{default:o(()=>[V("自动改价")]),_:1},8,["modelValue"]),e(a,{onClick:d[8]||(d[8]=s=>te.configShow=!0)},{default:o(()=>[V("设置自动改价")]),_:1})]),_:1})]),_:1}),e(I,null,{default:o(()=>[_a]),_:1})]),_:1}),_("div",pa,[_("div",ma,[e(a,{onClick:d[9]||(d[9]=s=>G(W)("check-all"))},{default:o(()=>[V("全选")]),_:1}),e(a,{onClick:d[10]||(d[10]=s=>G(W)("reverse"))},{default:o(()=>[V("反选")]),_:1}),e(a,{onClick:d[11]||(d[11]=s=>G(W)("area-select"))},{default:o(()=>[V("区域选择")]),_:1}),e(a,{onClick:d[12]||(d[12]=s=>G(W)("clear"))},{default:o(()=>[V("取消选择")]),_:1}),e(a,{onClick:d[13]||(d[13]=s=>{Z.is_show_textarea=!0,Z.goods_id_textarea=Z.goods_id})},{default:o(()=>[V("多ID筛选")]),_:1}),e(u,{modelValue:Z.goods_id,"onUpdate:modelValue":d[14]||(d[14]=s=>Z.goods_id=s),placeholder:"商品ID搜索,逗号隔开",style:{"margin-left":"12px","margin-right":"12px",width:"110px"}},null,8,["modelValue"])]),_("div",ga,[e(a,{type:"danger",plain:"",loading:v.value.delPifa,onClick:d[15]||(d[15]=s=>k()),title:"删除供货"},{default:o(()=>[V("删除批发")]),_:1},8,["loading"]),e(a,{type:"danger",plain:"",loading:v.value.addStock,onClick:d[16]||(d[16]=s=>z()),title:"添加商品库存"},{default:o(()=>[V("添加库存")]),_:1},8,["loading"]),e(a,{type:"primary",onClick:d[17]||(d[17]=s=>q()),loading:G(ke).loading},{default:o(()=>[V("获取商品")]),_:1},8,["loading"]),e(a,{type:"success",onClick:d[18]||(d[18]=s=>fe()),loading:v.value.exec},{default:o(()=>[V("开始任务")]),_:1},8,["loading"]),e(a,{type:"warning",onClick:Y},{default:o(()=>[V("停止任务")]),_:1})])]),e($,ut({...G(ke),...G(Oe)},{ref_key:"tableRef",ref:g}),{default:o(()=>[e(w,{type:"checkbox",width:50}),e(w,{type:"seq",title:"序号",width:50}),e(w,{title:"商品ID",field:"id",width:130}),e(w,{title:"商品名称",field:"goods_name",width:150}),e(w,{title:"SKU",width:150},{default:o(s=>[e(B,{modelValue:s.row._skuSelect,"onUpdate:modelValue":t=>s.row._skuSelect=t,onClick:t=>s.row._sku_select_list=s.row.sku_list},{default:o(()=>[(se(!0),we(je,null,Te(s.row._sku_select_list,t=>(se(),xe(A,{label:t.spec||s.row.goods_name,value:t.skuId},null,8,["label","value"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue","onClick"])]),_:1}),e(w,{title:"进度",field:"progress","min-width":380,fixed:"right"},{default:o(s=>[re.value.map.has(Number(s.row.id))?(se(),xe(l,{key:0},{default:o(()=>{var t,i,y;return[_("span",fa,"已完成："+ge((t=re.value.map.get(Number(s.row.id)))==null?void 0:t.current),1),_("span",ya,"下单量："+ge((i=re.value.map.get(Number(s.row.id)))==null?void 0:i.target),1),_("span",wa,"进行中："+ge((y=re.value.map.get(Number(s.row.id)))==null?void 0:y.requestCount),1)]}),_:2},1024)):dt("",!0)]),_:1}),e(w,{title:"提示",field:f.notice,"min-width":150,fixed:"right","show-overflow":"title"},null,8,["field"]),e(w,{title:"SKU拼团价格",width:150},{default:o(s=>{var t;return[V(ge(((t=s.row.sku_list.find(i=>i.skuId==s.row._skuSelect))==null?void 0:t.groupPrice)/100),1)]}),_:1}),e(w,{title:"商品销量",field:"sold_quantity",width:80,sortable:""})]),_:1},16),e(Lt,ut(G(H),{"page-size":G(H).limit,"onUpdate:page-size":d[19]||(d[19]=s=>G(H).limit=s),"current-page":G(H).page,"onUpdate:current-page":d[20]||(d[20]=s=>G(H).page=s),selection:G(L).selection,onCurrentChange:d[21]||(d[21]=s=>q())}),null,16,["page-size","current-page","selection"]),e(M,{list:h.value,"onUpdate:list":d[22]||(d[22]=s=>h.value=s)},null,8,["list"]),e(K,{modelValue:Z.is_show_textarea,"onUpdate:modelValue":d[26]||(d[26]=s=>Z.is_show_textarea=s),title:"批量输入商品ID"},{footer:o(()=>[e(a,{onClick:d[24]||(d[24]=s=>Z.is_show_textarea=!1)},{default:o(()=>[V("取消")]),_:1}),e(a,{type:"primary",onClick:d[25]||(d[25]=s=>{Z.is_show_textarea=!1,Z.goods_id=G(hs)(Z.goods_id_textarea).join(",")})},{default:o(()=>[V("确定")]),_:1})]),default:o(()=>[e(u,{type:"textarea",modelValue:Z.goods_id_textarea,"onUpdate:modelValue":d[23]||(d[23]=s=>Z.goods_id_textarea=s),placeholder:"用逗号,换行隔开",rows:10,resize:"none"},null,8,["modelValue"])]),_:1},8,["modelValue"]),e(K,{modelValue:te.configShow,"onUpdate:modelValue":d[30]||(d[30]=s=>te.configShow=s),width:480,title:"自动改价设置"},{footer:o(()=>[e(a,{onClick:d[29]||(d[29]=s=>te.configShow=!1)},{default:o(()=>[V("取消")]),_:1}),e(a,{type:"primary",onClick:Ce},{default:o(()=>[V("确定")]),_:1})]),default:o(()=>[e(J,{type:"warning",closable:!1},{default:o(()=>[va,ha,ka]),_:1}),e(p,{"label-position":"top"},{default:o(()=>[e(I,{label:"批发下单"},{default:o(()=>[e(l,null,{default:o(()=>[e(P,{modelValue:te.price,"onUpdate:modelValue":d[27]||(d[27]=s=>te.price=s),min:.01,precision:2,controls:!1},null,8,["modelValue","min"]),V(" 元 ")]),_:1})]),_:1}),e(I,{label:"其他"},{default:o(()=>[e(l,null,{default:o(()=>[e(P,{modelValue:te.discount,"onUpdate:modelValue":d[28]||(d[28]=s=>te.discount=s),min:1,precision:0,controls:!1},null,8,["modelValue"]),V(" 折 ")]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),Sa=it(ba,[["__scopeId","data-v-bd86d088"]]),vt=ie=>(at("data-v-e864c2e8"),ie=ie(),lt(),ie),$a={class:"global-buy"},Ia={class:"config"},Va={class:"item"},Ca=vt(()=>_("div",{class:"label"},"店铺：",-1)),Pa={class:"item"},xa=vt(()=>_("div",{class:"label"},"小号位置：",-1)),La={class:"item",title:"开启后会自动添加选择sku的库存"},Ua=vt(()=>_("div",{class:"label"},"自动添加库存:",-1)),Aa={class:"item"},ja=vt(()=>_("div",{class:"label"},"数量：",-1)),Ta={class:"item"},Ea=vt(()=>_("div",{class:"label"},"每单最大销量：",-1)),Ra={class:"table-header between-flex"},Ga={class:"center-flex"},Ma={class:"center-flex"},Na=et({__name:"GlobalBuy",setup(ie){const oe=ct(),X=Ae({goods_id:"",goods_id_textarea:"",is_show_textarea:!1,mallId:""}),f=Ae({num_type:1,goods_num:1e3,goods_num_range:{v1:100,v2:1e3},order_per_max_count:1811,auto_add_stock:!0}),T=qe(1,"start_site"),C=Ie([]),Z=Ie(new Map),v=(Y,k)=>{if(C.value.push({date:Date.now(),msg:"",...Y}),k){const z=Y.msg||"";Z.value.has(k.id)?(Z.value.get(k.id).msg=z,Z.value.get(k.id).type=Y.type):Z.value.set(k.id,{msg:z,type:Y.type})}},pe=Ie(),h=Xt(400,{id:"mallTask-global-table"},{tableRef:pe,pagination:{limit:100,pageSizes:[100,1e3,2e3]},clearWhenDataChange:!1}),{tableProps:c,tableEvents:ce,pagination:g,tableState:Se,tableAction:ke}=h;async function Oe(){const Y=oe.tableList.find(I=>I.mallId==X.mallId);if(!Y){ye.warning("请先选择店铺");return}if(!oe.checkAvailable(Y)){ye.warning("店铺不可用");return}const{goods_id:k}=X,{limit:z,page:b}=g,d=Math.round(z/100);c.loading=!0,k&&(g.page=1);const P=await $t({delay:1e3,pageStart:(b-1)*d+1,pageEnd:b*d,tableInstance:h,isStop:()=>!c.loading,request:async I=>{const{data:A}=await Is({goods_id_list:k.split(",").filter(n=>n),page:I},Y);if(!A.success)return Promise.reject();const B=A.result.goods_list;B.forEach(n=>{if(n.sku_list=n.sku_list.filter(a=>a.isOnsale),!n.sku_list.length)return;const r=n.sku_list.sort((a,p)=>a.groupPrice-p.groupPrice)[0];n._skuSelect=r.skuId,n._sku_select_list=[r]});const l=A.result.total;return g.total=l,{list:B,total:l}}});c.data=P,c.loading=!1}const H=Ae({exec:!1,stop:!1});function L(){H.stop=!0,v({msg:"已发送停止指令",type:"danger"})}async function W(){if(H.stop)return v({msg:"检测到停止指令",type:"warning"}),Promise.reject()}async function E(){const{mallId:Y}=X,k=oe.tableList.find(b=>b.mallId==Y);if(!k){ye.warning({message:"请选择店铺",grouping:!0});return}if(!oe.checkAvailable(k))return ye.warning({message:"当前店铺已过期，请重新登录",grouping:!0});const z=[...Se.selection];if(!z.length){ye.warning({message:"请选择商品",grouping:!0});return}H.exec=!0,H.stop=!1,await Fe(z,{batch:1,isStop:()=>!H.exec&&H.stop,async request([b]){const d=q(),P={row:b,mall:k,num:d,...ee(d),price_info:{goods_id:b.id,market_price:b.market_price,market_price_in_yuan:(Number(b.market_price)/100).toFixed(2),sku_prices:b.sku_list.filter(I=>I.isOnsale).map(I=>({sku_id:I.skuId,single_price:I.normalPrice,single_price_in_yuan:(Number(I.normalPrice)/100).toFixed(2),multi_price:I.groupPrice,multi_price_in_yuan:(Number(I.groupPrice)/100).toFixed(2)}))},accounts:[]};if(de(P),!P.accounts.length){v({msg:"没有分配到小号",type:"warning"},b);return}if(await W(),f.auto_add_stock){v({msg:"自动添加库存",type:"success"},b);const I=await $s(k,{edit_list:[{goods_id:b.id,sku_id:b._skuSelect,quantity_delta:P.num}]});I.success?(v({msg:"添加库存成功",type:"success"},b),v({msg:"等待添加库存同步数据",type:"info"},b),await Ve(10*1e3)):v({msg:"添加库存失败"+Ee(I)+"(添加失败不暂停，继续执行)",type:"danger"},b)}if(await W(),await te(P),await W(),!!(await le(P,"toHighPrice")).success){try{await _e(P)}catch{le(P,"recover");return}if(!(await le(P,"toLowPrice")).success){le(P,"recover"),fe(P);return}await ae(P),fe(P),le(P,"recover")}}}),H.exec=!1,H.stop=!1,v({msg:"执行完毕",type:"primary"})}function q(){const{goods_num:Y,goods_num_range:{v1:k,v2:z},num_type:b}=f;return b===2?Be(k,z):Y}function ee(Y){let k={num_per_order:0,price_per_order:0,max_num_per_order:9505};const z=Number(f.order_per_max_count);return z==359?(k.max_num_per_order=359,k.price_per_order=5.5):z===9505?(k.price_per_order=.21,k.max_num_per_order=9505):z===3845?(k.price_per_order=.51,k.max_num_per_order=3845):z===200001?(k.price_per_order=.01,k.max_num_per_order=200001):z===1811&&(k.price_per_order=1.1,k.max_num_per_order=1811),Y<=100?k.num_per_order=101:Y>=k.max_num_per_order?k.num_per_order=k.max_num_per_order:k.num_per_order=Y,k}const ne=Ke();function de(Y){const k=Y.num;let z=Y.max_num_per_order;const b=Math.ceil(k/z);let d=[];v({msg:"需要可用小号次数："+b});for(let P=0;P<b;P++){const I=ne.getAccount(T.value-1);if(I.status){d.push(I.data);const A=(I.ind+2)%ne.list.length;T.value=A===0?ne.list.length:A}else{v({type:"danger",msg:"获取小号出错"+I.msg},Y.row);break}}d.length<b&&(v({type:"danger",msg:"没有获取到足够小号"}),d.forEach(P=>{ne.recover(String(P))}),d=[]),Y.accounts=d}async function le(Y,k){const z={success:!1,msg:"初始化",needCheckPrice:k!=="recover"};let b;switch(k){case"toHighPrice":{const d=Math.min(...Y.price_info.sku_prices.map(A=>A.multi_price));if(d>4e3*100){v({type:"danger",msg:"当前商品sku全部高于4000，不需要提高价格"},Y.row),z.needCheckPrice=!1;break}let I={goods_id:Y.price_info.goods_id,market_price_in_yuan:"5002.00",market_price:500200,sku_prices:Y.price_info.sku_prices.map(A=>({sku_id:A.sku_id,multi_price_in_yuan:"4000",single_price_in_yuan:"5001.00",multi_price:4e5,single_price:500100}))};if(d<200*100){const A=It.cloneDeep(I);A.market_price=211*100,A.market_price_in_yuan="211.00",A.sku_prices.forEach(B=>{B.multi_price=200*100,B.multi_price_in_yuan="200.00",B.single_price=210*100,B.single_price_in_yuan="210.00"}),await Je(Y.mall,A),await Ve(15e3)}b=await Je(Y.mall,I);break}case"toLowPrice":{const d=bs.find(P=>P.multi_price===Math.round(Y.price_per_order*100));if(!d)z.success=!1,z.msg="--修改价格失败，没有找到修改价格参数";else{const{market_price_in_yuan:P,market_price:I,multi_price:A,multi_price_in_yuan:B,single_price:l,single_price_in_yuan:n}=d,r={goods_id:Y.price_info.goods_id,market_price_in_yuan:P,market_price:I,sku_prices:Y.price_info.sku_prices.map(a=>({sku_id:a.sku_id,multi_price_in_yuan:B,single_price_in_yuan:n,multi_price:A,single_price:l}))};b=await Je(Y.mall,r)}break}case"recover":{b=await Je(Y.mall,It.cloneDeep(Y.price_info));break}}if(b){const d=await b;d.success?z.success=!0:(z.success=!1,z.msg="提交修改失败"+Ee(d))}return v({msg:z.success?"提交修改价格成功":z.msg,type:z.success?"success":"danger"},Y.row),z.success&&z.needCheckPrice&&(await re(Y,k)?v({msg:"价格检测通过",type:"success"},Y.row):(v({msg:"价格检测未通过",type:"danger"},Y.row),z.success=!1)),z}async function re(...Y){const[k,z]=Y,{row:b,mall:d}=k,P=k.price_info.goods_id;return v({msg:"检查商品sku价格",type:"info"},b),await Ve(2e3),Pe(async I=>{var B,l,n,r;v({msg:`检查商品价格第${I}次`,type:"info"});const A=await Ot(d,{goods_id_list:[String(P)],pre_sale_type:4});if(!A.success)return v({msg:`获取商品信息失败${A.error_msg||""}`,type:"danger"}),Promise.reject(!1);if(A&&A.result&&A.result.total){const a=A.result.goods_list[0];if(!a.sku_list)return Promise.reject(!1);const p=(l=(B=a.sku_list)==null?void 0:B[0])==null?void 0:l.groupPrice;switch(z){case"toHighPrice":{if(p>=4e3*100)return!0;break}case"recover":{if(p>=Math.min(...k.price_info.sku_prices.map(u=>u.multi_price)))return!0;break}case"toLowPrice":{if(p<=550)return!0;break}}}if(I%3===0){const a=await Bt(d,[String(P)]);if(a&&a.success&&((n=a.result)!=null&&n.total)){const p=a.result.list;return v({msg:"检测到临时商品被驳回："+((r=p[0])==null?void 0:r.reject_comment),type:"danger"},b),!1}}return Promise.reject(!1)},15,3e3).then(I=>I?(v({msg:"商品价格已更改",type:"success"},b),!0):Promise.reject(!1)).catch(()=>!1)}async function _e(Y){const{mall:k,row:z,num_per_order:b,price_per_order:d}=Y,P="商品立减券"+Date.now();let I=b*d;Number.isInteger(I)&&Y.num_per_order++,I=Y.num_per_order*d;let A=Math.floor(I)*100;A>2e5&&(A=2e5);const B=Y.price_info.goods_id,l=[{goods_id:B,batch_desc:P,batch_start_time:Xe().startOf("day").valueOf(),batch_end_time:Xe().endOf("day").valueOf(),discount:A,init_quantity:1e5,user_limit:10}],n=await ws(k,l);if(n.success)if(n.result.has_error_msg){let r=!1;if(n.result.error_msg_list.forEach(a=>{v({msg:`创建优惠券失败:${a.error_msg}`,type:"danger"},z),r=!0}),r)return Promise.reject()}else v({msg:"提交创建优惠券成功",type:"success"},z);else{const r=n.error_msg||n.errorMsg||"";return v({msg:`创建优惠券失败:${r}`,type:"danger"},z),Promise.reject()}v({msg:"准备获取优惠券信息",type:"info"}),await Ve(5*1e3),await Pe(async r=>{var w;v({msg:`获取优惠券列表第${r}次--`});const u=(await Yt(k,{goods_list:[String(B||"")],batch_status:1})).result.data_list.find($=>$.batch_desc===P);return u?(Y.coupon_info={coupon_code:u.batch_sn,coupon_batch_id:u.batch_id},v({msg:"成功获取到优惠券码"+((w=Y.coupon_info)==null?void 0:w.coupon_code),type:"success"},z),!0):Promise.reject(!1)},30,10*1e3).catch(()=>(v({msg:"没有获取到优惠券码",type:"danger"},z),Promise.reject()))}async function fe(Y){const{mall:k,coupon_info:{coupon_batch_id:z}={}}=Y;if(k&&z)return Dt(k,{batch_id:z}).then(b=>{b.success?v({msg:"成功关闭优惠券领取",type:"success"}):v({msg:`关闭优惠券领取失败${b.error_msg||b.errorMsg||"-"}`,type:"danger"})})}async function te(Y){const{mall:k,row:z,price_info:{sku_prices:b}}=Y;return Ss({goods_id:z.id,sku_id:b.map(d=>d.sku_id)},void 0,k).then(d=>{v({msg:`成功获取拼团id:${d.data}`,type:"success"},z),Y.group_id=d.data}).catch(d=>(v({msg:`获取拼团id失败${d.msg}`,type:"danger"},z),Promise.reject()))}async function ae(Y){const{mall:k,row:z,group_id:b,num_per_order:d,accounts:P=[],coupon_info:I}=Y;if(!P.length)return fe(Y),Promise.reject({msg:"没有小号"});if(!b)return fe(Y),Promise.reject({msg:"没有拼团id"});const A=new Map;P.forEach(n=>{const r=String(n);A.has(r)?A.get(r).count++:A.set(r,{account:n,count:1})}),await W(),await le(Y,"toLowPrice");const B=z.sku_list,l=B.find(n=>n.sku_id==z._skuSelect)||B[0];await Fe([...A.values()],{batch:25,request:async n=>{await Promise.allSettled(n.map(async r=>{const a=new Array(r.count).fill(r.account);await Fe(a,{batch:1,request:async p=>{const u=p[0],w=await Ce(u,Y),$={account:u,sku:l.skuId,sku_spec:l.spec,shop_id:k.mallId,shop_name:k.mallName,goods_id:z.id,num:d,mode:"open_group",goods_name:z.goods_name,group_id:b,type:"globalBuy",use_coupon:!0,...w};await Pe(async()=>vs({coupon_code:I.coupon_code,account:u},{showErrorMsg:!1}),5,2e3).then(M=>{const K=M.data&&M.data.promotion_identity_vo;if(!K)return Promise.reject({msg:"promotion_identity_vo不存在"});const{promotion_id:J,promotion_type:s,extension:t}=K;$.single_promotion_list=[{promotion_id:J,promotion_type:s,sku_ids:[z._skuSelect],mall_id:k.mallId}],v({msg:`${u}领取优惠券成功`,type:"success"},z)}).catch(M=>(console.log(M,"getMerchantCouponV2 -error"),v({msg:`${u}领取优惠券失败：${M.msg}`,type:"danger"},z),Promise.reject())),await Pe(async()=>At($,{showErrorMsg:!1}).then(M=>{v({msg:`${$.goods_name}:下单成功。订单号:${M.data.order_sn}`,type:"success"})}),3).catch(M=>{v({msg:`${$.goods_name}:下单失败:${M.msg}`,type:"danger"})})}})}))}})}async function Ce(Y,k){const z=ze(),{nameCode_active:b,nameCode_position:d,nameCode_str:P,addr_active:I,addr_position:A,addr_str:B,filterStr:l,type:n,appoint_address:r}=z.address;let a={appoint_address:r||void 0};if(n=="diy"){const p=await Ze();Reflect.deleteProperty(p,"id"),a=p}else a={...a,filter_address:l.replace("，",","),address_cipher:I?B:void 0,address_site:I?A:void 0,name_cipher:b?P:void 0,name_site:b?d:void 0};return Ut({account:Y,...a},{showErrorMsg:!1}).then(p=>p.data).catch(p=>(Ke().recover(Y),v({msg:"添加地址出错:"+p.msg,type:"danger"},k.row),Promise.reject()))}return(Y,k)=>{const z=pt,b=_t,d=ft,P=gs,I=nt,A=gt,B=We("VxeColumn"),l=We("vxe-table"),n=yt,r=Vt;return se(),we("div",$a,[_("div",Ia,[_("div",Va,[Ca,e(b,{modelValue:X.mallId,"onUpdate:modelValue":k[0]||(k[0]=a=>X.mallId=a),placeholder:"请选择店铺"},{default:o(()=>[(se(!0),we(je,null,Te(G(oe).tableList,a=>(se(),xe(z,{key:a.mallId,label:a.mallName,value:a.mallId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_("div",Pa,[xa,e(d,{controls:!1,modelValue:G(T),"onUpdate:modelValue":k[1]||(k[1]=a=>mt(T)?T.value=a:null),precision:0,min:1},null,8,["modelValue"])]),_("div",La,[Ua,e(P,{modelValue:f.auto_add_stock,"onUpdate:modelValue":k[2]||(k[2]=a=>f.auto_add_stock=a)},null,8,["modelValue"])]),_("div",Aa,[ja,e(b,{modelValue:f.num_type,"onUpdate:modelValue":k[3]||(k[3]=a=>f.num_type=a),style:{width:"100px"}},{default:o(()=>[e(z,{label:"固定数量",value:1}),e(z,{label:"范围随机",value:2})]),_:1},8,["modelValue"]),Me(e(d,{controls:!1,modelValue:f.goods_num,"onUpdate:modelValue":k[4]||(k[4]=a=>f.goods_num=a),precision:0,min:1},null,8,["modelValue"]),[[Ne,f.num_type==1]]),Me(e(d,{controls:!1,modelValue:f.goods_num_range.v1,"onUpdate:modelValue":k[5]||(k[5]=a=>f.goods_num_range.v1=a),precision:0,min:1},null,8,["modelValue"]),[[Ne,f.num_type==2]]),Me(e(d,{controls:!1,modelValue:f.goods_num_range.v2,"onUpdate:modelValue":k[6]||(k[6]=a=>f.goods_num_range.v2=a),precision:0,min:1},null,8,["modelValue"]),[[Ne,f.num_type==2]])]),_("div",Ta,[Ea,e(b,{modelValue:f.order_per_max_count,"onUpdate:modelValue":k[7]||(k[7]=a=>f.order_per_max_count=a),style:{width:"100px"}},{default:o(()=>[e(z,{label:"9505",value:9505}),e(z,{label:"3845",value:3845}),e(z,{label:"1811(1.1)",value:1811}),e(z,{label:"359(5.5)",value:359})]),_:1},8,["modelValue"])])]),_("div",Ra,[_("div",Ga,[e(I,{onClick:k[8]||(k[8]=a=>G(ke)("check-all"))},{default:o(()=>[V("全选")]),_:1}),e(I,{onClick:k[9]||(k[9]=a=>G(ke)("reverse"))},{default:o(()=>[V("反选")]),_:1}),e(I,{onClick:k[10]||(k[10]=a=>G(ke)("area-select"))},{default:o(()=>[V("区域选择")]),_:1}),e(I,{onClick:k[11]||(k[11]=a=>G(ke)("clear"))},{default:o(()=>[V("取消选择")]),_:1}),e(I,{onClick:k[12]||(k[12]=a=>{X.is_show_textarea=!0,X.goods_id_textarea=X.goods_id})},{default:o(()=>[V("多ID筛选")]),_:1}),e(A,{modelValue:X.goods_id,"onUpdate:modelValue":k[13]||(k[13]=a=>X.goods_id=a),placeholder:"商品ID搜索,逗号隔开",style:{"margin-left":"12px","margin-right":"12px",width:"110px"}},null,8,["modelValue"])]),_("div",Ma,[e(I,{type:"primary",onClick:k[14]||(k[14]=a=>Oe()),loading:G(c).loading},{default:o(()=>[V("获取商品")]),_:1},8,["loading"]),e(I,{type:"success",onClick:k[15]||(k[15]=a=>E()),loading:H.exec},{default:o(()=>[V("开始任务")]),_:1},8,["loading"]),e(I,{type:"warning",onClick:k[16]||(k[16]=a=>L()),disabled:H.stop},{default:o(()=>[V("停止任务")]),_:1},8,["disabled"])])]),e(l,ut({...G(c),...G(ce)},{ref_key:"tableRef",ref:pe}),{default:o(()=>[e(B,{type:"checkbox",width:50}),e(B,{type:"seq",title:"序号",width:50}),e(B,{title:"商品ID",field:"id",width:130}),e(B,{title:"商品名称",field:"goods_name",width:150}),e(B,{title:"SKU",width:150},{default:o(a=>[e(b,{modelValue:a.row._skuSelect,"onUpdate:modelValue":p=>a.row._skuSelect=p,onClick:p=>a.row._sku_select_list=a.row.sku_list},{default:o(()=>[(se(!0),we(je,null,Te(a.row._sku_select_list,p=>(se(),xe(z,{label:p.spec||a.row.goods_name,value:p.skuId},null,8,["label","value"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue","onClick"])]),_:1}),e(B,{title:"SKU拼团价格",width:150},{default:o(a=>{var p;return[V(ge(((p=a.row.sku_list.find(u=>u.skuId==a.row._skuSelect))==null?void 0:p.groupPrice)/100),1)]}),_:1}),e(B,{title:"商品销量",field:"sold_quantity",width:80,sortable:""}),e(B,{title:"提示",field:"notice"})]),_:1},16),e(Lt,ut(G(g),{"page-size":G(g).limit,"onUpdate:page-size":k[17]||(k[17]=a=>G(g).limit=a),"current-page":G(g).page,"onUpdate:current-page":k[18]||(k[18]=a=>G(g).page=a),selection:G(Se).selection,onCurrentChange:k[19]||(k[19]=a=>Oe())}),null,16,["page-size","current-page","selection"]),e(n,{list:C.value,"onUpdate:list":k[20]||(k[20]=a=>C.value=a),style:{"margin-top":"12px"}},null,8,["list"]),e(r,{modelValue:X.is_show_textarea,"onUpdate:modelValue":k[24]||(k[24]=a=>X.is_show_textarea=a),title:"批量输入商品ID"},{footer:o(()=>[e(I,{onClick:k[22]||(k[22]=a=>X.is_show_textarea=!1)},{default:o(()=>[V("取消")]),_:1}),e(I,{type:"primary",onClick:k[23]||(k[23]=a=>{X.is_show_textarea=!1,X.goods_id=G(hs)(X.goods_id_textarea).join(",")})},{default:o(()=>[V("确定")]),_:1})]),default:o(()=>[e(A,{type:"textarea",modelValue:X.goods_id_textarea,"onUpdate:modelValue":k[21]||(k[21]=a=>X.goods_id_textarea=a),placeholder:"用逗号,换行隔开",rows:10,resize:"none"},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),Fa=it(Na,[["__scopeId","data-v-e864c2e8"]]),za={class:"mall-task"},Oa={class:"mall-task-content"},ol=et({__name:"mallTask",setup(ie){const oe=Ie("order"),X=[{label:"活动",key:"activity",component:rt(an)},{label:"赠品",key:"gift",component:rt(Zn)},{label:"快速下单",key:"order",component:rt(Sa)},{label:"全球购",key:"globayBuy",component:rt(Fa)},{label:"0.1折改销量",key:"CSLimit",component:rt(da)}],f=jt(()=>X.find(T=>T.key===oe.value));return(T,C)=>{var pe;const Z=Wt,v=Tt;return se(),we("div",za,[e(v,{modelValue:oe.value,"onUpdate:modelValue":C[0]||(C[0]=h=>oe.value=h),style:{"margin-bottom":"10px"},size:"default"},{default:o(()=>[(se(),we(je,null,Te(X,h=>e(Z,{label:h.key,key:h.key},{default:o(()=>[V(ge(h.label),1)]),_:2},1032,["label"])),64))]),_:1},8,["modelValue"]),_("div",Oa,[(se(),xe(ro,null,[(se(),xe(io((pe=G(f))==null?void 0:pe.component)))],1024))])])}}});export{ol as default};
