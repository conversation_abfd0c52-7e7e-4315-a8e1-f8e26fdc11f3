import{d as x,y as I,b as E,o as y,h as _,i as t,k as a,P as k,j as l,H as p,e as n,J as N,a8 as S,M as g,X as d,a2 as v,a3 as w,m as C}from"./index-CmETSh6Y.js";import{E as B}from"./el-space-mudxGYIP.js";const f=c=>(v("data-v-e3d3bcf1"),c=c(),w(),c),F={class:"app-info"},M={class:"box"},P=f(()=>_("p",{class:"title"},"应用信息",-1)),T=f(()=>_("br",null,null,-1)),U=x({__name:"appInfo",setup(c){const e=I();async function m(){const{value:i}=await d.prompt("请输入修正值（毫秒）"),o=Number(i);Number.isNaN(o)||(e.time_diff=o)}async function h(){const{value:i}=await d.prompt("请输入请求地址",{title:"修改请求地址",inputPattern:/^http/,inputErrorMessage:"请输入合法的请求地址"}),o=i.trim();e.currentUrl=o;const s=e.urls.findIndex(u=>u===o);s>=0?e.urlIndex=s:(e.urls.push(o),e.urlIndex=e.urls.length-1)}return(i,o)=>{const s=N,u=B,r=k,b=g;return y(),E("div",F,[_("div",M,[P,t(b,{inline:!0,"label-postion":"top","label-width":"100px"},{default:a(()=>[t(r,{label:"系统时间差值"},{default:a(()=>[t(u,null,{default:a(()=>[l(p(n(e).time_diff)+"毫秒 ",1),t(s,{underline:!1,onClick:m,type:"primary"},{default:a(()=>[l("修正")]),_:1})]),_:1})]),_:1}),t(r,{label:"应用路径"},{default:a(()=>[l(p(n(e).appInfo.appPath),1)]),_:1}),T,t(r,{label:"当前请求地址"},{default:a(()=>[t(u,null,{default:a(()=>[_("span",null,p(n(e).currentUrl),1),t(s,{underline:!1,onClick:h,type:"primary"},{default:a(()=>[l("手动修改")]),_:1})]),_:1})]),_:1}),t(r,{label:"当前频繁次数"},{default:a(()=>[l(p(n(e).urlErrorCount),1)]),_:1}),t(r,{label:"最新频繁时间"},{default:a(()=>[l(p(n(S)(n(e).urlErrorTime)),1)]),_:1})]),_:1})])])}}}),j=C(U,[["__scopeId","data-v-e3d3bcf1"]]);export{j as default};
