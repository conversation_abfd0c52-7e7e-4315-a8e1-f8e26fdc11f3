import{C as t}from"./index-CmETSh6Y.js";function i(e){return t.invoke("controller.address.getList",e)}function c(e){if(!e.length)return Promise.reject("没有数据");const n=e[0],r=Object.keys(n).join(","),o=e.map(d=>`(${Object.values(d).map(l=>`'${l}'`).join(",")})`).join(",");return t.invoke("controller.address.multipleAdd",{keys:r,values:o})}function a(e){return t.invoke("controller.address.delSelected",e)}function u(){return t.invoke("controller.address.delAddrAll")}function m(){return t.invoke("controller.address.getRandomItem")}export{a,m as b,u as d,i as g,c as m};
