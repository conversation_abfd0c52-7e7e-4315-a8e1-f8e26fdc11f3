import{_ as C,d as f,u as Se,a as $,c as W,b as v,o as m,r as E,n as I,e as n,f as R,w as D,g as be,h as o,i as s,j as S,E as X,k as i,l as ye,m as Z,p as Ce,q as B,s as Y,t as j,v as $e,x as Ee,y as Ie,z as Ve,A as O,B as xe,C as y,D as Ue,F as Ae,G as Be,H as w,I as Ne,J as We,K as De,L as Le,M as Me,N as Pe,O as He,P as Re,Q as J,R as Q,S as ze,T as H,U as Fe,V as Te,W as qe,X as Ge,Y as Ke,Z as N,$ as Ye,a0 as je,a1 as Oe,a2 as Je,a3 as Qe,a4 as Xe}from"./index-CmETSh6Y.js";import{E as Ze,a as et}from"./el-tab-pane-CjzQDPCe.js";import{E as tt}from"./el-input-number-V2-U9i7h.js";import{E as st}from"./el-space-mudxGYIP.js";import{u as ee,a as nt}from"./current-Auvi1tS_.js";import{d as ot}from"./dayjs.min-Bj8ADfnT.js";import{c as at}from"./autoApply-izhrV0Ni.js";import{c as lt}from"./extra-BEOWe5WS.js";const rt=f({name:"ElContainer"}),it=f({...rt,props:{direction:{type:String}},setup(p){const a=p,u=Se(),d=$("container"),l=W(()=>a.direction==="vertical"?!0:a.direction==="horizontal"?!1:u&&u.default?u.default().some(_=>{const V=_.type.name;return V==="ElHeader"||V==="ElFooter"}):!1);return(k,_)=>(m(),v("section",{class:I([n(d).b(),n(d).is("vertical",n(l))])},[E(k.$slots,"default")],2))}});var ct=C(it,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/container.vue"]]);const ut=f({name:"ElAside"}),pt=f({...ut,props:{width:{type:String,default:null}},setup(p){const a=p,u=$("aside"),d=W(()=>a.width?u.cssVarBlock({width:a.width}):{});return(l,k)=>(m(),v("aside",{class:I(n(u).b()),style:R(n(d))},[E(l.$slots,"default")],6))}});var te=C(pt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/aside.vue"]]);const dt=f({name:"ElFooter"}),_t=f({...dt,props:{height:{type:String,default:null}},setup(p){const a=p,u=$("footer"),d=W(()=>a.height?u.cssVarBlock({height:a.height}):{});return(l,k)=>(m(),v("footer",{class:I(n(u).b()),style:R(n(d))},[E(l.$slots,"default")],6))}});var se=C(_t,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/footer.vue"]]);const mt=f({name:"ElHeader"}),ft=f({...mt,props:{height:{type:String,default:null}},setup(p){const a=p,u=$("header"),d=W(()=>a.height?u.cssVarBlock({height:a.height}):{});return(l,k)=>(m(),v("header",{class:I(n(u).b()),style:R(n(d))},[E(l.$slots,"default")],6))}});var ne=C(ft,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/header.vue"]]);const ht=f({name:"ElMain"}),gt=f({...ht,setup(p){const a=$("main");return(u,d)=>(m(),v("main",{class:I(n(a).b())},[E(u.$slots,"default")],2))}});var oe=C(gt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/main.vue"]]);const vt=be(ct,{Aside:te,Footer:se,Header:ne,Main:oe}),kt=D(te);D(se);const wt=D(ne),St=D(oe),bt={class:"browser-view"},yt={class:"controls"},Ct=f({__name:"BrowserView",setup(p){const a=ee();async function u(){a.changeUrl("https://mobile.pinduoduo.com/login.html",!0)}return(d,l)=>{const k=X;return m(),v("div",bt,[o("div",yt,[o("div",{class:"item",onClick:l[0]||(l[0]=_=>n(a).changeUrl("home"))},[s(k,{class:"m-r-5"},{default:i(()=>[s(n(ye))]),_:1}),S("首页 ")]),o("div",{class:"item",onClick:l[1]||(l[1]=_=>n(a).changeUrl("back"))}," 后退 "),o("div",{class:"item",onClick:l[2]||(l[2]=_=>n(a).changeUrl("next"))}," 前进 "),o("div",{class:"item",onClick:l[3]||(l[3]=_=>n(a).changeUrl("reload"))}," 刷新 "),o("div",{class:"item",onClick:u}," 退出并切换账号 ")])])}}}),$t=Z(Ct,[["__scopeId","data-v-b4338656"]]),Et=p=>(Je("data-v-f5b340f0"),p=p(),Qe(),p),It={class:"home drag-able"},Vt={class:"left"},xt={class:"version"},Ut={class:"user-name"},At={class:"past-time"},Bt={class:"right"},Nt={class:"notice drag-able"},Wt={class:"infos"},Dt={class:"ctrl"},Lt=["onClick"],Mt={style:{"margin-top":"20px"}},Pt=Et(()=>o("span",{style:{"margin-left":"10px"}},"%",-1)),Ht={class:"home-tabs-label"},Rt={class:"label-name"},zt=f({__name:"Home",setup(p){Ce();async function a(){const{value:t}=await Ge.prompt("请输入","提示",{showCancelButton:!0,inputType:"password"})}u();function u(){const t=()=>{const e=b.tableList.map(c=>({shop_name:c.mallName,shop_id:c.mallId.toString(),shop_token:c.cookieJSON||"",shop_sturt:c.status.toString(),shop_time:c.update_time})),g=O();g.shopDataForAttack=e,Ke().then(c=>{c.data&&c.data.length&&y.invoke("controller.aRequest.add",c.data)}).finally(async()=>{await N(6e4),t()})};t()}const d=B();d.getOrderSettingAll(),Y(()=>d.pay.winCount,t=>{t&&(at(t),Xe(t))},{immediate:!0});const l=j({active:"index",list:$e.find(t=>t.name==="home").children.filter(t=>{var e;return!t.redirect&&!((e=t.meta)!=null&&e.hiddenInTab)})}),k=Ee();Y(()=>k.name,t=>{l.active=t},{immediate:!0});const _=Ie();function V(){const{appHeight:t,appWidth:e,scale:g}=B().appWindow;Ye({scale:g,height:t,width:e})}V(),Ve();const x=O();x.refreshUserInfo();let ae=setInterval(()=>{x.refreshUserInfo()},1e3*45);xe(()=>{clearInterval(ae)});const le=ee();function z(t,e){le.url=e}y.off("controller.pddWindow.updateUrl",z),y.on("controller.pddWindow.updateUrl",z);const F=nt();y.off("controller.pddWindow.currentGoodsDetailsUpdate",L),y.on("controller.pddWindow.currentGoodsDetailsUpdate",L);function L(t,e){return e?new Promise((g,c)=>{try{F.infos=JSON.parse(e),g(!0)}catch(U){throw c(),U}}):(F.infos=null,Promise.reject())}Ue().then(t=>{L("",t)});const b=Ae();b.getList();const re=()=>{const t=b.tableList.map(async e=>{var g;if(b.checkAvailable(e)){const c=await Oe(e);return console.log(c),(g=c==null?void 0:c.result)!=null&&g.login||b.disabledStore(e),c}else return Promise.reject()});return Promise.allSettled(t)};function T(){re().finally(async()=>{await N(60*1e3),T()})}T();const h=j({scale:100,dialog:!1,quickList:[.75,1,1.5]});function ie(){const e=B().appWindow.scale;h.scale=e>2?e:e*100,h.dialog=!0}function ce(t){h.scale=t,M()}async function M(){const t=B();t.appWindow.scale=h.scale,h.dialog=!1}function q(){lt().then(async t=>{t.code>=0&&(je({remark:t.exe}).then(e=>{console.log(e,"userRisk")}).catch(e=>{console.log(e,"userRisk error")}),await N(3e3),_.closeApp(!1)),await N(5e3),q()})}return q(),(t,e)=>{const g=X,c=We,U=st,A=De,ue=wt,pe=tt,de=Re,_e=Me,G=ze,me=Le,fe=et,he=Ze,ge=Be("router-view"),ve=Fe,ke=St,we=kt,K=vt;return m(),v("div",It,[s(K,null,{default:i(()=>[s(ue,{class:"home-header drag-enable"},{default:i(()=>[o("div",Vt,[s(U,null,{default:i(()=>[o("span",{class:"app-name",onDblclick:a},"CK助手内测版",32),o("span",xt,"v"+w(n(_).appInfo.appVersion),1),o("span",Ut,[s(g,{class:"m-l-5"},{default:i(()=>[s(n(Ne))]),_:1}),S(w(n(x).user_info.username),1)]),o("span",At,"有效期:"+w(n(ot)(n(x).app_info.past_time*1e3).format("YYYY-MM-DD")||"-"),1),s(c,{onClick:e[0]||(e[0]=r=>n(_).renewal()),class:"m-l-5",underline:!1,style:{color:"#fff"}},{default:i(()=>[S("续期")]),_:1})]),_:1})]),o("div",Bt,[o("div",Nt,[s(A,{href:"icon-notice1",style:{color:"#fff"}}),S(" "+w(n(_).info.app.notice),1)]),o("div",Wt,[o("div",Dt,[o("div",{class:"item",onClick:ie},[s(A,{href:"icon-ic_sharp-settings"})]),o("div",{class:"item",onClick:e[1]||(e[1]=r=>n(_).minimize())},[s(A,{href:"icon-minus"})]),o("div",{class:"item",onClick:e[2]||(e[2]=r=>n(_).closeApp(!0))},[s(A,{href:"icon-close"})])])])])]),_:1}),s(me,{title:"应用设置",width:480,class:"dialog-480",modelValue:h.dialog,"onUpdate:modelValue":e[6]||(e[6]=r=>h.dialog=r),top:"200px"},{footer:i(()=>[s(G,{onClick:e[5]||(e[5]=r=>h.dialog=!1)},{default:i(()=>[S("取消")]),_:1}),s(G,{onClick:M,type:"primary"},{default:i(()=>[S("确定")]),_:1})]),default:i(()=>[s(_e,{size:"default",onSubmit:e[4]||(e[4]=He(()=>{},["prevent"])),onKeyup:Pe(M,["enter"])},{default:i(()=>[s(de,{label:"放大倍数"},{default:i(()=>[o("div",null,[s(U,{class:"quick-scale-box"},{default:i(()=>[(m(!0),v(J,null,Q(h.quickList,r=>(m(),v("div",{onClick:P=>ce(r),class:"quick-scale-div"},w(r*100+"%"),9,Lt))),256))]),_:1}),o("p",Mt,[s(pe,{step:25,precision:0,min:50,max:200,modelValue:h.scale,"onUpdate:modelValue":e[3]||(e[3]=r=>h.scale=r)},null,8,["modelValue"]),Pt])])]),_:1})]),_:1},8,["onKeyup"])]),_:1},8,["modelValue"]),s(K,{class:"content-container drag-enable"},{default:i(()=>[s(ke,null,{default:i(()=>[s(he,{type:"border-card",modelValue:l.active,"onUpdate:modelValue":e[7]||(e[7]=r=>l.active=r),onTabChange:e[8]||(e[8]=r=>t.$router.push({name:r}))},{default:i(()=>[(m(!0),v(J,null,Q(l.list,r=>(m(),H(fe,{key:r.name,name:r.name.toString()},{label:i(()=>{var P;return[o("span",Ht,[o("span",Rt,w((P=r.meta)==null?void 0:P.name),1)])]}),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]),s(ve,{"view-class":"home-scrollbar-view",id:"home-page-scroll"},{default:i(()=>[s(ge,null,{default:i(({Component:r})=>[(m(),H(Te,{exclude:/(appInfo|bulletin)/},[(m(),H(qe(r)))],1032,["exclude"]))]),_:1})]),_:1})]),_:1}),s(we,{width:"414px"},{default:i(()=>[s($t)]),_:1})]),_:1})]),_:1})])}}}),Jt=Z(zt,[["__scopeId","data-v-f5b340f0"]]);export{Jt as default};
