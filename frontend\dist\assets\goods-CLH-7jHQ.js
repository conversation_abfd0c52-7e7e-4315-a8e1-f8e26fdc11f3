import{C as d}from"./index-CmETSh6Y.js";function s(o){return d.invoke("controller.goods.getReadList",o)}function i(o){const e={...o};return e.skus=JSON.stringify(o.skus),e.group_order_ids=JSON.stringify(o.group_order_ids),e.group_id=JSON.stringify(o.group_id),d.invoke("controller.goods.addGoodsRecord",{...e,add_time:Date.now()}).then(r=>(r.code==2&&d.invoke("controller.goods.updateGoods",{...e,add_time:Date.now()}).then(t=>{console.log("update res")}),r.code?Promise.reject(r):r))}function c(o){return d.invoke("controller.goods.delGoodsRecord",o)}export{i as a,c as d,s as g};
