import{d2 as R,ej as G,ek as V,ak as q,el as j,q as k,ce as B,Z as E,cb as K,bV as U,c4 as J,aJ as $,em as Q,en as Z,aA as F,eg as H,aE as W,eh as X,ei as Y}from"./index-CmETSh6Y.js";import{u as tt}from"./el-config-provider-DRHX7QJ5.js";import{b as et}from"./address-DsxfsM69.js";import{u as st}from"./autoApply-BvulaZhm.js";let it=(s=21)=>crypto.getRandomValues(new Uint8Array(s)).reduce((t,e)=>(e&=63,e<36?t+=e.toString(36):e<62?t+=(e-26).toString(36).toUpperCase():e>62?t+="-":t+="_",t),"");const P=window.get_anti_content;var D=(s=>(s[s.IDLE=0]="IDLE",s[s.PENDING=1]="PENDING",s[s.LOADING=2]="LOADING",s[s.PAUSED=3]="PAUSED",s))(D||{});let S=null;const dt=R("order-task",{state(){return{unCompleteList:[],completeList:[],completeTotal:0,status:0,loading:new Map,paused:new Map,finish:new Map,requestPool:new Map,logList:[],requestCountMax:0,pointExecTime:0,supplyState:[]}},getters:{taskStatusDes(){switch(this.status){case 0:return"空闲";case 2:return"正在执行";case 3:return"已暂停";default:return"出错"}},pageListData(){const s={unStart:[],loading:[],paused:[]};return this.unCompleteList.forEach(t=>{const{task_id:e}=t;this.loading.has(e)?s.loading.push(t):this.paused.has(e)?s.paused.push(t):s.unStart.push(t)}),s}},actions:{completeTask(s){const t=this.unCompleteList.findIndex(e=>e.task_id===s);this.loading.has(s)&&this.finish.set(s,this.loading.get(s)),this.loading.delete(s),this.paused.delete(s),t>=0&&this.completeList.unshift(...this.unCompleteList.splice(t,1)),this.completeTotal++},addToLog(s){this.logList.push({msg:"",...s,time:Date.now()})},autoSupply(s){const{shop_id:t,goods_id:e}=s,a=this.supplyState.find(n=>n.goods_id==e&&n.shop_id==t);a&&(a.status=2,F({mallId:t,goods_id:Number(e)}).then(n=>{if(n.code)return Promise.reject(n);this.addToLog({msg:`商品id${e}设置自动供货成功!`,type:"success"}),a.status=0,this.addTask(a.list),a.list=[]}).catch(n=>{console.log("自动供货失败",n),this.addToLog({msg:`商品id${e}设置自动供货失败:${n.msg}`,type:"danger"}),a.status=1,a.list.forEach(f=>{this.addToLog({task_id:f.task_id,msg:"开始失败：批发商品设置自动供货失败",type:"warning"})}),a.list=[]}).finally(()=>{}))},createTaskItem(s){const{task_id:t,account_site:e}=s,{complete_num:a,order_num:n,order_site:f}=s,_=new Map;for(let d=a+1;d<=n;d++){const c=it();_.set(c,{id:c,task_id:t,requestCount:0,sort:d,history:[]})}return{task_id:t,account_site:e,order_site:f,nextExecTime:0,status:1,complete_num:a,order_num:n,loading:new Map,pending:_,finish:new Map,row:s}},addTask(s){s.forEach(async t=>{const{task_id:e,account_site:a,complete_num:n,order_num:f,type:_,goods_id:d,shop_id:c,skus:i}=t;if(!i){this.addToLog({msg:"任务"+e+"已过期，请删除此任务重新创建",type:"danger",task_id:e});return}const{auto_supply:l}=k().taskCreate;if(this.loading.has(e))return q.warning({message:"检测到已存在的任务,将自动跳过",grouping:!0});if(n>=f)return this.completeTask(e),q.warning({message:"检测到已完成的任务,将自动跳过",grouping:!0});if(l&&_=="pifa"){const g=this.supplyState.find(r=>r.goods_id==d&&r.shop_id==c);if(!(g&&g.status===0))if(g&&g.status===2){this.addToLog({task_id:e,msg:"商品正在进行自动供货-请等待"}),g.list.find(r=>r.task_id===t.task_id)||g.list.push(t);return}else if(g&&g.status===1){this.addToLog({task_id:e,msg:"检测到上次自动供货失败，商品即将重新执行自动供货-请等待"}),g.list.push(t),this.autoSupply({shop_id:c,goods_id:d});return}else{this.addToLog({task_id:e,msg:"开始设置自动供货"}),this.supplyState.push({shop_id:c,goods_id:d,status:2,list:[t]}),this.autoSupply({shop_id:c,goods_id:d});return}}this.paused.has(e)?this.pausedToPending(e):(this.addToLog({task_id:e,msg:"进入执行队列"}),this.loading.set(e,this.createTaskItem(t)))}),this.startTasks()},pausedToPending(s){const t=this.paused.get(s);t&&(this.paused.delete(s),this.loading.set(s,t))},checkAndExecRequest(){this.loading.size&&(this.status=2);let s=1;for(let[t,e]of this.loading){if(this.requestCountMax&&s++>this.requestCountMax)return;if(this.requestPool.size>=25)break;if(!e.pending.size){e.complete_num+e.pending.size+e.loading.size<e.order_num&&this.loading.set(t,this.createTaskItem(e));continue}const a=Date.now();e.nextExecTime<=a&&this.createRequest(e)}},createRequest(s){const{task_id:t,pending:e,loading:a,order_num:n,complete_num:f,order_site:_,account_site:d}=s;if(!e.size){a.size||(f>=n?(this.addToLog({task_id:t,msg:"已完成的任务，将跳过"}),this.completeTask(t)):f+e.size+a.size>=n||this.loading.set(t,this.createTaskItem(s)));return}const[c]=e.keys(),i=e.get(c),l=this.unCompleteList.find(r=>r.task_id===t);if(!l){this.addToLog({task_id:t,msg:"没有找到原始数据，将移除任务",type:"warning"}),this.loading.delete(t);return}{const r=k(),{max:y,min:L,active:A}=r.visit.orderDelay;let w=.5;A&&(w=$(L,y)),s.nextExecTime=Date.now()+w*1e3}if(!i.account){const r=this.getAccount(i.account_site||d);if(r)i.account=r.data,s.account_site=r.ind+2,i.order_site=_+1,i.account_site=r.ind+2,l.account_site=s.account_site,l.order_site=i.order_site,s.order_site=i.order_site,this.addToLog({task_id:t,msg:`已成功分配小号${i.account}`});else{this.pauseTask([t]);return}}this.requestPool.set(c,i),e.delete(c),a.set(c,i);const g=()=>{a.delete(c),this.requestPool.delete(c)};Promise.resolve([]).then(async()=>{i.requestCount++;const r=k(),{nameCode_active:y,nameCode_position:L,nameCode_str:A,addr_active:w,addr_position:I,addr_str:z,filterStr:M,type:b,appoint_address:N}=r.address;let x={appoint_address:N||void 0};if(b==="diy"){const o=await et();if(o&&o.id)Reflect.deleteProperty(o,"id"),x=o;else{this.addToLog({type:"warning",msg:"检测到自定义地址列表为空，已暂停任务!"}),e.set(c,i),this.pauseTask([t]),g();return}}let p={sku_id:0,task_id:t,account:i.account,account_site:i.account_site,order_site:i.order_site,filter_address:M.replace("，",","),address_cipher:w?z:void 0,address_site:w?I:void 0,name_cipher:y?A:void 0,name_site:y?L:void 0,anti_content:P(),...x};const{active:O}=r.chat;if(O&&i.requestCount<=1&&r.getChatArr().forEach((u,h)=>{this.addToLog({task_id:t,msg:`正在发送第${i.sort}次执行的第${h+1}条消息,(小号：${p.account},信息：${u})`}),J({shop_id:l.shop_id,account:p.account,content:u}).then(m=>{this.addToLog({task_id:t,msg:`第${i.sort}次执行的第${h+1}条消息发送成功`,type:"success"})}).catch(m=>{this.addToLog({task_id:t,msg:`第${i.sort}次执行的第${h+1}条消息发送失败:${m.msg||""}`,type:"danger"})})}),i.requestCount<=1){const{active:o,min:u,max:h}=r.visit.visitDetails;if(o){const m=$(u,h);this.addToLog({task_id:t,msg:`第${i.sort}次执行浏览商品详情${m}秒`}),await E(m*1e3)}}i.requestCount==1&&l.setting.includes("coupon")&&l.use_coupon&&l.coupon_code&&await new Promise(o=>{Q({task_id:p.task_id,account:p.account},{showErrorMsg:!1}).then(()=>{this.addToLog({task_id:t,msg:"领取优惠券成功",type:"success"}),p.use_coupon=1}).catch(u=>{this.addToLog({task_id:t,msg:`领取优惠券失败:${u.msg}`,type:"danger"}),p.use_coupon=0}).finally(()=>{o(!0)})}),this.addToLog({task_id:t,msg:`第${i.sort}次执行第${i.requestCount}次开始`});{const{max:o,min:u,active:h}=r.visit.visitCount;if(h&&i.requestCount<=1){const m=$(u,o+1);this.addToLog({task_id:t,msg:`将对任务执行${m}次访客任务。`}),this.execGoodsVisit(l.goods_id,m)}}const v=JSON.parse(l.skus);let C=Number(l.sku);if(l.spec,!C){const o=v[$(0,v.length)]||v[0];C=o.skuId,o.spec}p.sku_id=C,Z(p,{isStop:()=>!this.loading.has(i.task_id),beforeRequest:async o=>{o.data.anti_content=P(),o.data.store_anti_content=await H(),s.row.type==="pifa"&&(o.data.singleBuy=void 0,await new Promise(async u=>{try{await W(async()=>{const h=[{goodsId:s.row.goods_id,skuId:C,skuNum:2}];let m;if(await Promise.allSettled([X({subScene:1,createOrderDTO:{orderType:1,createOrderItemList:h}}).then(T=>{T.success&&T.result&&(m={result:{curl:"https://mobile.yangkeduo.com/transac_order_coupon.html?_t_timestamp=transac_volume_checkout&secret_key="+T.result.secretKey,qrKey:T.result.secretKey}})}),Y({createOrderItemList:h}).then(T=>{T.success&&T.result&&T.result.curl&&T.result.qrKey&&(m=T)})]),m)o.data.singleBuy=m;else return Promise.reject()},50),u(!0)}catch(h){console.log(h),u(!0)}u(!0)}))}}).then(async o=>{var u;try{g(),l.complete_num++,s.complete_num++,this.addToLog({task_id:t,msg:`第${i.sort}次执行完成`,type:"success"}),i.history.push({account:p.account,account_site:p.account_site,res:o,order_site:p.order_site,requestCount:i.requestCount}),s.finish.set(i.id,i);const{auto_change_price:h}=k().taskCreate;h?this.changeOrderPrice(o.data):this.execAutoApply((u=o.data)==null?void 0:u.order_sn),!a.size&&!e.size&&(s.complete_num>=s.order_num&&(this.completeTask(t),this.paused.delete(t)),this.loading.delete(t),this.paused.has(t)||this.addToLog({task_id:t,msg:"已执行完成！",type:"success"}))}catch{}}).catch(o=>{var h;console.log(o,"taskStart-error"),i.history.push({account:p.account,account_site:p.account_site,res:o,order_site:p.order_site,requestCount:i.requestCount}),g();let u=(o==null?void 0:o.msg)||"网络超时";u.includes("商品信息变更")&&s.row.type==="pifa"&&(u+=";批发下单请确认商品已设置供货"),this.addToLog({task_id:t,msg:`第${i.sort}次执行第${i.requestCount}次失败:(${u})`,type:"danger"}),(h=o==null?void 0:o.msg)!=null&&h.includes("任务已完成")?(this.paused.delete(t),this.completeTask(t)):o!=null&&o.msg.includes("小号")?(i.account=void 0,i.account_site=void 0,i.order_site=void 0,i.requestCount=0,e.set(c,i)):o.msg.includes("绑定电脑地址错误")?(this.pauseTask([t]),q.warning({message:o.msg,grouping:!0})):i.requestCount<5?e.set(c,i):(this.addToLog({task_id:t,msg:"执行失败,将暂停任务",type:"warning"}),this.pauseTask([t]))}).finally(()=>{})})},async changeOrderPrice(s,t){const e=t||this.addToLog,{type:a,order_amount:n,shop_id:f,shop_name:_,order_sn:d,id:c}=s,i=k(),{pifaTargetPrice:l,othersTargetDiscount:g}=i.taskCreate;let r;a==="pifa"?r=Number(n-l*100):r=Number(n*(10-g))/10,e({msg:`订单${d},将执行改价操作`}),await E(3e3),K({store_id:f,shop_name:_,goodsDiscount:r,order_sn:d}).then(y=>{e({type:"success",msg:`订单${d},改价成功`}),this.execAutoApply(d)}).catch(y=>{e({type:"danger",msg:`订单${d},改价失败:${y.msg}`});const{chance:L}=i.pay;L==="immediate"&&e({msg:"改价失败不会启动自动支付",type:"warning"})}).finally(async()=>{await E(1e3),U({ids:d},{showErrorMsg:!1})})},execAutoApply(s,t){if(!s)return;const e=t||this.addToLog,a=k(),{chance:n,zfb_pass:f}=a.pay;if(n==="immediate"){if(!f){e({msg:"没有设置支付密码，不会开启自动支付",type:"warning"});return}e({msg:`订单${s},开始获取支付链接`}),B({order_sn:s}).then(_=>{const d=st();e({msg:`订单${s},已进入自动支付队列`}),d.addToPendding([{..._.data,type:"auto"}])}).catch(_=>{e({msg:`订单${s},获取支付链接失败，不会自动支付`})})}},execGoodsVisit(s,t){j({goods_id:s,num:t})},startTasks(){if(!this.loading.size){this.addToLog({msg:"执行队列为空，等待新任务",type:"primary"}),this.status=0;return}if(this.status===3){this.addToLog({msg:"执行队列已暂停",type:"warning"});return}S&&clearTimeout(S),(this.status===0||this.status===2)&&(S=setTimeout(()=>{S=null,this.pointExecTime=Date.now(),this.checkAndExecRequest(),this.startTasks()},1e3))},getAccount(s){const e=tt().getAccount(s-1<0?0:s-1);if(e.status)return e;this.addToLog({msg:`分配小号出错：${e.msg}`,type:"danger"}),this.status=3},pauseTask(s){s.forEach(t=>{const e=this.loading.get(t);e&&(this.loading.delete(t),this.paused.set(t,e),this.addToLog({task_id:t,msg:"已暂停",type:"warning"}))}),this.loading.size||(this.status=0)},taskAction(s,t,e){switch(s){case"start":{this.addTask(t);break}case"pause":{this.pauseTask(t.map(a=>a.task_id));break}case"reStart":{t.forEach(a=>{const n=this.paused.get(a.task_id);n&&this.loading.set(a.task_id,n),this.paused.delete(a.task_id)}),this.status=2,this.startTasks();break}case"delete":{V({task_id:t.map(a=>{const{task_id:n}=a;if(this.paused.has(n))this.paused.delete(n);else if(this.loading.has(n))return q.warning("当前任务正在执行不可删除"),"";return a.task_id}).filter(a=>a).join(",")}).then(()=>{this.getTaskList(e)});break}}},getEnumStatus(){return D},getTaskList(s={page:1,limit:20}){return G(s).then(t=>(console.log("taskList",t,s),this.unCompleteList=t.data.unfinished,this.completeList=t.data.finish.data,this.completeTotal=t.data.finish.total,this.unCompleteList.forEach(e=>{const{task_id:a}=e;e.complete_num>0&&e.complete_num<e.order_num&&!this.paused.has(a)&&!this.loading.has(a)&&this.paused.set(e.task_id,this.createTaskItem(e)),this.paused.has(a)&&e.complete_num>=e.order_num&&this.paused.delete(a)}),t))}}});export{dt as u};
