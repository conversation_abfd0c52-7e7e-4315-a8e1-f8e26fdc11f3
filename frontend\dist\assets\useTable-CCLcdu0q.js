import{t as C,s as H,ao as y,X as W,ak as Y}from"./index-CmETSh6Y.js";function $(A,z,P){var p,v,d,x,R,N;const l={clearWhenDataChange:!0,...P},e=C(z||{});e.height=A,e.loading=!1,e.round=e.round??!0,e.columnConfig={resizable:!0,...e.columnConfig},e.border=e.border??!0,e.stripe=e.stripe??!0,e.showHeaderOverflow=e.showHeaderOverflow??"title",e.showOverflow=e.showOverflow??"title",e.rowConfig={height:void 0,...e.rowConfig},e.customConfig={storage:{resizable:!0},...e.customConfig},e.align=e.align||"center",e.checkboxConfig={checkField:"checkField",...e.checkboxConfig},e.scrollY={enabled:!0,scrollToTopOnChange:!0,gt:0,...e.scrollY},l.clearWhenDataChange&&H([()=>e.loading,()=>e.data],t=>{var o,a;s.selection=((a=(o=l.tableRef)==null?void 0:o.value)==null?void 0:a.getCheckboxRecords())||[]});const s=C({selection:[]}),c=l.tableEvents||{},L=async t=>{var a,i,r;const o=l.tableRef;if(!o||!o.value)return Promise.reject("没有表格实例");switch(t){case"check-all":{(a=o.value)==null||a.setAllCheckboxRow(!0);break}case"clear":{(i=o.value)==null||i.clearCheckboxRow();break}case"reverse":{const{fullData:h}=(r=o.value)==null?void 0:r.getTableData();(h||[]).forEach(b=>{var n;(n=o.value)==null||n.toggleCheckboxRow(b)});break}case"area-select":await W.prompt(`请输入需要选中的订单(用-隔开),留空表示起始值或末尾值
(例：20-30 ; -30 ; 20- ; -)`,"区域选择",{showCancelButton:!0,inputPlaceholder:"请输入范围"}).then(h=>{var E,M,O;const{fullData:D}=(E=o.value)==null?void 0:E.getTableData(),{value:b}=h;let[n,g]=b.split("-").map(f=>Number(f.trim()));if(n=n-1<0?0:n-1,g=g||D.length,n>g)return Y.warning({message:"最小值大于最大值",grouping:!0});(M=o.value)==null||M.clearCheckboxRow(),(((O=o.value)==null?void 0:O.getTableData().visibleData.slice(n,g))||[]).forEach(f=>{var T;(T=o.value)==null||T.setCheckboxRow(f,!0)})})}o.value.dispatchEvent("checkbox-change",null,null)},m=c.onCheckboxAll;c.onCheckboxAll=t=>{var o,a;s.selection=((a=(o=l.tableRef)==null?void 0:o.value)==null?void 0:a.getCheckboxRecords())||[],m&&m(t)};const w=c.onCheckboxChange;c.onCheckboxChange=t=>{var o,a;s.selection=((a=(o=l.tableRef)==null?void 0:o.value)==null?void 0:a.getCheckboxRecords())||[],w&&w(t)};const u=e.rowClassName,S=t=>{let o=[];if(t.row===s.selectRow&&o.push("row-current"),u){let a=[];if(typeof u=="function"){const i=u(t)||"";if(typeof i=="string")a.push(i);else for(let r in i)i[r]&&a.push(r)}else o.push(u);o=o.concat(a)}return o.join(" ")},j=(p=e.menuConfig)==null?void 0:p.visibleMethod;e.menuConfig&&Reflect.deleteProperty(e.menuConfig,"visibleMethod");const B={className:`table-menu ${((v=e.menuConfig)==null?void 0:v.className)||""}`,visibleMethod:t=>{s.selectRow=t.row;const o=j;return o?o(t):!0},...e.menuConfig||{}};e.rowClassName=S,e.menuConfig=B;const k=(d=l.tableEvents)==null?void 0:d.onCellClick;c.onCellClick=t=>{s.selectRow=t.row,k&&k(t)};const F=C({total:0,pageSizes:((x=l.pagination)==null?void 0:x.pageSizes)||[100,200,500,1e3],limit:((R=l.pagination)==null?void 0:R.limit)||100,page:((N=l.pagination)==null?void 0:N.page)||1});return{tableAction:L,pagination:F,tableState:s,tableProps:e,loading:y(e,"loading"),tableList:y(e,"data"),tableEvents:c}}export{$ as u};
