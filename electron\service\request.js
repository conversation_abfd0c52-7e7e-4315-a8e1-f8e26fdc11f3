"use strict";
const axios = require("axios");
const { ipcMain } = require("electron");
const fs = require("fs");
const path = require("path");
const Service = require("ee-core").Service;
const FormData = require("form-data");
const { apiUrl, userAgent, isDev } = require("../config/config.app");
const os = require("os");
const CryptoJS = require("crypto-js");
let token = "";
let software_url = "";
let that = null;
function httpErrorStatusHandle(error) {
  let noticeFlag = true;
  if (axios.isCancel(error)) {
    console.error("重复请求", error.message);
  } else {
    // fs.writeFile(path.resolve(__dirname,'../../logs/error.json'),JSON.stringify(error),()=>{})
    // let message = "未知错误";
    let message = "";
    if (error.message.includes("timeout")) {
      noticeFlag = false;
      message = "超时的请求";
    } else if (error.message.includes("Network")) {
      message = "网络错误";
    } else if (error && error.response) {
      switch (error.response.status) {
        case 302: {
          message = "网络重定向";
          break;
        }
        case 404: {
          noticeFlag = false;
          message = "请求地址出错" + error.response.config.url;
          break;
        }
        case 501:
        case 502:
        case 503:
        case 500: {
          noticeFlag = false;
          message = "网络超时";
          break;
        }
        case 504: {
          message = "服务器超时";
          break;
        }
      }
    }
    // noticeFlag &&
    //   message &&
    //   that.service.noticeToView.notifiy({
    //     message,
    //     type: "error",
    //   });
  }
}

function createAxios(config, options) {
  console.log("[createAxios]",config.url);
  if (!config.url.startsWith("http")) {
    // config.url = apiUrl + config.url;
    config.url = (software_url || apiUrl) + config.url;
  }
  // 默认加密
  if (config.encrypt === void 0) {
    config.encrypt = true;
  }

  // console.log(config.url);
  if ((config.method || "").toUpperCase() === "GET") {
    config.data = config.params;
  }
  // config.method = config.method || "POST";
  if (config.encrypt) {
    config.method = "POST";
  }

  if (!config.headers) {
    config.headers = {};
  }
  config.headers.token = token;
  config.headers.mac = Object.values(os.networkInterfaces())[0][0].mac;
  const instance = axios.create({
    timeout: 90 * 1000,
  });
  // console.log(config)
  instance.interceptors.request.use((config) => {
    // console.log(config.url,config.encrypt,config.data)
    if (config.encrypt) {
      if (!config.data) {
        config.data = {};
      }
      config.data["_post_time"] = Math.floor(
        (Date.now() + config.time_diff) / 1000
      );
      const data = {};
      Object.keys(config.data).forEach((key) => {
        if (config.data[key] !== void 0) {
          data[key] = config.data[key];
        }
      });
      config.data = data;
      const str = JSON.stringify(config.data);
      const key = CryptoJS.enc.Utf8.parse("1672369827627767");
      //加密向量16位
      const iv = CryptoJS.enc.Utf8.parse("2688677969716887");
      const encrypted = CryptoJS.AES.encrypt(str, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      // console.log(config.data);
      // console.log(config.data, encrypted.toString());
      config.data = {
        // data: encodeURIComponent() ,
        data: encrypted.toString(),
      };
    }
    // console.log(encrypted)
    return config;
  });
  instance.interceptors.response.use(
    (response) => {
      // console.log('res:',response.data)
      // console.log(response.data)
      if (response.status >= 200 && response.status <= 299) {
        if (config.url.includes("/api/Index/getAppInfo")) {
          // console.log(response.data)
          try {
            software_url = response.data.app.software_url || "";
          } catch (e) { }
        }
        return response.data;
      } else {
        options.notcieError &&
          httpErrorStatusHandle({
            response,
          });
        return Promise.reject();
      }
    },
    (e) => {
      // console.error('res error:',e)
      httpErrorStatusHandle(e);
      return Promise.reject(e);
    }
  );
  return instance(config);
}
/**
 * 示例服务
 * @class
 */
class RequestService extends Service {
  constructor(ctx) {
    super(ctx);
    that = this;
  }

  async createAxios(config, options = { notcieError: true }) {
    return createAxios(config, options);
  }

  async createAnyAxios(config) {
    isDev && console.log("[createAnyAxios url]", config.url);
    const instance = axios.create({
      timeout: 60 * 1000,
      validateStatus(status) {
        return status >= 200 && status < 303
      },
    });
    if (!config.headers) {
      config.headers = {};
    }
    const ignoreArr = ['t.gif','acquireQrCode']
    if(!ignoreArr.some(item => config.url.includes(item))){
      const domain = String(config.url).split("/")[2] || '';
      if(!config.headers || !config.headers["User-Agent"]){
        config.headers["User-Agent"] =  userAgent;
      }
      config.headers['sec-ch-ua-platform'] = '"Windows"';
      config.headers['sec-fetch-dest'] = 'empty'
      config.headers['sec-fetch-mode'] = 'cors'
      config.headers['sec-fetch-site'] = 'same-origin'
      config.headers['authority'] = config.headers['authority'] || domain
    }
    instance.interceptors.response.use(
      (response) => {
        console.log(response.data)
        if(config.url.includes('https://fuwu.pinduoduo.com/clint/api/login')){
          const html = response.data
          return {
            success:true,
            data:html,
            _responseHeader: response.headers,
          }
        }
        if (response.status >= 200 && response.status <= 299) {
          if(config.url.includes("/clint/api/login")){
            return {
              res: response.data,
              res_headers:response.headers
            }
          }
          return response.data;
        } else {
          return {
            error_code: -1,
            data: "",
            error_msg: "*网络超时---*",
          };
        }
      },
      (error) => {
        console.log(error)
        const res = {
          success:false,
          error_code: -2,
          data: {},
          error_msg: error.toString(),
        };
        if(typeof error === 'object' && error.response){
          res.responseData = error.response.data
        }
        return res
      }
    );
    return instance(config);
  }

  // 验证身份
  async auth() {
    const response = await createAxios({
      url: "/api/user/monitor",
      method: "get",
      params: {
        mac: Object.values(os.networkInterfaces())[0][0].mac,
      },
    });
    return response;
  }

  async setToken(data) {
    token = data;
    return true;
  }

  async upload(path) {
    const formData = new FormData();
    formData.append("file", fs.createReadStream(path));
    const response = await createAxios({
      url: "/api/tool/uploadFile",
      data: formData,
    });
    return response;
  }
}

RequestService.toString = () => "[class RequestService]";
module.exports = RequestService;
