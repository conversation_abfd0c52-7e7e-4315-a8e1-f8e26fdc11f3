import{cJ as $e,bh as Ce,b2 as W,cE as fe,bn as pe,cw as ye,bo as Z,_ as ve,d as K,cK as he,cL as Le,cM as Ee,aR as be,a as me,cN as Se,a5 as O,cO as Te,ct as ze,c as u,s as ie,b1 as we,T as J,o as v,i as y,k as N,h,f as R,e,n as c,a9 as L,b as w,r as G,O as Ne,E as D,cC as ke,Q as ue,cB as Oe,aN as xe,cP as Ae,cQ as Be,W as _e,cR as De,cS as Re,R as Me,a6 as Pe,a7 as Ve,cT as Fe,cU as We,cV as U,cW as Ye,b4 as j,ab as Ie,g as ge,cX as Xe,cY as He,bs as je,H as ce,cZ as Ue,cD as de,c_ as Ze,c$ as Ge,d0 as Ke,d1 as Qe,bc as qe,aP as Je,aU as et}from"./index-CmETSh6Y.js";var tt="Expected a function";function re(r,n,i){var l=!0,k=!0;if(typeof r!="function")throw new TypeError(tt);return $e(i)&&(l="leading"in i?!!i.leading:l,k="trailing"in i?!!i.trailing:k),Ce(r,n,{leading:l,maxWait:n,trailing:k})}const at=(r,n)=>{if(!W||!r||!n)return!1;const i=r.getBoundingClientRect();let l;return n instanceof Element?l=n.getBoundingClientRect():l={top:0,right:window.innerWidth,bottom:window.innerHeight,left:0},i.top<l.bottom&&i.bottom>l.top&&i.right>l.left&&i.left<l.right},st=()=>W&&/firefox/i.test(window.navigator.userAgent),nt=pe({urlList:{type:Z(Array),default:()=>ye([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:{type:Boolean,default:!1},teleported:{type:Boolean,default:!1},closeOnPressEscape:{type:Boolean,default:!0}}),lt={close:()=>!0,switch:r=>fe(r)},ot=["src"],rt=K({name:"ElImageViewer"}),it=K({...rt,props:nt,emits:lt,setup(r,{expose:n,emit:i}){const l=r,k={CONTAIN:{name:"contain",icon:he(Ee)},ORIGINAL:{name:"original",icon:he(Le)}},_=st()?"DOMMouseScroll":"mousewheel",{t:Y}=be(),d=me("image-viewer"),{nextZIndex:E}=Se(),b=O(),I=O([]),M=Te(),$=O(!0),m=O(l.initialIndex),P=ze(k.CONTAIN),p=O({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),V=u(()=>{const{urlList:a}=l;return a.length<=1}),X=u(()=>m.value===0),F=u(()=>m.value===l.urlList.length-1),t=u(()=>l.urlList[m.value]),C=u(()=>{const{scale:a,deg:o,offsetX:f,offsetY:T,enableTransition:A}=p.value;let z=f/a,B=T/a;switch(o%360){case 90:case-270:[z,B]=[B,-z];break;case 180:case-180:[z,B]=[-z,-B];break;case 270:case-90:[z,B]=[-B,z];break}const H={transform:`scale(${a}) rotate(${o}deg) translate(${z}px, ${B}px)`,transition:A?"transform .3s":""};return P.value.name===k.CONTAIN.name&&(H.maxWidth=H.maxHeight="100%"),H}),S=u(()=>fe(l.zIndex)?l.zIndex:E());function g(){oe(),i("close")}function x(){const a=re(f=>{switch(f.code){case j.esc:l.closeOnPressEscape&&g();break;case j.space:se();break;case j.left:ne();break;case j.up:s("zoomIn");break;case j.right:le();break;case j.down:s("zoomOut");break}}),o=re(f=>{(f.wheelDelta?f.wheelDelta:-f.detail)>0?s("zoomIn",{zoomRate:1.2,enableTransition:!1}):s("zoomOut",{zoomRate:1.2,enableTransition:!1})});M.run(()=>{U(document,"keydown",a),U(document,_,o)})}function oe(){M.stop()}function ee(){$.value=!1}function te(a){$.value=!1,a.target.alt=Y("el.image.error")}function ae(a){if($.value||a.button!==0||!b.value)return;p.value.enableTransition=!1;const{offsetX:o,offsetY:f}=p.value,T=a.pageX,A=a.pageY,z=re(H=>{p.value={...p.value,offsetX:o+H.pageX-T,offsetY:f+H.pageY-A}}),B=U(document,"mousemove",z);U(document,"mouseup",()=>{B()}),a.preventDefault()}function Q(){p.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function se(){if($.value)return;const a=Ye(k),o=Object.values(k),f=P.value.name,A=(o.findIndex(z=>z.name===f)+1)%a.length;P.value=k[a[A]],Q()}function q(a){const o=l.urlList.length;m.value=(a+o)%o}function ne(){X.value&&!l.infinite||q(m.value-1)}function le(){F.value&&!l.infinite||q(m.value+1)}function s(a,o={}){if($.value)return;const{zoomRate:f,rotateDeg:T,enableTransition:A}={zoomRate:1.4,rotateDeg:90,enableTransition:!0,...o};switch(a){case"zoomOut":p.value.scale>.2&&(p.value.scale=Number.parseFloat((p.value.scale/f).toFixed(3)));break;case"zoomIn":p.value.scale<7&&(p.value.scale=Number.parseFloat((p.value.scale*f).toFixed(3)));break;case"clockwise":p.value.deg+=T;break;case"anticlockwise":p.value.deg-=T;break}p.value.enableTransition=A}return ie(t,()=>{Ie(()=>{const a=I.value[0];a!=null&&a.complete||($.value=!0)})}),ie(m,a=>{Q(),i("switch",a)}),we(()=>{var a,o;x(),(o=(a=b.value)==null?void 0:a.focus)==null||o.call(a)}),n({setActiveItem:q}),(a,o)=>(v(),J(We,{to:"body",disabled:!a.teleported},[y(Fe,{name:"viewer-fade",appear:""},{default:N(()=>[h("div",{ref_key:"wrapper",ref:b,tabindex:-1,class:c(e(d).e("wrapper")),style:R({zIndex:e(S)})},[h("div",{class:c(e(d).e("mask")),onClick:o[0]||(o[0]=Ne(f=>a.hideOnClickModal&&g(),["self"]))},null,2),L(" CLOSE "),h("span",{class:c([e(d).e("btn"),e(d).e("close")]),onClick:g},[y(e(D),null,{default:N(()=>[y(e(ke))]),_:1})],2),L(" ARROW "),e(V)?L("v-if",!0):(v(),w(ue,{key:0},[h("span",{class:c([e(d).e("btn"),e(d).e("prev"),e(d).is("disabled",!a.infinite&&e(X))]),onClick:ne},[y(e(D),null,{default:N(()=>[y(e(Oe))]),_:1})],2),h("span",{class:c([e(d).e("btn"),e(d).e("next"),e(d).is("disabled",!a.infinite&&e(F))]),onClick:le},[y(e(D),null,{default:N(()=>[y(e(xe))]),_:1})],2)],64)),L(" ACTIONS "),h("div",{class:c([e(d).e("btn"),e(d).e("actions")])},[h("div",{class:c(e(d).e("actions__inner"))},[y(e(D),{onClick:o[1]||(o[1]=f=>s("zoomOut"))},{default:N(()=>[y(e(Ae))]),_:1}),y(e(D),{onClick:o[2]||(o[2]=f=>s("zoomIn"))},{default:N(()=>[y(e(Be))]),_:1}),h("i",{class:c(e(d).e("actions__divider"))},null,2),y(e(D),{onClick:se},{default:N(()=>[(v(),J(_e(e(P).icon)))]),_:1}),h("i",{class:c(e(d).e("actions__divider"))},null,2),y(e(D),{onClick:o[3]||(o[3]=f=>s("anticlockwise"))},{default:N(()=>[y(e(De))]),_:1}),y(e(D),{onClick:o[4]||(o[4]=f=>s("clockwise"))},{default:N(()=>[y(e(Re))]),_:1})],2)],2),L(" CANVAS "),h("div",{class:c(e(d).e("canvas"))},[(v(!0),w(ue,null,Me(a.urlList,(f,T)=>Pe((v(),w("img",{ref_for:!0,ref:A=>I.value[T]=A,key:f,src:f,style:R(e(C)),class:c(e(d).e("img")),onLoad:ee,onError:te,onMousedown:ae},null,46,ot)),[[Ve,T===m.value]])),128))],2),G(a.$slots,"default")],6)]),_:3})],8,["disabled"]))}});var ut=ve(it,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image-viewer/src/image-viewer.vue"]]);const ct=ge(ut),dt=pe({hideOnClickModal:{type:Boolean,default:!1},src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:{type:Boolean,default:!1},scrollContainer:{type:Z([String,Object])},previewSrcList:{type:Z(Array),default:()=>ye([])},previewTeleported:{type:Boolean,default:!1},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0}}),ft={load:r=>r instanceof Event,error:r=>r instanceof Event,switch:r=>fe(r),close:()=>!0,show:()=>!0},pt=["src","loading"],vt={key:0},mt=K({name:"ElImage",inheritAttrs:!1}),gt=K({...mt,props:dt,emits:ft,setup(r,{emit:n}){const i=r;let l="";const{t:k}=be(),_=me("image"),Y=Xe(),d=He(),E=O(),b=O(!1),I=O(!0),M=O(!1),$=O(),m=O(),P=W&&"loading"in HTMLImageElement.prototype;let p,V;const X=u(()=>Y.style),F=u(()=>{const{fit:s}=i;return W&&s?{objectFit:s}:{}}),t=u(()=>{const{previewSrcList:s}=i;return Array.isArray(s)&&s.length>0}),C=u(()=>{const{previewSrcList:s,initialIndex:a}=i;let o=a;return a>s.length-1&&(o=0),o}),S=u(()=>i.loading==="eager"?!1:!P&&i.loading==="lazy"||i.lazy),g=()=>{W&&(I.value=!0,b.value=!1,E.value=i.src)};function x(s){I.value=!1,b.value=!1,n("load",s)}function oe(s){I.value=!1,b.value=!0,n("error",s)}function ee(){at($.value,m.value)&&(g(),Q())}const te=Ge(ee,200);async function ae(){var s;if(!W)return;await Ie();const{scrollContainer:a}=i;Ue(a)?m.value=a:de(a)&&a!==""?m.value=(s=document.querySelector(a))!=null?s:void 0:$.value&&(m.value=Ze($.value)),m.value&&(p=U(m,"scroll",te),setTimeout(()=>ee(),100))}function Q(){!W||!m.value||!te||(p==null||p(),m.value=void 0)}function se(s){if(s.ctrlKey){if(s.deltaY<0)return s.preventDefault(),!1;if(s.deltaY>0)return s.preventDefault(),!1}}function q(){t.value&&(V=U("wheel",se,{passive:!1}),l=document.body.style.overflow,document.body.style.overflow="hidden",M.value=!0,n("show"))}function ne(){V==null||V(),document.body.style.overflow=l,M.value=!1,n("close")}function le(s){n("switch",s)}return ie(()=>i.src,()=>{S.value?(I.value=!0,b.value=!1,Q(),ae()):g()}),we(()=>{S.value?ae():g()}),(s,a)=>(v(),w("div",{ref_key:"container",ref:$,class:c([e(_).b(),s.$attrs.class]),style:R(e(X))},[E.value!==void 0&&!b.value?(v(),w("img",je({key:0},e(d),{src:E.value,loading:s.loading,style:e(F),class:[e(_).e("inner"),e(t)&&e(_).e("preview"),I.value&&e(_).is("loading")],onClick:q,onLoad:x,onError:oe}),null,16,pt)):L("v-if",!0),I.value||b.value?(v(),w("div",{key:1,class:c(e(_).e("wrapper"))},[I.value?G(s.$slots,"placeholder",{key:0},()=>[h("div",{class:c(e(_).e("placeholder"))},null,2)]):b.value?G(s.$slots,"error",{key:1},()=>[h("div",{class:c(e(_).e("error"))},ce(e(k)("el.image.error")),3)]):L("v-if",!0)],2)):L("v-if",!0),e(t)?(v(),w(ue,{key:2},[M.value?(v(),J(e(ct),{key:0,"z-index":s.zIndex,"initial-index":e(C),infinite:s.infinite,"url-list":s.previewSrcList,"hide-on-click-modal":s.hideOnClickModal,teleported:s.previewTeleported,"close-on-press-escape":s.closeOnPressEscape,onClose:ne,onSwitch:le},{default:N(()=>[s.$slots.viewer?(v(),w("div",vt,[G(s.$slots,"viewer")])):L("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):L("v-if",!0)],64)):L("v-if",!0)],6))}});var ht=ve(gt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image/src/image.vue"]]);const St=ge(ht),yt=pe({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:r=>r>=0&&r<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:{type:Boolean,default:!1},duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:Z(String),default:"round"},textInside:{type:Boolean,default:!1},width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:Z([String,Array,Function]),default:""},format:{type:Z(Function),default:r=>`${r}%`}}),bt=["aria-valuenow"],wt={viewBox:"0 0 100 100"},kt=["d","stroke","stroke-width"],_t=["d","stroke","opacity","stroke-linecap","stroke-width"],It={key:0},$t=K({name:"ElProgress"}),Ct=K({...$t,props:yt,setup(r){const n=r,i={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},l=me("progress"),k=u(()=>({width:`${n.percentage}%`,animationDuration:`${n.duration}s`,backgroundColor:F(n.percentage)})),_=u(()=>(n.strokeWidth/n.width*100).toFixed(1)),Y=u(()=>["circle","dashboard"].includes(n.type)?Number.parseInt(`${50-Number.parseFloat(_.value)/2}`,10):0),d=u(()=>{const t=Y.value,C=n.type==="dashboard";return`
          M 50 50
          m 0 ${C?"":"-"}${t}
          a ${t} ${t} 0 1 1 0 ${C?"-":""}${t*2}
          a ${t} ${t} 0 1 1 0 ${C?"":"-"}${t*2}
          `}),E=u(()=>2*Math.PI*Y.value),b=u(()=>n.type==="dashboard"?.75:1),I=u(()=>`${-1*E.value*(1-b.value)/2}px`),M=u(()=>({strokeDasharray:`${E.value*b.value}px, ${E.value}px`,strokeDashoffset:I.value})),$=u(()=>({strokeDasharray:`${E.value*b.value*(n.percentage/100)}px, ${E.value}px`,strokeDashoffset:I.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"})),m=u(()=>{let t;return n.color?t=F(n.percentage):t=i[n.status]||i.default,t}),P=u(()=>n.status==="warning"?Ke:n.type==="line"?n.status==="success"?Qe:qe:n.status==="success"?Je:ke),p=u(()=>n.type==="line"?12+n.strokeWidth*.4:n.width*.111111+2),V=u(()=>n.format(n.percentage));function X(t){const C=100/t.length;return t.map((g,x)=>de(g)?{color:g,percentage:(x+1)*C}:g).sort((g,x)=>g.percentage-x.percentage)}const F=t=>{var C;const{color:S}=n;if(et(S))return S(t);if(de(S))return S;{const g=X(S);for(const x of g)if(x.percentage>t)return x.color;return(C=g[g.length-1])==null?void 0:C.color}};return(t,C)=>(v(),w("div",{class:c([e(l).b(),e(l).m(t.type),e(l).is(t.status),{[e(l).m("without-text")]:!t.showText,[e(l).m("text-inside")]:t.textInside}]),role:"progressbar","aria-valuenow":t.percentage,"aria-valuemin":"0","aria-valuemax":"100"},[t.type==="line"?(v(),w("div",{key:0,class:c(e(l).b("bar"))},[h("div",{class:c(e(l).be("bar","outer")),style:R({height:`${t.strokeWidth}px`})},[h("div",{class:c([e(l).be("bar","inner"),{[e(l).bem("bar","inner","indeterminate")]:t.indeterminate}]),style:R(e(k))},[(t.showText||t.$slots.default)&&t.textInside?(v(),w("div",{key:0,class:c(e(l).be("bar","innerText"))},[G(t.$slots,"default",{percentage:t.percentage},()=>[h("span",null,ce(e(V)),1)])],2)):L("v-if",!0)],6)],6)],2)):(v(),w("div",{key:1,class:c(e(l).b("circle")),style:R({height:`${t.width}px`,width:`${t.width}px`})},[(v(),w("svg",wt,[h("path",{class:c(e(l).be("circle","track")),d:e(d),stroke:`var(${e(l).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-width":e(_),fill:"none",style:R(e(M))},null,14,kt),h("path",{class:c(e(l).be("circle","path")),d:e(d),stroke:e(m),fill:"none",opacity:t.percentage?1:0,"stroke-linecap":t.strokeLinecap,"stroke-width":e(_),style:R(e($))},null,14,_t)]))],6)),(t.showText||t.$slots.default)&&!t.textInside?(v(),w("div",{key:2,class:c(e(l).e("text")),style:R({fontSize:`${e(p)}px`})},[G(t.$slots,"default",{percentage:t.percentage},()=>[t.status?(v(),J(e(D),{key:1},{default:N(()=>[(v(),J(_e(e(P))))]),_:1})):(v(),w("span",It,ce(e(V)),1))])],6)):L("v-if",!0)],10,bt))}});var Lt=ve(Ct,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/progress/src/progress.vue"]]);const Tt=ge(Lt),zt=[{label:"多多果园",value:"guoyuan",disabled:!1},{label:"多多批发",value:"pifa",disabled:!1,tooltip:!0,tooltipContent:"每单最低两件"},{label:"关键词",value:"keyword",disabled:!1},{label:"万人团",value:"wanrentuan",disabled:!1},{label:"活动",value:"activity",showInCreate:!1,disabled:!1},{label:"赠品",value:"gift",disabled:!1,showInCreate:!1},{label:"全球购",value:"globalBuy",disabled:!1,showInCreate:!1}],Nt=[{label:"开团",value:"open_group",disabled:({order_type:r,isGroupOrder:n})=>!1},{label:"参团",value:"spell_group",disabled:({order_type:r,isGroupOrder:n})=>r==="wanrentuan"||!n},{label:"单独购买",value:"alone",disabled:({order_type:r,isGroupOrder:n})=>r==="wanrentuan"}];export{Tt as E,St as a,Nt as b,zt as o};
