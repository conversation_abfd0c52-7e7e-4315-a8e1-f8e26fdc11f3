import{d as N,bn as C,a as T,c as v,aC as I,r as $,a5 as x,d6 as L,dd as p,cE as g,i as y,eW as o,j as B,cv as A,eX as z,eY as O,eZ as R,cD as Y,bo as h,g as j}from"./index-CmETSh6Y.js";const V=C({prefixCls:{type:String}}),P=N({name:"ElSpaceItem",props:V,setup(e,{slots:u}){const d=T("space"),r=v(()=>`${e.prefixCls||d.b()}__item`);return()=>I("div",{class:r.value},$(u,"default"))}}),w={small:8,default:12,large:16};function _(e){const u=T("space"),d=v(()=>[u.b(),u.m(e.direction),e.class]),r=x(0),n=x(0),m=v(()=>{const t=e.wrap||e.fill?{flexWrap:"wrap",marginBottom:`-${n.value}px`}:{},l={alignItems:e.alignment};return[t,l,e.style]}),f=v(()=>{const t={paddingBottom:`${n.value}px`,marginRight:`${r.value}px`},l=e.fill?{flexGrow:1,minWidth:`${e.fillRatio}%`}:{};return[t,l]});return L(()=>{const{size:t="small",wrap:l,direction:i,fill:a}=e;if(p(t)){const[s=0,c=0]=t;r.value=s,n.value=c}else{let s;g(t)?s=t:s=w[t||"small"]||w.small,(l||a)&&i==="horizontal"?r.value=n.value=s:i==="horizontal"?(r.value=s,n.value=0):(n.value=s,r.value=0)}}),{classes:d,containerStyle:m,itemStyle:f}}const k=C({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},class:{type:h([String,Object,Array]),default:""},style:{type:h([String,Array,Object]),default:""},alignment:{type:h(String),default:"center"},prefixCls:{type:String},spacer:{type:h([Object,String,Number,Array]),default:null,validator:e=>A(e)||g(e)||Y(e)},wrap:Boolean,fill:Boolean,fillRatio:{type:Number,default:100},size:{type:[String,Array,Number],values:R,validator:e=>g(e)||p(e)&&e.length===2&&e.every(g)}}),W=N({name:"ElSpace",props:k,setup(e,{slots:u}){const{classes:d,containerStyle:r,itemStyle:n}=_(e);function m(f,t="",l=[]){const{prefixCls:i}=e;return f.forEach((a,s)=>{z(a)?p(a.children)&&a.children.forEach((c,S)=>{z(c)&&p(c.children)?m(c.children,`${t+S}-`,l):l.push(y(P,{style:n.value,prefixCls:i,key:`nested-${t+S}`},{default:()=>[c]},o.PROPS|o.STYLE,["style","prefixCls"]))}):O(a)&&l.push(y(P,{style:n.value,prefixCls:i,key:`LoopKey${t+s}`},{default:()=>[a]},o.PROPS|o.STYLE,["style","prefixCls"]))}),l}return()=>{var f;const{spacer:t,direction:l}=e,i=$(u,"default",{key:0},()=>[]);if(((f=i.children)!=null?f:[]).length===0)return null;if(p(i.children)){let a=m(i.children);if(t){const s=a.length-1;a=a.reduce((c,S,E)=>{const b=[...c,S];return E!==s&&b.push(y("span",{style:[n.value,l==="vertical"?"width: 100%":null],key:E},[A(t)?t:B(t,o.TEXT)],o.STYLE)),b},[])}return y("div",{class:d.value,style:r.value},a,o.STYLE|o.CLASS)}return i.children}}}),X=j(W);export{X as E};
