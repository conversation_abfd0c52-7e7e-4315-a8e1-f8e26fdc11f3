import{d as re,A as ne,a5 as de,t as G,F as ie,bZ as ce,G as ue,b as f,o as u,h as r,i as s,H as g,e as z,k as o,E as pe,ax as _e,S as ge,j as c,J as me,T as x,a9 as M,ac as fe,af as he,N as ye,au as we,Q as Y,R as H,at as ke,a8 as be,L as Se,ak as m,d5 as A,dM as ve,a2 as Ie,a3 as Ce,m as xe}from"./index-CmETSh6Y.js";import{a as Ee,E as $e}from"./el-table-column-XN33CJP9.js";import"./el-tooltip-l0sNRNKZ.js";import{E as De}from"./el-empty-Bk3X6ZAS.js";import{E as Le}from"./el-space-mudxGYIP.js";import{d as Ve}from"./dayjs.min-Bj8ADfnT.js";import{s as Te}from"./pddStore-CIiwLQql.js";const L=N=>(Ie("data-v-dba02782"),N=N(),Ce(),N),Pe={class:"refund-manage"},Fe={class:"user-info-bar"},ze={class:"user-info"},Ue=L(()=>r("span",{class:"user-label"},"用户信息：",-1)),Ge={class:"user-id"},Me=L(()=>r("span",{class:"points-label"},"积分余额：",-1)),Ne={class:"points-value"},Be={class:"action-buttons"},qe={class:"status-tabs"},Oe={style:{"margin-left":"auto"}},Ae=L(()=>r("p",null,"当前状态下没有订单数据",-1)),je=L(()=>r("p",null,"请尝试切换其他状态或添加新的商品订单",-1)),Ke={class:"amount"},Ye={key:0},He={key:1},Re={class:"score"},Qe={class:"illustrate-tabs"},Je=L(()=>r("div",{class:"container",style:{padding:"0","font-size":"14px",height:"150px"}},[r("h5"),r("h3",{class:"title f-s-16",style:{padding:"0px 0 0 25px"}}," 拍退声明 "),r("ol",null,[r("span",{style:{"margin-top":"0.5em"}},"1，链接上架不能超过15天，只能0销量才能提交;"),r("span",{style:{"margin-top":"0.5em","margin-left":"7em"}},"4，拍退激活失败只能再提交一次申请，第二次还是失败的链接不能再提交;"),r("br"),r("span",{style:{"margin-top":"1em"}},"2，商品价格300元以内，高于300的商品上个低价的sku;"),r("span",{style:{"margin-top":"1em","margin-left":"3.6em"}},"5，符合上述要求的，不出销量可以返还积分，不符合的则不返还积分。"),r("br"),r("span",{style:{"margin-top":"1em"}},"3，代拍前一两天或当天改销量;"),r("span",{style:{"margin-top":"1em","margin-left":"14.7em",color:"#f9638c"}},"6，需要开通极速退款，或及时自行退款，需当天完成退款。"),r("br")]),r("span",{style:{"padding-left":"60px",color:"#f9638c"}}," 拍退激活后，第二天下午6点前显示 食品，生鲜，茶叶，水果和图书，酒水，黑五， 这种特殊类目，成功率只有50% ,失败不返还积分")],-1)),Ze={class:"dialog-header"},We={class:"search-row"},Xe={class:"search-controls"},et={class:"right-controls"},tt={class:"store-select-wrapper"},st=L(()=>r("span",{class:"store-label"},"店铺：",-1)),ot={class:"status-row"},at={class:"status-buttons"},lt={class:"sku-option"},rt={class:"sku-spec"},nt={class:"sku-price"},dt={key:1},it={key:0,class:"price"},ct={key:1},ut={key:0,class:"stock"},pt={key:1},_t={key:0},gt={key:1},mt={class:"dialog-footer"},ft={class:"operation-log"},ht=L(()=>r("div",{class:"log-title"},"任务日志",-1)),yt={class:"log-content"},wt={class:"footer-buttons"},kt=re({__name:"refundManage",setup(N){const w=ne(),Q=async()=>{w.refreshUserInfo()},j=de(),k=G({refundList:[],selection:[]}),h=G({page:1,limit:50,total:0}),v=G({order_status:101}),l=G({visible:!1,goodsList:[],selectedGoods:[],operationLogs:[],loading:!1,currentStatus:"onsale",searchParams:{goods_id:"",mallId:""},showBatchInput:!1,batchInputText:""}),B=ie(),q=G({mallId:"",goods_id:""}),R=n=>{navigator.clipboard.writeText(n),m.success("已复制到剪贴板")},U=n=>({101:"全部",0:"待申请",1:"申请中",2:"执行中",3:"已完成",4:"执行失败"})[n]||"未知",I=async()=>{try{const n={order_status:v.order_status,per_page:h.limit,current_page:h.page},t=await A({url:"/api/ptorder/getList",method:"post",data:n,encrypt:!0});if(t.code===0){const p=t.data.data||[];console.log("接口返回的原始数据:",t.data),console.log("待处理的列表数据:",p);const a=p.map(d=>({id:d.ID,orderSn:d.order_sn||"",shopName:d.shop_name||"",goodsId:d.goods_id||"",goodsName:d.goods_name||"",goodsImg:d.goods_img||"",goodsValue:parseFloat(d.goods_price||"0"),refundAmount:parseFloat(d.goods_price||"0"),createTime:d.add_time?parseInt(d.add_time)*1e3:Date.now(),applyTime:d.add_time?parseInt(d.add_time)*1e3:Date.now(),processTime:d.server_time?parseInt(d.server_time)*1e3:void 0,status:d.order_status||0,reason:d.remarks||"",remark:d.remarks||"",blacklistScore:parseFloat(d.jifen||"0")}));console.log("转换后的数据:",a),k.refundList=a,h.total=t.data.total||0}else throw new Error(t.msg||"获取订单列表失败")}catch(n){m.error("获取订单列表失败"),console.error("获取订单列表失败:",n),k.refundList=[],h.total=0}},V=n=>{v.order_status=n,h.page=1,I()},J=async()=>{try{await I(),m.success("订单状态同步完成")}catch(n){console.error("同步订单状态失败:",n),m.error(n instanceof Error?n.message:"同步失败，请重试")}},Z=()=>{l.visible=!0,!l.searchParams.mallId&&q.mallId&&(l.searchParams.mallId=q.mallId)},W=n=>{l.selectedGoods=n},X=()=>{m.info("打开日志功能")},ee=()=>{const n=t=>t.split(/[,，\n]/).map(p=>p.trim()).filter(p=>p);l.searchParams.goods_id=n(l.batchInputText).join(","),l.showBatchInput=!1,T()},te=()=>{T()},se=n=>{if(n._skuSelect&&n.sku_list){const t=n.sku_list.find(p=>p.skuId===n._skuSelect);t&&(n._sku_select_list=[t])}},K=n=>{l.currentStatus=n,T()},T=async()=>{const n=l.searchParams.mallId||q.mallId,t=B.tableList.find(p=>p.mallId==n);if(!t){m.warning("请先选择店铺");return}if(!B.checkAvailable(t)){m.warning("店铺不可用");return}l.loading=!0;try{let a={page:1,goods_id_list:l.searchParams.goods_id?l.searchParams.goods_id.split(",").filter(C=>C.trim()):void 0};switch(l.currentStatus){case"onsale":a.is_onsale=1,a.sold_out=0;break;case"warehouse":a.is_onsale=0;break;case"all":break}const d=await ve({delay:1e3,pageStart:1,pageEnd:10,isStop:()=>!l.loading,request:async C=>{const{data:i}=await Te({...a,page:C},t);if(!i.success)return Promise.reject(new Error(i.error_msg||"获取商品列表失败"));const F=i.result.goods_list||[];F.forEach(_=>{if(_.sku_list&&_.sku_list.length>0&&(l.currentStatus==="onsale"&&(_.sku_list=_.sku_list.filter(E=>E.isOnsale)),_.sku_list.length>0)){const S=[..._.sku_list].sort(($,D)=>$.groupPrice-D.groupPrice)[0];_._skuSelect=S.skuId,_._sku_select_list=[S]}});const b=i.result.total||0;return{list:F,total:b}}});l.goodsList=d,P({action:"获取商品",status:`共获取${d.length}个商品`}),P({action:"获取成功",status:`共获取${d.length}个商品`})}catch(p){console.error("获取商品列表失败:",p),m.error("获取商品列表失败"),l.goodsList=[],P({action:"获取商品",status:"获取失败"})}finally{l.loading=!1}},P=n=>{const t=new Date,p=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}:${String(t.getSeconds()).padStart(2,"0")}`;l.operationLogs.push({id:Date.now(),time:p,action:n.action,status:n.status})},oe=async()=>{if(l.selectedGoods.length===0){m.warning("请先选择要添加的商品");return}const n=Date.now(),t=w.pait.day;console.log(w.pait.combo,"2222222222222222222222222");const p=24*60*60*1e3;for(const a of l.selectedGoods)if(a.created_at){const d=typeof a.created_at=="number"?a.created_at<1e10?a.created_at*1e3:a.created_at:new Date(a.created_at).getTime();if(Math.floor((n-d)/p)>t){m.error(`${a.goods_name} 商品已超${w.pait.day}过天`),P({action:"时间验证",status:`${a.goods_name} 超过${w.pait.day}天，添加失败`});return}}try{const a=l.searchParams.mallId||q.mallId,d=B.tableList.find(_=>_.mallId==a),C=(d==null?void 0:d.mallName)||"",i=l.selectedGoods.map(_=>{const E=_._sku_select_list&&_._sku_select_list.length>0?_._sku_select_list[0]:null;if(!E)throw new Error(`商品 ${_.goods_name} 未选择SKU`);const S=E.groupPrice/100;let $=S.toFixed(2),D=!1;if(w.pait.combo&&Array.isArray(w.pait.combo))for(const e of w.pait.combo){const y=parseFloat(e.prefix);if(S<y){$=parseFloat(e.price).toFixed(2),D=!0;break}}if(!D)throw new Error("该价格无效");const O=_.created_at||"";return{goods_name:_.goods_name,goods_id:_.id.toString(),goods_price:S.toFixed(2),jifen:$,shop_name:C,goods_add_time:O}}),b=await A({url:"/api/ptorder/add",method:"post",data:{goods:i},encrypt:!0});if(b.code===0)m.success(`成功添加 ${i.length} 个商品`),P({action:"添加商品",status:`成功添加${i.length}个商品`}),l.selectedGoods=[],l.visible=!1,await I();else throw new Error(b.msg)}catch(a){console.error("添加选中商品失败:",a),m.error(a instanceof Error?a.message:"添加商品失败，请重试"),P({action:"添加商品",status:"添加失败"})}},ae=async()=>{var n;if(k.selection.length===0){m.warning("请先勾选要申请的订单");return}try{const t=k.selection.map(d=>d.goodsId),a=await A({url:"/api/ptorder/setOrder",method:"post",data:{type:"start",goods_id:t},encrypt:!0});if(a.code===0)m.success(`成功申请 ${t.length} 个订单`),await I(),await w.refreshUserInfo(),k.selection=[],(n=j.value)==null||n.clearSelection();else throw new Error(a.msg||"申请订单失败")}catch(t){console.error("申请订单失败:",t),m.error(t instanceof Error?t.message:"申请订单失败，请重试")}},le=async()=>{var n;if(k.selection.length===0){m.warning("请先勾选要删除的订单");return}try{const t=k.selection.map(d=>d.goodsId),a=await A({url:"/api/ptorder/setOrder",method:"post",data:{type:"is_del",goods_id:t},encrypt:!0});if(a.code===0)m.success(`成功删除 ${t.length} 个订单`),await I(),k.selection=[],(n=j.value)==null||n.clearSelection();else throw new Error(a.msg||"删除订单失败")}catch(t){console.error("删除订单失败:",t),m.error(t instanceof Error?t.message:"删除订单失败，请重试")}};return ce(()=>{I()}),(n,t)=>{const p=pe,a=ge,d=Le,C=De,i=Ee,F=me,b=fe,_=$e,E=ue("ds-pagination"),S=he,$=ke,D=we,O=Se;return u(),f("div",Pe,[r("div",Fe,[r("div",ze,[Ue,r("span",Ge,g(z(w).user_info.username),1),Me,r("span",Ne,g(z(w).user_info.balance),1),s(a,{type:"info",size:"small",circle:"",class:"refresh-btn",onClick:Q},{default:o(()=>[s(p,null,{default:o(()=>[s(z(_e))]),_:1})]),_:1})]),r("div",Be,[s(a,{type:"primary",size:"default",onClick:Z},{default:o(()=>[c("获取商品")]),_:1}),s(a,{type:"success",size:"default",onClick:ae},{default:o(()=>[c("申请订单")]),_:1}),s(a,{type:"danger",size:"default",onClick:le},{default:o(()=>[c("删除订单")]),_:1})])]),r("div",qe,[s(d,null,{default:o(()=>[s(a,{type:v.order_status===101?"primary":"default",onClick:t[0]||(t[0]=e=>V(101))},{default:o(()=>[c(" 全部 ")]),_:1},8,["type"]),s(a,{type:v.order_status===0?"primary":"default",onClick:t[1]||(t[1]=e=>V(0))},{default:o(()=>[c(" 待申请 ")]),_:1},8,["type"]),s(a,{type:v.order_status===1?"primary":"default",onClick:t[2]||(t[2]=e=>V(1))},{default:o(()=>[c(" 申请中 ")]),_:1},8,["type"]),s(a,{type:v.order_status===2?"primary":"default",onClick:t[3]||(t[3]=e=>V(2))},{default:o(()=>[c(" 执行中 ")]),_:1},8,["type"]),s(a,{type:v.order_status===3?"primary":"default",onClick:t[4]||(t[4]=e=>V(3))},{default:o(()=>[c(" 已完成 ")]),_:1},8,["type"]),s(a,{type:v.order_status===4?"primary":"default",onClick:t[5]||(t[5]=e=>V(4))},{default:o(()=>[c(" 执行失败 ")]),_:1},8,["type"]),r("div",Oe,[s(a,{onClick:J,type:"primary",size:"default"},{default:o(()=>[c("同步订单状态")]),_:1})])]),_:1})]),s(_,{stripe:"",onSelectionChange:t[6]||(t[6]=e=>{k.selection=e}),data:k.refundList,class:"refund-table",ref_key:"refundTableEl",ref:j,height:"420"},{empty:o(()=>[s(C,{description:"暂无订单记录"},{description:o(()=>[Ae,je]),_:1})]),default:o(()=>[s(i,{type:"selection",width:"30"}),s(i,{label:"序号",type:"index",width:"60",index:e=>(h.page-1)*h.limit+e+1},null,8,["index"]),s(i,{label:"店铺名称",prop:"shopName",width:"150"}),s(i,{label:"宝贝ID",prop:"goodsId",width:"120"},{default:o(e=>[s(F,{underline:!1,onClick:y=>R(e.row.goodsId)},{default:o(()=>[c(g(e.row.goodsId),1)]),_:2},1032,["onClick"])]),_:1}),s(i,{label:"宝贝名称",prop:"goodsName","min-width":"200","show-overflow-tooltip":""}),s(i,{label:"状态",width:"100",prop:"status"},{default:o(e=>[e.row.status==0?(u(),x(b,{key:0,type:"info"},{default:o(()=>[c(g(U(e.row.status)),1)]),_:2},1024)):M("",!0),e.row.status==1?(u(),x(b,{key:1,type:"warning"},{default:o(()=>[c(g(U(e.row.status)),1)]),_:2},1024)):M("",!0),e.row.status==2?(u(),x(b,{key:2,type:""},{default:o(()=>[c(g(U(e.row.status)),1)]),_:2},1024)):M("",!0),e.row.status==3?(u(),x(b,{key:3,type:"success"},{default:o(()=>[c(g(U(e.row.status)),1)]),_:2},1024)):M("",!0),e.row.status==4?(u(),x(b,{key:4,type:"danger"},{default:o(()=>[c(g(U(e.row.status)),1)]),_:2},1024)):M("",!0)]),_:1}),s(i,{label:"商品价值",prop:"goodsValue",width:"120"},{default:o(e=>[r("span",Ke,"¥"+g(e.row.goodsValue.toFixed(2)),1)]),_:1}),s(i,{label:"创建时间",prop:"createTime",width:"160"},{default:o(e=>[e.row.createTime?(u(),f("span",Ye,g(z(Ve)(e.row.createTime).format("YYYY-MM-DD HH:mm:ss")),1)):(u(),f("span",He,"-"))]),_:1}),s(i,{label:"扣除积分",prop:"blacklistScore",width:"100"},{default:o(e=>[r("span",Re,g(e.row.blacklistScore.toFixed(2)),1)]),_:1})]),_:1},8,["data"]),s(E,{total:h.total,"current-page":h.page,"onUpdate:current-page":t[7]||(t[7]=e=>h.page=e),"page-size":h.limit,"onUpdate:page-size":t[8]||(t[8]=e=>h.limit=e),onCurrentChange:t[9]||(t[9]=e=>I()),onSizeChange:t[10]||(t[10]=e=>I()),selection:k.selection},null,8,["total","current-page","page-size","selection"]),r("div",Qe,[s(d,null,{default:o(()=>[Je]),_:1})]),s(O,{modelValue:l.visible,"onUpdate:modelValue":t[17]||(t[17]=e=>l.visible=e),title:"实时获取商品",width:"90%","close-on-click-modal":!1,class:"goods-dialog"},{default:o(()=>[r("div",Ze,[r("div",We,[r("div",Xe,[s(S,{modelValue:l.searchParams.goods_id,"onUpdate:modelValue":t[11]||(t[11]=e=>l.searchParams.goods_id=e),placeholder:"宝贝ID搜索,逗号隔开",style:{width:"200px"},clearable:"",onKeyup:ye(T,["enter"]),onClear:T},null,8,["modelValue","onKeyup"])]),r("div",et,[r("div",tt,[st,s(D,{modelValue:l.searchParams.mallId,"onUpdate:modelValue":t[12]||(t[12]=e=>l.searchParams.mallId=e),placeholder:"请选择店铺",style:{width:"200px"},onChange:te},{default:o(()=>[(u(!0),f(Y,null,H(z(B).tableList,e=>(u(),x($,{key:e.mallId,label:e.mallName,value:e.mallId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s(a,{type:"primary",onClick:T,loading:l.loading,size:"default"},{default:o(()=>[c(" 获取商品 ")]),_:1},8,["loading"]),s(a,{type:"success",onClick:oe,size:"default"},{default:o(()=>[c(" 添加选中商品 ")]),_:1})])]),r("div",ot,[r("div",at,[s(a,{type:l.currentStatus==="onsale"?"primary":"default",size:"small",onClick:t[13]||(t[13]=e=>K("onsale"))},{default:o(()=>[c(" 在售商品 ")]),_:1},8,["type"]),s(a,{type:l.currentStatus==="warehouse"?"primary":"default",size:"small",onClick:t[14]||(t[14]=e=>K("warehouse"))},{default:o(()=>[c(" 仓库中商品 ")]),_:1},8,["type"]),s(a,{type:l.currentStatus==="all"?"primary":"default",size:"small",onClick:t[15]||(t[15]=e=>K("all"))},{default:o(()=>[c(" 全部商品 ")]),_:1},8,["type"])])])]),s(_,{data:l.goodsList,stripe:"",height:"500",loading:l.loading,onSelectionChange:W},{default:o(()=>[s(i,{type:"selection",width:"50"}),s(i,{label:"序号",type:"index",width:"60",index:e=>e+1},null,8,["index"]),s(i,{label:"宝贝ID",prop:"id",width:"120"},{default:o(e=>[s(F,{underline:!1,onClick:y=>R(e.row.id)},{default:o(()=>[c(g(e.row.id),1)]),_:2},1032,["onClick"])]),_:1}),s(i,{label:"商品名称",prop:"goods_name","min-width":"300","show-overflow-tooltip":""}),s(i,{label:"SKU",width:"200"},{default:o(e=>[e.row.sku_list&&e.row.sku_list.length>0?(u(),x(D,{key:0,modelValue:e.row._skuSelect,"onUpdate:modelValue":y=>e.row._skuSelect=y,placeholder:"请选择SKU",size:"small",style:{width:"100%"},onChange:y=>se(e.row)},{default:o(()=>[(u(!0),f(Y,null,H(e.row.sku_list,y=>(u(),x($,{key:y.skuId,label:y.spec||e.row.goods_name,value:y.skuId},{default:o(()=>[r("div",lt,[r("div",rt,g(y.spec||e.row.goods_name),1),r("div",nt,"¥"+g((y.groupPrice/100).toFixed(2)),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):(u(),f("span",dt,"-"))]),_:1}),s(i,{label:"销售价格",width:"100",align:"right"},{default:o(e=>[e.row._sku_select_list&&e.row._sku_select_list.length>0?(u(),f("span",it,g((e.row._sku_select_list[0].groupPrice/100).toFixed(2)),1)):(u(),f("span",ct,"-"))]),_:1}),s(i,{label:"库存",width:"80",align:"right"},{default:o(e=>[e.row._sku_select_list&&e.row._sku_select_list.length>0?(u(),f("span",ut,g(e.row._sku_select_list[0].skuQuantity),1)):(u(),f("span",pt,"-"))]),_:1}),s(i,{label:"商品创建时间",width:"160",align:"center"},{default:o(e=>[e.row.created_at?(u(),f("span",_t,g(z(be)(e.row.created_at)),1)):(u(),f("span",gt,"-"))]),_:1})]),_:1},8,["data","loading"]),r("div",mt,[r("div",ft,[ht,r("div",yt,[(u(!0),f(Y,null,H(l.operationLogs,e=>(u(),f("div",{key:e.id,class:"log-item"},g(e.time)+" - "+g(e.action)+" - "+g(e.status),1))),128))])]),r("div",wt,[s(a,{onClick:X},{default:o(()=>[c("打开日志")]),_:1}),s(a,{type:"danger",onClick:t[16]||(t[16]=e=>l.visible=!1)},{default:o(()=>[c("关闭")]),_:1})])])]),_:1},8,["modelValue"]),s(O,{modelValue:l.showBatchInput,"onUpdate:modelValue":t[20]||(t[20]=e=>l.showBatchInput=e),title:"批量输入商品ID",width:"500px"},{footer:o(()=>[s(a,{onClick:t[19]||(t[19]=e=>l.showBatchInput=!1)},{default:o(()=>[c("取消")]),_:1}),s(a,{type:"primary",onClick:ee},{default:o(()=>[c("确定")]),_:1})]),default:o(()=>[s(S,{type:"textarea",modelValue:l.batchInputText,"onUpdate:modelValue":t[18]||(t[18]=e=>l.batchInputText=e),placeholder:"用逗号,换行隔开",rows:10,resize:"none"},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),$t=xe(kt,[["__scopeId","data-v-dba02782"]]);export{$t as default};
