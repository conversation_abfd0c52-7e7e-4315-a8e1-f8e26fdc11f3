import{d as Q,a5 as x,t as z,F as X,bZ as J,G as A,ap as K,b as T,o as g,h as M,i as s,k as a,a6 as G,T as _,S as W,e as n,j as d,a9 as P,H as B,bs as ee,Q as te,R as ae,bm as oe,L as se,ac as le,ak as h,X as ne,dq as ie,dr as re,ds as ce,a1 as de,dt as U,Z as ue,aD as pe,a2 as ge,a3 as me,m as fe}from"./index-CmETSh6Y.js";import{a as _e,E as ke}from"./el-table-column-XN33CJP9.js";import"./el-tooltip-l0sNRNKZ.js";import{E as be}from"./el-empty-Bk3X6ZAS.js";import{E as ye}from"./el-space-mudxGYIP.js";import{d as he}from"./dayjs.min-Bj8ADfnT.js";import{a as we}from"./goods-CLH-7jHQ.js";import{u as ve}from"./useTable-CCLcdu0q.js";const Ce=C=>(ge("data-v-8e727267"),C=C(),me(),C),Se={class:"store-manage"},Le={class:"table-header"},Ie={class:"m-b-10"},Pe=["onUpdate:modelValue","onInput"],$e=Ce(()=>M("span",null,"运费险:",-1)),Ee=Q({__name:"storeManage",setup(C){const j=x(),b=z({logList:new Map,selection:[],checkStatusLoding:!1}),D=x(),k=ve(500,{id:"store-analyze-table",checkboxConfig:{checkMethod({row:r}){return r.goods_type!=19}}},{tableRef:D}),u=z({dialog:!1,isGetList:!1});function R(r){u.dialog=!0,u.store=r,k.tableAction("clear"),k.tableProps.data=[],F(r)}function F(r){const{tableProps:e}=k;let o=1,p=0;const i=async()=>{var f,v,L;e.loadingConfig={text:`加载第${o}页数据中。(${e.data.length}/${p})`};const c=await pe(r,{page:o++});if(!c.success)return Promise.reject(c);const S=((f=c.result)==null?void 0:f.goods_list)||[];return p=((v=c.result)==null?void 0:v.total)||0,!S.length||e.data.length>=p?Promise.reject(c):((L=e.data)==null||L.push(...S),c)};e.loading=!0,u.isGetList=!0;const y=()=>{i().then(async()=>u.isGetList&&u.dialog?(await ue(300),y()):Promise.reject()).catch(()=>{e.loading=!1,u.isGetList=!1})};y()}const Y=x([{data:""}]);async function H(){const{selection:r}=k.tableState,e=r.filter(i=>i.goods_type!=19);if(!e.length){h.warning({grouping:!0,message:"过滤后没有可用商品"});return}const o=u.store,p=e.map(async i=>{const y={goods_name:i.goods_name,goods_id:i.id,goods_img:i.thumb_url,activity_id:0,mallId:o.mallId,mallName:o.mallName,groupPrice:i.sku_group_price[0]/100,normalPrice:i.sku_price[0]/100,skus:i.sku_list.filter(c=>c.isOnsale).map(c=>({skuId:c.skuId,spec:c.spec,skuImg:c.skuThumbUrl,groupPrice:c.groupPrice/100,normalPrice:c.normalPrice/100})),group_id:{multiple:[]},group_order_ids:[],coupon_code:""};we(y)});await Promise.allSettled(p),h.success({grouping:!0,message:"添加成功"}),u.dialog=!1}async function V(){u.isGetList&&await ne({title:"提示",message:"当前数据正在加载，确定要关闭吗",showCancelButton:!0}),u.dialog=!1}const l=X();J(()=>{l.getList()});const w=z({insurance:new Set});function $(r,e){if(!l.checkAvailable(r))return h({message:"店铺已过期",type:"warning",grouping:!0});const o=`${r.mallId}-${e}`;w.insurance.add(o);let p;switch(e){case"check":{p=ce(r).then(i=>(i.data.result&&i.data.result.configStatus?h.success({message:"已开通",grouping:!0}):h.warning({message:"未开通",grouping:!0}),i));break}case"close":{p=re(r);break}case"open":{p=ie(r);break}}p.then(i=>{e!=="check"&&h.success({message:"操作成功",grouping:!0})}).finally(()=>{w.insurance.delete(o)})}async function O(r=b.selection){b.checkStatusLoding=!0,await Promise.allSettled(r.map(async e=>{const o=await de(e);o.success&&o.result.login?l.checkAvailable(e)||await U({mallId:e.mallId,status:1}):l.checkAvailable(e)&&await U({mallId:e.mallId,status:0})})),l.getList(),b.checkStatusLoding=!1}return(r,e)=>{const o=W,p=ye,i=A("vxe-column"),y=A("vxe-table"),c=se,S=be,f=_e,v=le,L=ke,Z=A("ds-pagination"),N=K("blur");return g(),T("div",Se,[M("div",Le,[s(p,null,{default:a(()=>[G((g(),_(o,{onClick:e[0]||(e[0]=t=>n(l).addPddStore()),type:"primary",size:"default"},{default:a(()=>[d("添加店铺")]),_:1})),[[N]]),G((g(),_(o,{onClick:n(l).getList,type:"primary",size:"default"},{default:a(()=>[d("刷新")]),_:1},8,["onClick"])),[[N]]),s(o,{onClick:e[1]||(e[1]=t=>O()),type:"success",disabled:!b.selection.length,size:"default",loading:b.checkStatusLoding},{default:a(()=>[d("重新检测选中店铺登录状态")]),_:1},8,["disabled","loading"])]),_:1})]),s(c,{title:"全店解析",width:880,class:"store-goods-list",modelValue:u.dialog,"onUpdate:modelValue":e[3]||(e[3]=t=>u.dialog=t),"close-on-click-modal":!1,"close-on-press-escape":!1,"on-close":V},{footer:a(()=>[s(o,{onClick:V},{default:a(()=>[d("关闭")]),_:1})]),default:a(()=>[M("div",Ie,[s(o,{type:"success",onClick:H},{default:a(()=>[d("解析选中("+B(n(k).tableState.selection.length)+")",1)]),_:1}),u.isGetList?(g(),_(o,{key:0,type:"danger",onClick:e[2]||(e[2]=t=>u.isGetList=!1)},{default:a(()=>[d("停止加载后续商品")]),_:1})):P("",!0)]),s(y,ee({...n(k).tableProps,...n(k).tableEvents},{ref_key:"tableRef",ref:D}),{default:a(()=>[s(i,{type:"checkbox",width:50}),s(i,{width:150,field:"id",title:"商品ID",filters:Y.value,"filter-method":({row:t,option:m})=>String(t.id).includes(String(m.data))},{filter:a(({$panel:t,column:m})=>[(g(!0),T(te,null,ae(m.filters,(I,q)=>G((g(),T("input",{type:"type",key:q,"onUpdate:modelValue":E=>I.data=E,onInput:E=>t.changeOption(E,!!I.data,I)},null,40,Pe)),[[oe,I.data]])),128))]),_:1},8,["filters","filter-method"]),s(i,{field:"goods_name",title:"商品名称"})]),_:1},16)]),_:1},8,["modelValue"]),s(L,{stripe:"",onSelectionChange:e[4]||(e[4]=t=>{b.selection=t}),data:n(l).tableList,class:"store-table",ref_key:"storeTableEl",ref:j,height:"700"},{empty:a(()=>[s(S,{description:"暂无店铺"})]),default:a(()=>[s(f,{type:"selection",width:"30"}),s(f,{label:"店铺名称",prop:"mallName"}),s(f,{label:"店铺ID",prop:"mallId"}),s(f,{label:"最近登录时间",prop:"update_time"},{default:a(t=>[d(B(n(he)(Number(t.row.update_time)).format("YYYY-MM-DD HH:mm:ss")),1)]),_:1}),s(f,{label:"店铺状态",width:"120",prop:""},{default:a(t=>[n(l).checkAvailable(t.row)?(g(),_(v,{key:0,type:"success"},{default:a(()=>[d("在线")]),_:1})):(g(),_(v,{key:1,type:"info"},{default:a(()=>[d("失效")]),_:1}))]),_:1}),s(f,{label:"操作",width:"300",align:"center",prop:"",fixed:"right"},{default:a(t=>[s(p,null,{default:a(()=>[n(l).checkAvailable(t.row)?(g(),_(o,{key:0,type:"success",onClick:m=>R(t.row)},{default:a(()=>[d(" 全店解析 ")]),_:2},1032,["onClick"])):P("",!0),n(l).checkAvailable(t.row)?(g(),_(o,{key:1,type:"primary",onClick:m=>n(l).openStorePage(t.row)},{default:a(()=>[d("打开店铺")]),_:2},1032,["onClick"])):P("",!0),s(o,{type:"primary",onClick:m=>n(l).updatePddStore(t.row)},{default:a(()=>[d("登录")]),_:2},1032,["onClick"]),s(o,{type:"danger",onClick:m=>n(l).deleteItem(t.row.mallId)},{default:a(()=>[d("删除")]),_:2},1032,["onClick"])]),_:2},1024),n(l).checkAvailable(t.row)?(g(),_(p,{key:0,class:"m-t-8"},{default:a(()=>[$e,s(o,{loading:w.insurance.has(`${t.row.mallId}-check`),plain:"",type:"primary",onClick:m=>$(t.row,"check")},{default:a(()=>[d("检查")]),_:2},1032,["loading","onClick"]),s(o,{loading:w.insurance.has(`${t.row.mallId}-open`),plain:"",type:"success",onClick:m=>$(t.row,"open")},{default:a(()=>[d("打开")]),_:2},1032,["loading","onClick"]),s(o,{loading:w.insurance.has(`${t.row.mallId}-close`),plain:"",type:"danger",onClick:m=>$(t.row,"close")},{default:a(()=>[d("关闭")]),_:2},1032,["loading","onClick"])]),_:2},1024)):P("",!0)]),_:1})]),_:1},8,["data"]),s(Z,{total:n(l).pagination.total,"current-page":n(l).pagination.page,"onUpdate:current-page":e[5]||(e[5]=t=>n(l).pagination.page=t),"page-size":n(l).pagination.limit,"onUpdate:page-size":e[6]||(e[6]=t=>n(l).pagination.limit=t),onCurrentChange:e[7]||(e[7]=t=>n(l).getList()),onSizeChange:e[8]||(e[8]=t=>n(l).getList())},null,8,["total","current-page","page-size"])])}}}),Ne=fe(Ee,[["__scopeId","data-v-8e727267"]]);export{Ne as default};
