import{d2 as n,cc as p,q as a,C as o,ep as r,aJ as d,ak as l}from"./index-CmETSh6Y.js";const g=n("auto-apply",{state:()=>({pending:[],lastExecTime:0,lastCycleTime:0}),actions:{addToPendding(e){if(!e.length)return;const t=new Set;this.pending.forEach(i=>{t.add(i.id)});let s=!1;e.forEach(i=>{t.has(i.id)?s=!0:(t.add(i.id),this.pending.push(i))}),s&&l.warning({message:"检测到重复订单，已过滤",grouping:!0}),this.startExec()},startExec(){this.applyTid||(this.applyTid=setTimeout(()=>{const e=a(),{max:t,min:s,active:i}=e.visit.payDelay;if(this.applyTid=void 0,this.lastCycleTime=Date.now(),!!this.pending.length){if(i)Date.now()-this.lastExecTime>d(s,t)*1e3&&this.pendingToLoading(this.pending.shift());else for(;this.pending.length;)this.pendingToLoading(this.pending.shift());this.startExec()}},1e3))},pendingToLoading(e){if(this.lastExecTime=Date.now(),e){const t=a(),s={item:{...e},win:{pass:t.pay.zfb_pass,passArr:[...t.zfbPassArr]}};t.pay.isNew?o.invoke("controller.autoApply.autoApply",s):r(s)}},stop(){this.pending=[],this.applyTid=void 0,this.applyTid&&clearTimeout(this.applyTid)},resolveResult(e){e.id&&p({ids:String(e.order_sn)},{showErrorMsg:!1}),this.startExec()}}});export{g as u};
