{"cache": {"config": {"env": "local", "name": "ckpdd", "baseDir": "D:\\接单\\流程\\CK-gitlab\\electron", "HOME": "D:\\接单\\流程\\CK-gitlab", "rundir": "D:\\接单\\流程\\CK-gitlab\\electron\\run", "dump": {"ignore": {}}, "homeDir": "D:\\接单\\流程\\CK-gitlab", "root": "D:\\接单\\流程\\CK-gitlab", "appUserDataDir": "C:\\Users\\<USER>\\AppData\\Roaming\\ckpdd", "userHome": "C:\\Users\\<USER>", "appVersion": "1.2.14", "isPackaged": false, "execDir": "D:\\接单\\流程\\CK-gitlab", "logger": {"dir": "D:\\接单\\流程\\CK-gitlab\\logs", "encoding": "utf8", "env": "local", "level": "INFO", "consoleLevel": "INFO", "disableConsoleAfterReady": false, "outputJSON": false, "buffer": true, "appLogName": "ee-2025-08-06.log", "coreLogName": "ee-core.log", "agentLogName": "ee-agent.log", "errorLogName": "ee-error-2025-08-06.log", "coreLogger": {}, "allowDebugAtProd": false, "enablePerformanceTimer": false}, "httpclient": {"enableDNSCache": false, "dnsCacheLookupInterval": 10000, "dnsCacheMaxLength": 1000, "request": {"timeout": 5000}, "httpAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}, "httpsAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}}, "socketServer": {"enable": false, "port": 7070, "path": "/socket.io/", "connectTimeout": 45000, "pingTimeout": 30000, "pingInterval": 25000, "maxHttpBufferSize": 100000000, "transports": ["polling", "websocket"], "cors": {"origin": true}}, "httpServer": {"enable": false, "https": {"enable": false, "key": "/public/ssl/localhost+1.key", "cert": "/public/ssl/localhost+1.pem"}, "protocol": "http://", "host": "127.0.0.1", "port": 7071, "cors": {"origin": "*"}, "body": {"multipart": true, "formidable": {"keepExtensions": true}}, "filterRequest": {"uris": ["favicon.ico"], "returnData": ""}}, "mainServer": {"protocol": "http://", "host": "127.0.0.1", "port": 2341, "options": {}, "ssl": {"key": "", "cert": ""}}, "openAppMenu": "dev-show", "hardGpu": {"enable": false}, "storage": {"dir": "D:\\接单\\流程\\CK-gitlab\\data"}, "loadingPage": false, "addons": {"window": {"enable": true}, "tray": {"enable": true, "title": "CK多多助手", "icon": "/public/images/tray_logo.png"}, "security": {"enable": true}, "awaken": {"enable": true, "protocol": "ee", "args": []}, "autoUpdater": {"enable": true, "windows": true, "macOS": false, "linux": false, "options": {"provider": "generic", "url": ""}, "force": false}, "javaServer": {"enable": false, "port": 18080, "jreVersion": "jre1.8.0_201", "opt": "-server -Xms512M -Xmx512M -Xss512k -Dspring.profiles.active=prod -Dserver.port=${port} -Dlogging.file.path=\"${path}\" ", "name": "java-app.jar"}, "example": {"enable": true}}, "developmentMode": {"default": "vue", "mode": {"vue": {"hostname": "localhost", "port": 2341}, "react": {"hostname": "localhost", "port": 3000}, "html": {"hostname": "localhost", "indexPage": "index.html"}}}, "openDevTools": true, "windowsOption": {"title": "k", "width": 1350, "height": 660, "minWidth": 1350, "minHeight": 660, "x": 250, "y": 60, "transparent": true, "webPreferences": {"contextIsolation": false, "nodeIntegration": true}, "resizable": false, "frame": false, "show": false, "icon": "D:\\接单\\流程\\CK-gitlab\\public\\images\\logo-32.png"}, "remoteUrl": {"enable": false, "url": "http://localhost:2336/"}, "testConfig": {"login": "http://local.com/api/login"}, "coreMiddlewares": [], "coreMiddleware": [], "appMiddlewares": [], "appMiddleware": []}}}