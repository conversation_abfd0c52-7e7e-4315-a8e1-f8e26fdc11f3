import{eU as u,eV as m,dN as f,F as _,Z as S,y as h,aE as b,ef as w,aF as I}from"./index-CmETSh6Y.js";import{d as y}from"./dayjs.min-Bj8ADfnT.js";const p=m;async function N(e,t){e.goods_name||Reflect.deleteProperty(e,"goods_name");const s={method:"post",url:"https://mms.pinduoduo.com/vodka/v2/mms/query/display/mall/goodsList",headers:await u(t,{Referer:"https://mms.pinduoduo.com/goods/goods_list"}),data:{is_onsale:1,size:100,order_by:"sold_quantity:asc,id:desc",sold_out:0,...e}},o=await m(s);return{data:o,code:o.success?0:1,msg:o.error_msg||o.errorMsg||""}}async function E(e,t){const s={method:"post",url:"https://mms.pinduoduo.com/express_inf/cost_template/get_list",headers:await u(e),data:{sourceKey:"MMS",pageSize:10,...t}};return m(s)}async function M(e,t){const s={provinceId:6,cityId:76,districtId:697,costType:0,costTemplateName:"k-指定不发货",shipFromOutsideMainland:!1,dispatchCost:[],sourceKey:"MMS",...t},o={url:"https://mms.pinduoduo.com/express_inf/cost_template/create",method:"post",headers:await u(e),data:s};return m(o)}async function P(e,t){const s=t.edit_list[t.edit_list.length-1];s&&(s.quantity_delta=String(s.quantity_delta));const o={url:"https://mms.pinduoduo.com/guide-api/mms/inventory/batch_edit/submit",method:"post",headers:await u(e),data:t};return m(o)}async function v(e,t){const o={method:"post",url:"https://mms.pinduoduo.com/mille/mms/goods/pageQueryLegalGoods",data:{bizId:1,pageSize:50,pageNum:1,...t},headers:await u(e)};return await p(o)}async function A(e,t){const s={code:0,msg:"",success:new Set,failed:new Set,reason:{}},o=(Array.isArray(e)?e:[e]).map(Number),d=await v(t,{goodsIds:o});if(!d.success)return s.code=1,s.msg=f(d),s;const r=new Set,c=new Set;if(d.result.list.forEach(a=>{if(a.enrollEnable){r.add(a.goodsId);return}a.reasons.includes("商品已参与批发活动")?c.add(a.goodsId):(s.failed.add(a.goodsId),s.reason[a.goodsId]=a.reasons.join(";"))}),r.size){const a=await G([...r.values()],t);console.log(a,"addSupply"),a.success||(s.code=1,s.msg=f(a));const{failGoodsReasons:n}=a.result;Object.keys(n).forEach(i=>{const l=n[i];l&&l!=="商品已存在"&&(s.code=1,s.reason[i]=l,s.failed.add(Number(i)),r.delete(Number(i)))}),[...r.values()].forEach(i=>s.success.add(i))}if(c.size){const a=await z([...c.values()],t);console.log(a,"updateSupply"),a.success||(s.code=1,s.msg=f(a));const{failGoodsReasons:n={}}=a.result;Object.keys(n).forEach(i=>{const l=n[i];l&&l!=="商品已存在"&&(s.code=1,s.reason[i]=l,s.failed.add(Number(i)),c.delete(Number(i)))}),[...c.values()].forEach(i=>s.success.add(i))}return o.length===1&&(s.success.size&&(s.code=0),s.failed.size&&(s.code=1,s.msg=s.reason[o[0]])),s}async function R(e,t){t.bizId||(t.bizId=1);const s={url:"https://mms.pinduoduo.com/mille/mms/goods/deleteGoods",method:"post",headers:await u(e),data:t};return p(s)}async function G(e,t){var a;const o={method:"post",url:"https://mms.pinduoduo.com/mille/mms/goods/addGoods",data:{activityGoodsConfigs:(Array.isArray(e)?e:[e]).map(n=>({goodsId:Number(n),goodsLadderDiscounts:[{ladderStartValue:2,ladderDiscount:90}],supportDropShipping:0})),syncAllGoodsDiscount:!1,bizId:1},headers:await u(t,{referer:"https://mms.pinduoduo.com/supplier/wholesale/create"})},d=await p(o),r=(a=d.result)==null?void 0:a.verifyAuthToken;return r?await new Promise(n=>{h().openPddVerify({verify_auth_token:r,cb:n})})?p(o):{success:!1,error_msg:"未通过验证"}:d}async function z(e,t){var a;const s=Array.isArray(e)?e:[e],o={method:"post",url:"https://mms.pinduoduo.com/mille/mms/goods/batchUpdateGoodsDiscount",headers:await u(t),data:{operateSource:0,batchUpdate:s.map(n=>({goodsId:Number(n),goodsLadderDiscounts:[{ladderStartValue:2,ladderDiscount:90}],supportDropShipping:0})),syncAllGoodsDiscount:!1,bizId:1}},d=await p(o),r=(a=d.result)==null?void 0:a.verifyAuthToken;return r?await new Promise(n=>{h().openPddVerify({verify_auth_token:r,cb:n})})?p(o):{success:!1,error_msg:"未通过验证"}:d}function g(e,t,s=10){return b(async d=>{const r=w(0,e.sku_id.length),c={goods_id:e.goods_id,sku_id:e.sku_id[r]};return I(c)},s,500)}function k(e,t,s){if(!s)return g(e);let o;return typeof s=="number"||typeof s=="string"?o=_().tableList.find(r=>r.mallId===Number(s)):o=s,g(e,{},5).catch(async d=>{if(!o)return Promise.reject(d);const r=await A(e.goods_id,o);return r.code?(r.msg="供货失败："+r.msg,Promise.reject(r)):(await S(3e3),g(e))})}async function T(e,t){const s={start_time:y().startOf("day").valueOf().toString(),end_time:y().add(365,"day").endOf("day").valueOf().toString(),page:1,size:20,sort_by:"id",sort_type:"DESC",source_type:54,...e},o={method:"post",url:"https://mms.pinduoduo.com/madrid/query_filtered_mall_goods_list",headers:await u(t),data:s};return p(o)}export{E as a,k as b,M as c,R as d,P as e,A as g,T as q,N as s};
