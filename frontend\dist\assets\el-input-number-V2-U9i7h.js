import{aU as Y,cE as f,f1 as y,bn as se,a_ as h,e_ as F,a$ as X,df as oe,_ as ie,d as Z,aR as ce,a as de,a5 as me,t as pe,be,c as I,bf as fe,ev as ve,s as Ne,b1 as Ve,cA as Ie,b as K,o as v,a6 as j,a9 as H,i as O,e as t,N as P,n as L,k as W,T,bb as he,f2 as ye,E as q,dj as Ee,cG as _e,af as ge,O as R,bg as J,cD as we,aV as k,g as Se}from"./index-CmETSh6Y.js";const Ae=100,Pe=600,Q={beforeMount(l,N){const o=N.value,{interval:a=Ae,delay:E=Pe}=Y(o)?{}:o;let c,m;const u=()=>Y(o)?o():o.handler(),p=()=>{m&&(clearTimeout(m),m=void 0),c&&(clearInterval(c),c=void 0)};l.addEventListener("mousedown",_=>{_.button===0&&(p(),u(),document.addEventListener("mouseup",()=>p(),{once:!0}),m=setTimeout(()=>{c=setInterval(()=>{u()},a)},E))})}},Te=se({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:Number,readonly:Boolean,disabled:Boolean,size:oe,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:l=>l===null||f(l)||["min","max"].includes(l),default:null},name:String,label:String,placeholder:String,precision:{type:Number,validator:l=>l>=0&&l===Number.parseInt(`${l}`,10)},validateEvent:{type:Boolean,default:!0}}),ke={[X]:(l,N)=>l!==N,blur:l=>l instanceof FocusEvent,focus:l=>l instanceof FocusEvent,[F]:l=>f(l)||y(l),[h]:l=>f(l)||y(l)},Fe=["aria-label","onKeydown"],Ce=["aria-label","onKeydown"],xe=Z({name:"ElInputNumber"}),Be=Z({...xe,props:Te,emits:ke,setup(l,{expose:N,emit:o}){const a=l,{t:E}=ce(),c=de("input-number"),m=me(),u=pe({currentValue:a.modelValue,userInput:null}),{formItem:p}=be(),_=I(()=>f(a.modelValue)&&g(a.modelValue,-1)<a.min),U=I(()=>f(a.modelValue)&&g(a.modelValue)>a.max),ee=I(()=>{const e=G(a.step);return k(a.precision)?Math.max(G(a.modelValue),e):(e>a.precision,a.precision)}),C=I(()=>a.controls&&a.controlsPosition==="right"),$=fe(),V=ve(),x=I(()=>{if(u.userInput!==null)return u.userInput;let e=u.currentValue;if(y(e))return"";if(f(e)){if(Number.isNaN(e))return"";k(a.precision)||(e=e.toFixed(a.precision))}return e}),B=(e,n)=>{if(k(n)&&(n=ee.value),n===0)return Math.round(e);let r=String(e);const i=r.indexOf(".");if(i===-1||!r.replace(".","").split("")[i+n])return e;const S=r.length;return r.charAt(S-1)==="5"&&(r=`${r.slice(0,Math.max(0,S-1))}6`),Number.parseFloat(Number(r).toFixed(n))},G=e=>{if(y(e))return 0;const n=e.toString(),r=n.indexOf(".");let i=0;return r!==-1&&(i=n.length-r-1),i},g=(e,n=1)=>f(e)?B(e+a.step*n):u.currentValue,D=()=>{if(a.readonly||V.value||U.value)return;const e=Number(x.value)||0,n=g(e);w(n),o(F,u.currentValue)},M=()=>{if(a.readonly||V.value||_.value)return;const e=Number(x.value)||0,n=g(e,-1);w(n),o(F,u.currentValue)},z=(e,n)=>{const{max:r,min:i,step:s,precision:b,stepStrictly:S,valueOnClear:A}=a;let d=Number(e);if(y(e)||Number.isNaN(d))return null;if(e===""){if(A===null)return null;d=we(A)?{min:i,max:r}[A]:A}return S&&(d=B(Math.round(d/s)*s,b)),k(b)||(d=B(d,b)),(d>r||d<i)&&(d=d>r?r:i,n&&o(h,d)),d},w=(e,n=!0)=>{var r;const i=u.currentValue,s=z(e);if(i!==s){if(!n){o(h,s);return}u.userInput=null,o(h,s),o(X,s,i),a.validateEvent&&((r=p==null?void 0:p.validate)==null||r.call(p,"change").catch(b=>J())),u.currentValue=s}},ne=e=>{u.userInput=e;const n=e===""?null:Number(e);o(F,n),w(n,!1)},te=e=>{const n=e!==""?Number(e):"";(f(n)&&!Number.isNaN(n)||e==="")&&w(n),u.userInput=null},ae=()=>{var e,n;(n=(e=m.value)==null?void 0:e.focus)==null||n.call(e)},re=()=>{var e,n;(n=(e=m.value)==null?void 0:e.blur)==null||n.call(e)},le=e=>{o("focus",e)},ue=e=>{var n;o("blur",e),a.validateEvent&&((n=p==null?void 0:p.validate)==null||n.call(p,"blur").catch(r=>J()))};return Ne(()=>a.modelValue,e=>{const n=z(u.userInput),r=z(e,!0);(!n||n!==r)&&(u.currentValue=r,u.userInput=null)},{immediate:!0}),Ve(()=>{var e;const{min:n,max:r,modelValue:i}=a,s=(e=m.value)==null?void 0:e.input;if(s.setAttribute("role","spinbutton"),Number.isFinite(r)?s.setAttribute("aria-valuemax",String(r)):s.removeAttribute("aria-valuemax"),Number.isFinite(n)?s.setAttribute("aria-valuemin",String(n)):s.removeAttribute("aria-valuemin"),s.setAttribute("aria-valuenow",String(u.currentValue)),s.setAttribute("aria-disabled",String(V.value)),!f(i)&&i!=null){let b=Number(i);Number.isNaN(b)&&(b=null),o(h,b)}}),Ie(()=>{var e;const n=(e=m.value)==null?void 0:e.input;n==null||n.setAttribute("aria-valuenow",`${u.currentValue}`)}),N({focus:ae,blur:re}),(e,n)=>(v(),K("div",{class:L([t(c).b(),t(c).m(t($)),t(c).is("disabled",t(V)),t(c).is("without-controls",!e.controls),t(c).is("controls-right",t(C))]),onDragstart:n[0]||(n[0]=R(()=>{},["prevent"]))},[e.controls?j((v(),K("span",{key:0,role:"button","aria-label":t(E)("el.inputNumber.decrease"),class:L([t(c).e("decrease"),t(c).is("disabled",t(_))]),onKeydown:P(M,["enter"])},[O(t(q),null,{default:W(()=>[t(C)?(v(),T(t(he),{key:0})):(v(),T(t(ye),{key:1}))]),_:1})],42,Fe)),[[t(Q),M]]):H("v-if",!0),e.controls?j((v(),K("span",{key:1,role:"button","aria-label":t(E)("el.inputNumber.increase"),class:L([t(c).e("increase"),t(c).is("disabled",t(U))]),onKeydown:P(D,["enter"])},[O(t(q),null,{default:W(()=>[t(C)?(v(),T(t(Ee),{key:0})):(v(),T(t(_e),{key:1}))]),_:1})],42,Ce)),[[t(Q),D]]):H("v-if",!0),O(t(ge),{id:e.id,ref_key:"input",ref:m,type:"number",step:e.step,"model-value":t(x),placeholder:e.placeholder,readonly:e.readonly,disabled:t(V),size:t($),max:e.max,min:e.min,name:e.name,label:e.label,"validate-event":!1,onKeydown:[P(R(D,["prevent"]),["up"]),P(R(M,["prevent"]),["down"])],onBlur:ue,onFocus:le,onInput:ne,onChange:te},null,8,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","label","onKeydown"])],34))}});var De=ie(Be,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/input-number/src/input-number.vue"]]);const ze=Se(De);export{ze as E,Q as v};
