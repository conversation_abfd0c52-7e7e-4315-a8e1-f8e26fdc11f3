import{d5 as Y,d as fe,A as ge,t as T,d6 as _e,c as he,a5 as ye,G as F,ap as Z,a6 as h,az as be,o as m,b as P,h as g,i as l,T as v,a9 as K,k as t,af as ve,S as we,j as i,J as ke,E as xe,e as A,ax as Ce,ae as Ve,H as V,a7 as Q,ah as Le,M as Ee,P as Se,Q as De,R as Ae,n as Ie,L as Pe,ac as Te,d7 as Ue,d8 as $e,ak as w,d9 as ee,da as ze,bE as Re,bG as te,db as Be,a2 as qe,a3 as Me,m as Ge}from"./index-CmETSh6Y.js";/* empty css                   */import{E as je,a as Ne,b as Fe}from"./el-radio-DoZFrPxL.js";import{u as Ke,E as Qe}from"./el-config-provider-DRHX7QJ5.js";import{E as Ye}from"./el-date-picker-W7Ity7Gv.js";import{E as He}from"./el-input-number-V2-U9i7h.js";import{E as Je}from"./el-space-mudxGYIP.js";import{d as Oe}from"./dayjs.min-Bj8ADfnT.js";import{Q as We}from"./browser-GT_2EIZx.js";function Xe(x={},p,y){return Y({url:"/api/serve/getGoodsList",method:"get",data:x},p,y)}function Ze(x,p,y){return Y({url:"/api/serve/buyGoods",data:x},p,y)}function et(x,p,y){return Y({url:"/api/serve/getPayResult",data:x},p,y)}const $=x=>(qe("data-v-61c4918c"),x=x(),Me(),x),tt={class:"sub-account no-padding"},at={class:"padding"},lt={class:"table-header"},ot={class:"actions"},nt=$(()=>g("span",null,"导入小号",-1)),st={class:"able-select","element-loading-text":"导入中"},it={class:"primary"},rt={class:"controls"},ut=$(()=>g("span",{class:"f-s-14",style:{color:"var(--el-text-color-regular)"}},"删除小号不影响收货，评价等任何功能，只为方便管理下单 ",-1)),dt=$(()=>g("span",null,"至",-1)),ct={style:{color:"var(--el-color-primary)"}},mt=$(()=>g("del",{style:{"margin-left":"20px",color:"gray"},class:"old-price"},"原价：2.0/个",-1)),pt={key:0,class:"img-box",style:{border:"var(--el-border)",width:"300px",height:"300px",padding:"20px",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column"}},ft=["src"],gt={style:{"font-size":"40px",color:"var(--el-color-primary)","margin-top":"10px"}},_t=fe({__name:"subAccount",setup(x){const p=Ke();ge();const y=T({showList:[],total:0,limit:100,searchKey:"",page:1});_e(()=>{const{list:s}=p;console.log(s,"??什么");const{searchKey:e}=y,f=s.filter(o=>o.account.toString().includes(e));y.total=f.length,y.showList=f});const b=T({pageLoading:!1,tableLoading:!1,commboList:[],num:1,accountCami:""}),n=T({recover:!1,check:!1,uploadCami:!1,getCami:!1,delete:!1,commbo:!1,buyAccount:!1,onlineBuy:!1}),z=he(()=>b.commboList.length?b.commboList[0]:null);function S(){b.tableLoading=!0,p.getList().finally(()=>{b.tableLoading=!1})}S();function ae(){n.commbo||(n.commbo=!0,Xe().then(s=>{b.commboList=s.data}).finally(()=>{n.commbo=!1}))}ae();const d=T({type:1,title:"",dialog:!1,ind_max:1,ind_min:1,date:void 0});function H(s){d.type=s;let e="";switch(s){case 1:{e="按序号删除";break}case 2:{e="按日期删除";break}}d.title=e,d.dialog=!0}function le(){n.check=!0,$e().then(()=>(w.success("检查完毕"),S())).finally(()=>{n.check=!1})}function oe(s){s.length?ee({account:s.map(e=>e.account).join(",")}).then(e=>{console.log(e),w.success("删除成功"),S()}).finally(()=>{}):w.warning({message:"无可删除小号",grouping:!0})}function ne(){n.recover=!0,ze().then(s=>(console.log(s,"aa"),S())).finally(()=>{n.recover=!1})}function R(s=d.type){if(!p.list.length)return w.warning({message:"小号列表没有数据",grouping:!0});let{date:e,ind_max:f,ind_min:o}=d,r=[];switch(s){case 1:{o=o-1<0?0:o-1,r=p.list.slice(o,f);break}case 2:{if(e){const[u,k]=e;r=p.list.filter(L=>{let C=L.add_time*1e3;return C>=u&&C<=k})}else{w.warning("没有选择时间");return}break}case 3:{r=p.list.filter(u=>u.status.status!=1);break}case 4:{r=p.list.filter(u=>u.use_num>=p.useLimit);break}}r.length?(n.delete=!0,ee({account:r.map(u=>u.account).join(",")}).then(u=>{w.success("删除成功"),d.dialog=!1,S()}).finally(()=>{n.delete=!1})):(w.warning({message:"无可删除小号",grouping:!0}),d.dialog=!1)}const _=T({percentage:0,logs:[],logShow:!1,showType:-1});function B(s=b.accountCami.split(`
`).filter(Boolean)){if(console.log(s,b.accountCami),!s.length)return w.warning({message:"没有可上传的卡密",grouping:!0});_.logShow=!0,_.logs=[],n.uploadCami=!0,n.getCami=!0;const e=[];for(;s.length;)e.push(s.splice(0,100));const f=1;let o=0;function r(){const u=e.slice(o,f+o);o+=u.length;const k=u.map(L=>Be({card:L.join(",")},{showErrorMsg:!1}).then(C=>{_.logs.push(...C.data)}).catch(C=>{console.log("error",C),_.logs.push({code:1,msg:C.msg,card:"系统提示"})}));Promise.allSettled(k).then(L=>{_.percentage=Math.floor(o/e.length*100),o>=e.length?(n.uploadCami=!1,n.getCami=!1,S(),b.accountCami=""):n.uploadCami&&r()})}r()}async function se(s){let e=s;if(!e){const r=await Re({properties:["createDirectory"],filters:[{extensions:["txt"],name:""}]});if(r.canceled)return w({message:"取消文件选择"});e=r.filePaths[0]}if(!e)return w.warning("没有文件路径");const o=(await te({url:e})).split(/[\r\n]/).filter(r=>r);B(o)}function ie(s){var f;if(n.uploadCami)return w.warning({message:"当前有任务在上传,请稍后",grouping:!0});const e=(f=s.dataTransfer)==null?void 0:f.files;if(e&&e.length){const o=[];for(let u=0;u<e.length;u++){const k=e[u].path;k.endsWith(".txt")&&o.push(k)}if(!o.length)return w.warning({message:"选择的文件中没有txt格式的文件",grouping:!0});const r=[];Promise.all(o.map(u=>te({url:u}).then(k=>{k.split(/[\r\n]/).forEach(L=>{L&&r.push(L)})}))).then(()=>{r.length?B(r):w.warning({message:"选择的文件中没有获取到卡密数据",grouping:!0})});return}}const c=ye({num:10,show:!1,payImg:"",pay_type:"alipay",checkId:"",price:0});function re(){if(!z.value)return;n.buyAccount=!0;const{num:s,pay_type:e}=c.value,{ID:f}=z.value;Ze({pay_type:e,num:s,id:f}).then(r=>{c.value.price=r.data.money,c.value.checkId=r.data.id,We.toDataURL(r.data.qrcode).then(u=>{c.value.payImg=u}),J()}).finally(()=>{n.buyAccount=!1})}function J(){c.value.show&&et({id:c.value.checkId},{showErrorMsg:!1}).then(s=>{console.log(s,"success"),p.getList(),c.value.show=!1}).catch(s=>{setTimeout(()=>{J()},2e3)})}return(s,e)=>{const f=ve,o=we,r=xe,u=ke,k=Je,L=Ve,C=Le,q=Ne,O=je,I=Se,M=Ee,ue=F("VxeList"),G=Pe,D=F("vxe-column"),j=Te,de=F("vxe-table"),N=He,ce=Ye,me=Qe,W=Fe,E=Z("blur"),X=be,pe=Z("drop");return h((m(),P("div",tt,[g("div",at,[l(C,{size:"default"},{default:t(()=>[g("div",lt,[l(k,{size:10},{default:t(()=>[l(f,{placeholder:"搜索小号",modelValue:y.searchKey,"onUpdate:modelValue":e[0]||(e[0]=a=>y.searchKey=a)},null,8,["modelValue"]),h((m(),v(o,{onClick:le,loading:n.check,disabled:n.check},{default:t(()=>[i("检测小号")]),_:1},8,["loading","disabled"])),[[E]]),h((m(),v(o,{title:"只有还能用的小号才可恢复",onClick:ne,type:"success",loading:n.recover,disabled:n.recover},{default:t(()=>[i("恢复删除小号")]),_:1},8,["loading","disabled"])),[[E]]),l(u,{underline:!1,type:"primary",onClick:e[1]||(e[1]=a=>S())},{default:t(()=>[l(r,{class:"m-r-5"},{default:t(()=>[l(A(Ce))]),_:1}),i(" 同步小号 ")]),_:1})]),_:1}),g("div",ot,[l(f,{type:"textarea",resize:"none",rows:1,placeholder:"请输入小号卡密",class:"m-r-8",modelValue:b.accountCami,"onUpdate:modelValue":e[2]||(e[2]=a=>b.accountCami=a)},null,8,["modelValue"]),h((m(),v(o,{type:"primary",onClick:e[3]||(e[3]=a=>B()),disabled:!b.accountCami||n.getCami,loading:n.getCami},{default:t(()=>[i("读取小号")]),_:1},8,["disabled","loading"])),[[E]]),l(L,{direction:"vertical"}),h((m(),v(o,{type:"success",onClick:e[4]||(e[4]=a=>se()),loading:n.uploadCami,disabled:n.uploadCami},{default:t(()=>[nt,h(g("span",null,V(_.percentage)+"%",513),[[Q,n.uploadCami]])]),_:1},8,["loading","disabled"])),[[E]])])])]),_:1}),l(G,{title:"导入分析",width:480,class:"dialog-480",modelValue:_.logShow,"onUpdate:modelValue":e[6]||(e[6]=a=>_.logShow=a)},{default:t(()=>[l(M,null,{default:t(()=>[l(I,{lable:"展示"},{default:t(()=>[l(O,{modelValue:_.showType,"onUpdate:modelValue":e[5]||(e[5]=a=>_.showType=a)},{default:t(()=>[l(q,{label:-1},{default:t(()=>[i("全部("+V(_.logs.length)+")",1)]),_:1}),l(q,{label:0},{default:t(()=>[i("仅成功")]),_:1}),l(q,{label:1},{default:t(()=>[i("仅失败")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),h((m(),P("div",st,[l(ue,{data:_.logs.filter(a=>_.showType<0?!0:a.code===_.showType),height:400},{default:t(({items:a})=>[(m(!0),P(De,null,Ae(a,(U,ht)=>(m(),P("p",{class:Ie(U.code?"danger":"success")},V(U.card)+": "+V(U.msg),3))),256))]),_:1},8,["data"])])),[[X,n.uploadCami]])]),_:1},8,["modelValue"]),h((m(),P("div",{class:"table-container able-select",onDrop:ie},[l(de,{border:"","column-config":{resizable:!0},loading:b.tableLoading,stripe:"",data:y.showList,height:"650","row-config":{height:40},"show-header-overflow":"tooltip","show-overflow":"tooltip"},{default:t(()=>[l(D,{type:"seq",width:"100",title:"序号"}),l(D,{field:"account",title:"小号"}),l(D,{field:"use_num",title:"使用次数"}),l(D,{field:"status.status",title:"状态",width:"100"},{default:t(a=>[a.row.status.status==1&&a.row.use_num>=A(p).useLimit?(m(),v(j,{key:0,type:"warning"},{default:t(()=>[i("已用完")]),_:1})):a.row.status.status==1?(m(),v(j,{key:1,type:"success"},{default:t(()=>[i("正常")]),_:1})):(m(),v(j,{key:2,type:"info"},{default:t(()=>[i("失效")]),_:1}))]),_:1}),l(D,{field:"add_time",title:"创建时间"},{default:t(a=>[i(V(A(Oe)(a.row.add_time*1e3).format("YYYY-MM-DD HH:mm:ss")),1)]),_:1}),l(D,{field:"past_time",title:"剩余时间"},{default:t(a=>[i(V(A(Ue)(a.row.past_time*1e3-Date.now())),1)]),_:1}),l(D,{filed:"action",title:"操作",width:"120"},{default:t(a=>[l(u,{underline:!1,type:"danger",onClick:U=>oe([a.row])},{default:t(()=>[i("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["loading","data"]),g("footer",null,[i(" 共 "),g("span",it,V(y.total)+" / "+V(A(p).total),1),i(" 条数据 ")])],32)),[[pe]])]),l(C,{size:"default"},{default:t(()=>[g("footer",rt,[l(k,{size:6},{default:t(()=>[ut,h((m(),v(o,{onClick:e[7]||(e[7]=a=>H(1))},{default:t(()=>[i("按序号删")]),_:1})),[[E]]),h((m(),v(o,{onClick:e[8]||(e[8]=a=>H(2))},{default:t(()=>[i("按日期删")]),_:1})),[[E]]),h((m(),v(o,{onClick:e[9]||(e[9]=a=>R(3))},{default:t(()=>[i("删除无效")]),_:1})),[[E]]),h((m(),v(o,{onClick:e[10]||(e[10]=a=>R(4))},{default:t(()=>[i("删除已用完")]),_:1})),[[E]])]),_:1})])]),_:1}),l(G,{width:400,title:d.title,modelValue:d.dialog,"onUpdate:modelValue":e[16]||(e[16]=a=>d.dialog=a),class:"dialog-480"},{footer:t(()=>[l(o,{onClick:e[14]||(e[14]=a=>d.dialog=!1)},{default:t(()=>[i("取消")]),_:1}),l(o,{loading:n.delete,disabled:n.delete,type:"primary",onClick:e[15]||(e[15]=a=>R())},{default:t(()=>[i("确定")]),_:1},8,["loading","disabled"])]),default:t(()=>[l(M,{"label-position":"top"},{default:t(()=>[h(l(I,{label:"输入序号"},{default:t(()=>[l(k,{size:8},{default:t(()=>[l(N,{controls:!1,modelValue:d.ind_min,"onUpdate:modelValue":e[11]||(e[11]=a=>d.ind_min=a),min:0,max:d.ind_max},null,8,["modelValue","max"]),dt,l(N,{controls:!1,modelValue:d.ind_max,"onUpdate:modelValue":e[12]||(e[12]=a=>d.ind_max=a),min:d.ind_min,max:A(p).list.length},null,8,["modelValue","min","max"])]),_:1})]),_:1},512),[[Q,d.type===1]]),h(l(I,{label:"选择日期"},{default:t(()=>[l(ce,{modelValue:d.date,"onUpdate:modelValue":e[13]||(e[13]=a=>d.date=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"x"},null,8,["modelValue"])]),_:1},512),[[Q,d.type===2]])]),_:1})]),_:1},8,["title","modelValue"]),c.value.show?(m(),v(G,{key:0,title:"提取小号",width:480,modelValue:c.value.show,"onUpdate:modelValue":e[20]||(e[20]=a=>c.value.show=a),"close-on-click-modal":!1},{footer:t(()=>[l(o,{onClick:e[19]||(e[19]=a=>c.value.show=!1)},{default:t(()=>[i("取消")]),_:1}),c.value.payImg?K("",!0):(m(),v(o,{key:0,type:"primary",onClick:re,loading:n.buyAccount},{default:t(()=>[i("提取")]),_:1},8,["loading"]))]),default:t(()=>[l(me,{type:"success",closable:!1,size:"default"},{default:t(()=>{var a;return[g("p",null,[g("span",ct," 价格："+V(((a=A(z))==null?void 0:a.price)||"-")+"/个 ",1),mt])]}),_:1}),l(M,{"label-position":"top",style:{"margin-top":"20px"}},{default:t(()=>[l(I,{label:"提取数量"},{default:t(()=>[l(N,{precision:0,min:1,modelValue:c.value.num,"onUpdate:modelValue":e[17]||(e[17]=a=>c.value.num=a)},null,8,["modelValue"])]),_:1}),l(I,{label:"支付方式"},{default:t(()=>[l(O,{modelValue:c.value.pay_type,"onUpdate:modelValue":e[18]||(e[18]=a=>c.value.pay_type=a)},{default:t(()=>[l(W,{label:"wxpay"},{default:t(()=>[i("微信")]),_:1}),l(W,{label:"alipay"},{default:t(()=>[i("支付宝")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(I,{label:"二维码"},{default:t(()=>[c.value.payImg?(m(),P("div",pt,[g("img",{src:c.value.payImg,alt:"",style:{"max-width":"100%","max-height":"100%"}},null,8,ft),g("p",gt,V(c.value.price),1)])):K("",!0)]),_:1})]),_:1})]),_:1},8,["modelValue"])):K("",!0)])),[[X,b.pageLoading]])}}}),Et=Ge(_t,[["__scopeId","data-v-61c4918c"]]);export{Et as default};
