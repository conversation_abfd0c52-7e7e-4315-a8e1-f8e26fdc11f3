import{dc as oe}from"./index-CmETSh6Y.js";var z={},Q,At;function ie(){return At||(At=1,Q=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),Q}var $={},U={},Rt;function F(){if(Rt)return U;Rt=1;let r;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return U.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17},U.getSymbolTotalCodewords=function(n){return o[n]},U.getBCHDigit=function(i){let n=0;for(;i!==0;)n++,i>>>=1;return n},U.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');r=n},U.isKanjiModeEnabled=function(){return typeof r<"u"},U.toSJIS=function(n){return r(n)},U}var W={},It;function Et(){return It||(It=1,function(r){r.L={bit:1},r.M={bit:0},r.Q={bit:3},r.H={bit:2};function o(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"l":case"low":return r.L;case"m":case"medium":return r.M;case"q":case"quartile":return r.Q;case"h":case"high":return r.H;default:throw new Error("Unknown EC Level: "+i)}}r.isValid=function(n){return n&&typeof n.bit<"u"&&n.bit>=0&&n.bit<4},r.from=function(n,t){if(r.isValid(n))return n;try{return o(n)}catch{return t}}}(W)),W}var Z,Tt;function ue(){if(Tt)return Z;Tt=1;function r(){this.buffer=[],this.length=0}return r.prototype={get:function(o){const i=Math.floor(o/8);return(this.buffer[i]>>>7-o%8&1)===1},put:function(o,i){for(let n=0;n<i;n++)this.putBit((o>>>i-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const i=Math.floor(this.length/8);this.buffer.length<=i&&this.buffer.push(0),o&&(this.buffer[i]|=128>>>this.length%8),this.length++}},Z=r,Z}var X,Nt;function se(){if(Nt)return X;Nt=1;function r(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return r.prototype.set=function(o,i,n,t){const e=o*this.size+i;this.data[e]=n,t&&(this.reservedBit[e]=!0)},r.prototype.get=function(o,i){return this.data[o*this.size+i]},r.prototype.xor=function(o,i,n){this.data[o*this.size+i]^=n},r.prototype.isReserved=function(o,i){return this.reservedBit[o*this.size+i]},X=r,X}var x={},Mt;function ae(){return Mt||(Mt=1,function(r){const o=F().getSymbolSize;r.getRowColCoords=function(n){if(n===1)return[];const t=Math.floor(n/7)+2,e=o(n),u=e===145?26:Math.ceil((e-13)/(2*t-2))*2,a=[e-7];for(let s=1;s<t-1;s++)a[s]=a[s-1]-u;return a.push(6),a.reverse()},r.getPositions=function(n){const t=[],e=r.getRowColCoords(n),u=e.length;for(let a=0;a<u;a++)for(let s=0;s<u;s++)a===0&&s===0||a===0&&s===u-1||a===u-1&&s===0||t.push([e[a],e[s]]);return t}}(x)),x}var tt={},Pt;function ce(){if(Pt)return tt;Pt=1;const r=F().getSymbolSize,o=7;return tt.getPositions=function(n){const t=r(n);return[[0,0],[t-o,0],[0,t-o]]},tt}var et={},St;function fe(){return St||(St=1,function(r){r.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};r.isValid=function(t){return t!=null&&t!==""&&!isNaN(t)&&t>=0&&t<=7},r.from=function(t){return r.isValid(t)?parseInt(t,10):void 0},r.getPenaltyN1=function(t){const e=t.size;let u=0,a=0,s=0,c=null,d=null;for(let B=0;B<e;B++){a=s=0,c=d=null;for(let h=0;h<e;h++){let f=t.get(B,h);f===c?a++:(a>=5&&(u+=o.N1+(a-5)),c=f,a=1),f=t.get(h,B),f===d?s++:(s>=5&&(u+=o.N1+(s-5)),d=f,s=1)}a>=5&&(u+=o.N1+(a-5)),s>=5&&(u+=o.N1+(s-5))}return u},r.getPenaltyN2=function(t){const e=t.size;let u=0;for(let a=0;a<e-1;a++)for(let s=0;s<e-1;s++){const c=t.get(a,s)+t.get(a,s+1)+t.get(a+1,s)+t.get(a+1,s+1);(c===4||c===0)&&u++}return u*o.N2},r.getPenaltyN3=function(t){const e=t.size;let u=0,a=0,s=0;for(let c=0;c<e;c++){a=s=0;for(let d=0;d<e;d++)a=a<<1&2047|t.get(c,d),d>=10&&(a===1488||a===93)&&u++,s=s<<1&2047|t.get(d,c),d>=10&&(s===1488||s===93)&&u++}return u*o.N3},r.getPenaltyN4=function(t){let e=0;const u=t.data.length;for(let s=0;s<u;s++)e+=t.data[s];return Math.abs(Math.ceil(e*100/u/5)-10)*o.N4};function i(n,t,e){switch(n){case r.Patterns.PATTERN000:return(t+e)%2===0;case r.Patterns.PATTERN001:return t%2===0;case r.Patterns.PATTERN010:return e%3===0;case r.Patterns.PATTERN011:return(t+e)%3===0;case r.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(e/3))%2===0;case r.Patterns.PATTERN101:return t*e%2+t*e%3===0;case r.Patterns.PATTERN110:return(t*e%2+t*e%3)%2===0;case r.Patterns.PATTERN111:return(t*e%3+(t+e)%2)%2===0;default:throw new Error("bad maskPattern:"+n)}}r.applyMask=function(t,e){const u=e.size;for(let a=0;a<u;a++)for(let s=0;s<u;s++)e.isReserved(s,a)||e.xor(s,a,i(t,s,a))},r.getBestMask=function(t,e){const u=Object.keys(r.Patterns).length;let a=0,s=1/0;for(let c=0;c<u;c++){e(c),r.applyMask(c,t);const d=r.getPenaltyN1(t)+r.getPenaltyN2(t)+r.getPenaltyN3(t)+r.getPenaltyN4(t);r.applyMask(c,t),d<s&&(s=d,a=c)}return a}}(et)),et}var J={},bt;function Zt(){if(bt)return J;bt=1;const r=Et(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return J.getBlocksCount=function(t,e){switch(e){case r.L:return o[(t-1)*4+0];case r.M:return o[(t-1)*4+1];case r.Q:return o[(t-1)*4+2];case r.H:return o[(t-1)*4+3];default:return}},J.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return i[(t-1)*4+0];case r.M:return i[(t-1)*4+1];case r.Q:return i[(t-1)*4+2];case r.H:return i[(t-1)*4+3];default:return}},J}var nt={},K={},Lt;function le(){if(Lt)return K;Lt=1;const r=new Uint8Array(512),o=new Uint8Array(256);return function(){let n=1;for(let t=0;t<255;t++)r[t]=n,o[n]=t,n<<=1,n&256&&(n^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),K.log=function(n){if(n<1)throw new Error("log("+n+")");return o[n]},K.exp=function(n){return r[n]},K.mul=function(n,t){return n===0||t===0?0:r[o[n]+o[t]]},K}var Dt;function de(){return Dt||(Dt=1,function(r){const o=le();r.mul=function(n,t){const e=new Uint8Array(n.length+t.length-1);for(let u=0;u<n.length;u++)for(let a=0;a<t.length;a++)e[u+a]^=o.mul(n[u],t[a]);return e},r.mod=function(n,t){let e=new Uint8Array(n);for(;e.length-t.length>=0;){const u=e[0];for(let s=0;s<t.length;s++)e[s]^=o.mul(t[s],u);let a=0;for(;a<e.length&&e[a]===0;)a++;e=e.slice(a)}return e},r.generateECPolynomial=function(n){let t=new Uint8Array([1]);for(let e=0;e<n;e++)t=r.mul(t,new Uint8Array([1,o.exp(e)]));return t}}(nt)),nt}var rt,vt;function ge(){if(vt)return rt;vt=1;const r=de();function o(i){this.genPoly=void 0,this.degree=i,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(n){this.degree=n,this.genPoly=r.generateECPolynomial(this.degree)},o.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(n.length+this.degree);t.set(n);const e=r.mod(t,this.genPoly),u=this.degree-e.length;if(u>0){const a=new Uint8Array(this.degree);return a.set(e,u),a}return e},rt=o,rt}var ot={},it={},ut={},qt;function Xt(){return qt||(qt=1,ut.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),ut}var L={},Ut;function xt(){if(Ut)return L;Ut=1;const r="[0-9]+",o="[A-Z $%*+\\-./:]+";let i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";i=i.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+i+`)(?:.|[\r
]))+`;L.KANJI=new RegExp(i,"g"),L.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),L.BYTE=new RegExp(n,"g"),L.NUMERIC=new RegExp(r,"g"),L.ALPHANUMERIC=new RegExp(o,"g");const t=new RegExp("^"+i+"$"),e=new RegExp("^"+r+"$"),u=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return L.testKanji=function(s){return t.test(s)},L.testNumeric=function(s){return e.test(s)},L.testAlphanumeric=function(s){return u.test(s)},L}var Ft;function _(){return Ft||(Ft=1,function(r){const o=Xt(),i=xt();r.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},r.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},r.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},r.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},r.MIXED={bit:-1},r.getCharCountIndicator=function(e,u){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!o.isValid(u))throw new Error("Invalid version: "+u);return u>=1&&u<10?e.ccBits[0]:u<27?e.ccBits[1]:e.ccBits[2]},r.getBestModeForData=function(e){return i.testNumeric(e)?r.NUMERIC:i.testAlphanumeric(e)?r.ALPHANUMERIC:i.testKanji(e)?r.KANJI:r.BYTE},r.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},r.isValid=function(e){return e&&e.bit&&e.ccBits};function n(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return r.NUMERIC;case"alphanumeric":return r.ALPHANUMERIC;case"kanji":return r.KANJI;case"byte":return r.BYTE;default:throw new Error("Unknown mode: "+t)}}r.from=function(e,u){if(r.isValid(e))return e;try{return n(e)}catch{return u}}}(it)),it}var _t;function he(){return _t||(_t=1,function(r){const o=F(),i=Zt(),n=Et(),t=_(),e=Xt(),u=7973,a=o.getBCHDigit(u);function s(h,f,N){for(let M=1;M<=40;M++)if(f<=r.getCapacity(M,N,h))return M}function c(h,f){return t.getCharCountIndicator(h,f)+4}function d(h,f){let N=0;return h.forEach(function(M){const S=c(M.mode,f);N+=S+M.getBitsLength()}),N}function B(h,f){for(let N=1;N<=40;N++)if(d(h,N)<=r.getCapacity(N,f,t.MIXED))return N}r.from=function(f,N){return e.isValid(f)?parseInt(f,10):N},r.getCapacity=function(f,N,M){if(!e.isValid(f))throw new Error("Invalid QR Code version");typeof M>"u"&&(M=t.BYTE);const S=o.getSymbolTotalCodewords(f),R=i.getTotalCodewordsCount(f,N),P=(S-R)*8;if(M===t.MIXED)return P;const I=P-c(M,f);switch(M){case t.NUMERIC:return Math.floor(I/10*3);case t.ALPHANUMERIC:return Math.floor(I/11*2);case t.KANJI:return Math.floor(I/13);case t.BYTE:default:return Math.floor(I/8)}},r.getBestVersionForData=function(f,N){let M;const S=n.from(N,n.M);if(Array.isArray(f)){if(f.length>1)return B(f,S);if(f.length===0)return 1;M=f[0]}else M=f;return s(M.mode,M.getLength(),S)},r.getEncodedBits=function(f){if(!e.isValid(f)||f<7)throw new Error("Invalid QR Code version");let N=f<<12;for(;o.getBCHDigit(N)-a>=0;)N^=u<<o.getBCHDigit(N)-a;return f<<12|N}}(ot)),ot}var st={},kt;function me(){if(kt)return st;kt=1;const r=F(),o=1335,i=21522,n=r.getBCHDigit(o);return st.getEncodedBits=function(e,u){const a=e.bit<<3|u;let s=a<<10;for(;r.getBCHDigit(s)-n>=0;)s^=o<<r.getBCHDigit(s)-n;return(a<<10|s)^i},st}var at={},ct,zt;function we(){if(zt)return ct;zt=1;const r=_();function o(i){this.mode=r.NUMERIC,this.data=i.toString()}return o.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(n){let t,e,u;for(t=0;t+3<=this.data.length;t+=3)e=this.data.substr(t,3),u=parseInt(e,10),n.put(u,10);const a=this.data.length-t;a>0&&(e=this.data.substr(t),u=parseInt(e,10),n.put(u,a*3+1))},ct=o,ct}var ft,Vt;function Ce(){if(Vt)return ft;Vt=1;const r=_(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(n){this.mode=r.ALPHANUMERIC,this.data=n}return i.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let u=o.indexOf(this.data[e])*45;u+=o.indexOf(this.data[e+1]),t.put(u,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},ft=i,ft}var lt,Kt;function Ee(){return Kt||(Kt=1,lt=function(o){for(var i=[],n=o.length,t=0;t<n;t++){var e=o.charCodeAt(t);if(e>=55296&&e<=56319&&n>t+1){var u=o.charCodeAt(t+1);u>=56320&&u<=57343&&(e=(e-55296)*1024+u-56320+65536,t+=1)}if(e<128){i.push(e);continue}if(e<2048){i.push(e>>6|192),i.push(e&63|128);continue}if(e<55296||e>=57344&&e<65536){i.push(e>>12|224),i.push(e>>6&63|128),i.push(e&63|128);continue}if(e>=65536&&e<=1114111){i.push(e>>18|240),i.push(e>>12&63|128),i.push(e>>6&63|128),i.push(e&63|128);continue}i.push(239,191,189)}return new Uint8Array(i).buffer}),lt}var dt,Ht;function ye(){if(Ht)return dt;Ht=1;const r=Ee(),o=_();function i(n){this.mode=o.BYTE,typeof n=="string"&&(n=r(n)),this.data=new Uint8Array(n)}return i.getBitsLength=function(t){return t*8},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(n){for(let t=0,e=this.data.length;t<e;t++)n.put(this.data[t],8)},dt=i,dt}var gt,Jt;function Be(){if(Jt)return gt;Jt=1;const r=_(),o=F();function i(n){this.mode=r.KANJI,this.data=n}return i.getBitsLength=function(t){return t*13},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(n){let t;for(t=0;t<this.data.length;t++){let e=o.toSJIS(this.data[t]);if(e>=33088&&e<=40956)e-=33088;else if(e>=57408&&e<=60351)e-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);e=(e>>>8&255)*192+(e&255),n.put(e,13)}},gt=i,gt}var ht={exports:{}},Yt;function pe(){return Yt||(Yt=1,function(r){var o={single_source_shortest_paths:function(i,n,t){var e={},u={};u[n]=0;var a=o.PriorityQueue.make();a.push(n,0);for(var s,c,d,B,h,f,N,M,S;!a.empty();){s=a.pop(),c=s.value,B=s.cost,h=i[c]||{};for(d in h)h.hasOwnProperty(d)&&(f=h[d],N=B+f,M=u[d],S=typeof u[d]>"u",(S||M>N)&&(u[d]=N,a.push(d,N),e[d]=c))}if(typeof t<"u"&&typeof u[t]>"u"){var R=["Could not find a path from ",n," to ",t,"."].join("");throw new Error(R)}return e},extract_shortest_path_from_predecessor_list:function(i,n){for(var t=[],e=n;e;)t.push(e),i[e],e=i[e];return t.reverse(),t},find_path:function(i,n,t){var e=o.single_source_shortest_paths(i,n,t);return o.extract_shortest_path_from_predecessor_list(e,t)},PriorityQueue:{make:function(i){var n=o.PriorityQueue,t={},e;i=i||{};for(e in n)n.hasOwnProperty(e)&&(t[e]=n[e]);return t.queue=[],t.sorter=i.sorter||n.default_sorter,t},default_sorter:function(i,n){return i.cost-n.cost},push:function(i,n){var t={value:i,cost:n};this.queue.push(t),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};r.exports=o}(ht)),ht.exports}var jt;function Ae(){return jt||(jt=1,function(r){const o=_(),i=we(),n=Ce(),t=ye(),e=Be(),u=xt(),a=F(),s=pe();function c(R){return unescape(encodeURIComponent(R)).length}function d(R,P,I){const p=[];let b;for(;(b=R.exec(I))!==null;)p.push({data:b[0],index:b.index,mode:P,length:b[0].length});return p}function B(R){const P=d(u.NUMERIC,o.NUMERIC,R),I=d(u.ALPHANUMERIC,o.ALPHANUMERIC,R);let p,b;return a.isKanjiModeEnabled()?(p=d(u.BYTE,o.BYTE,R),b=d(u.KANJI,o.KANJI,R)):(p=d(u.BYTE_KANJI,o.BYTE,R),b=[]),P.concat(I,p,b).sort(function(E,C){return E.index-C.index}).map(function(E){return{data:E.data,mode:E.mode,length:E.length}})}function h(R,P){switch(P){case o.NUMERIC:return i.getBitsLength(R);case o.ALPHANUMERIC:return n.getBitsLength(R);case o.KANJI:return e.getBitsLength(R);case o.BYTE:return t.getBitsLength(R)}}function f(R){return R.reduce(function(P,I){const p=P.length-1>=0?P[P.length-1]:null;return p&&p.mode===I.mode?(P[P.length-1].data+=I.data,P):(P.push(I),P)},[])}function N(R){const P=[];for(let I=0;I<R.length;I++){const p=R[I];switch(p.mode){case o.NUMERIC:P.push([p,{data:p.data,mode:o.ALPHANUMERIC,length:p.length},{data:p.data,mode:o.BYTE,length:p.length}]);break;case o.ALPHANUMERIC:P.push([p,{data:p.data,mode:o.BYTE,length:p.length}]);break;case o.KANJI:P.push([p,{data:p.data,mode:o.BYTE,length:c(p.data)}]);break;case o.BYTE:P.push([{data:p.data,mode:o.BYTE,length:c(p.data)}])}}return P}function M(R,P){const I={},p={start:{}};let b=["start"];for(let g=0;g<R.length;g++){const E=R[g],C=[];for(let l=0;l<E.length;l++){const A=E[l],m=""+g+l;C.push(m),I[m]={node:A,lastCount:0},p[m]={};for(let y=0;y<b.length;y++){const w=b[y];I[w]&&I[w].node.mode===A.mode?(p[w][m]=h(I[w].lastCount+A.length,A.mode)-h(I[w].lastCount,A.mode),I[w].lastCount+=A.length):(I[w]&&(I[w].lastCount=A.length),p[w][m]=h(A.length,A.mode)+4+o.getCharCountIndicator(A.mode,P))}}b=C}for(let g=0;g<b.length;g++)p[b[g]].end=0;return{map:p,table:I}}function S(R,P){let I;const p=o.getBestModeForData(R);if(I=o.from(P,p),I!==o.BYTE&&I.bit<p.bit)throw new Error('"'+R+'" cannot be encoded with mode '+o.toString(I)+`.
 Suggested mode is: `+o.toString(p));switch(I===o.KANJI&&!a.isKanjiModeEnabled()&&(I=o.BYTE),I){case o.NUMERIC:return new i(R);case o.ALPHANUMERIC:return new n(R);case o.KANJI:return new e(R);case o.BYTE:return new t(R)}}r.fromArray=function(P){return P.reduce(function(I,p){return typeof p=="string"?I.push(S(p,null)):p.data&&I.push(S(p.data,p.mode)),I},[])},r.fromString=function(P,I){const p=B(P,a.isKanjiModeEnabled()),b=N(p),g=M(b,I),E=s.find_path(g.map,"start","end"),C=[];for(let l=1;l<E.length-1;l++)C.push(g.table[E[l]].node);return r.fromArray(f(C))},r.rawSplit=function(P){return r.fromArray(B(P,a.isKanjiModeEnabled()))}}(at)),at}var Ot;function Re(){if(Ot)return $;Ot=1;const r=F(),o=Et(),i=ue(),n=se(),t=ae(),e=ce(),u=fe(),a=Zt(),s=ge(),c=he(),d=me(),B=_(),h=Ae();function f(g,E){const C=g.size,l=e.getPositions(E);for(let A=0;A<l.length;A++){const m=l[A][0],y=l[A][1];for(let w=-1;w<=7;w++)if(!(m+w<=-1||C<=m+w))for(let T=-1;T<=7;T++)y+T<=-1||C<=y+T||(w>=0&&w<=6&&(T===0||T===6)||T>=0&&T<=6&&(w===0||w===6)||w>=2&&w<=4&&T>=2&&T<=4?g.set(m+w,y+T,!0,!0):g.set(m+w,y+T,!1,!0))}}function N(g){const E=g.size;for(let C=8;C<E-8;C++){const l=C%2===0;g.set(C,6,l,!0),g.set(6,C,l,!0)}}function M(g,E){const C=t.getPositions(E);for(let l=0;l<C.length;l++){const A=C[l][0],m=C[l][1];for(let y=-2;y<=2;y++)for(let w=-2;w<=2;w++)y===-2||y===2||w===-2||w===2||y===0&&w===0?g.set(A+y,m+w,!0,!0):g.set(A+y,m+w,!1,!0)}}function S(g,E){const C=g.size,l=c.getEncodedBits(E);let A,m,y;for(let w=0;w<18;w++)A=Math.floor(w/3),m=w%3+C-8-3,y=(l>>w&1)===1,g.set(A,m,y,!0),g.set(m,A,y,!0)}function R(g,E,C){const l=g.size,A=d.getEncodedBits(E,C);let m,y;for(m=0;m<15;m++)y=(A>>m&1)===1,m<6?g.set(m,8,y,!0):m<8?g.set(m+1,8,y,!0):g.set(l-15+m,8,y,!0),m<8?g.set(8,l-m-1,y,!0):m<9?g.set(8,15-m-1+1,y,!0):g.set(8,15-m-1,y,!0);g.set(l-8,8,1,!0)}function P(g,E){const C=g.size;let l=-1,A=C-1,m=7,y=0;for(let w=C-1;w>0;w-=2)for(w===6&&w--;;){for(let T=0;T<2;T++)if(!g.isReserved(A,w-T)){let q=!1;y<E.length&&(q=(E[y]>>>m&1)===1),g.set(A,w-T,q),m--,m===-1&&(y++,m=7)}if(A+=l,A<0||C<=A){A-=l,l=-l;break}}}function I(g,E,C){const l=new i;C.forEach(function(T){l.put(T.mode.bit,4),l.put(T.getLength(),B.getCharCountIndicator(T.mode,g)),T.write(l)});const A=r.getSymbolTotalCodewords(g),m=a.getTotalCodewordsCount(g,E),y=(A-m)*8;for(l.getLengthInBits()+4<=y&&l.put(0,4);l.getLengthInBits()%8!==0;)l.putBit(0);const w=(y-l.getLengthInBits())/8;for(let T=0;T<w;T++)l.put(T%2?17:236,8);return p(l,g,E)}function p(g,E,C){const l=r.getSymbolTotalCodewords(E),A=a.getTotalCodewordsCount(E,C),m=l-A,y=a.getBlocksCount(E,C),w=l%y,T=y-w,q=Math.floor(l/y),V=Math.floor(m/y),ee=V+1,yt=q-V,ne=new s(yt);let Y=0;const H=new Array(y),Bt=new Array(y);let j=0;const re=new Uint8Array(g.buffer);for(let k=0;k<y;k++){const G=k<T?V:ee;H[k]=re.slice(Y,Y+G),Bt[k]=ne.encode(H[k]),Y+=G,j=Math.max(j,G)}const O=new Uint8Array(l);let pt=0,D,v;for(D=0;D<j;D++)for(v=0;v<y;v++)D<H[v].length&&(O[pt++]=H[v][D]);for(D=0;D<yt;D++)for(v=0;v<y;v++)O[pt++]=Bt[v][D];return O}function b(g,E,C,l){let A;if(Array.isArray(g))A=h.fromArray(g);else if(typeof g=="string"){let q=E;if(!q){const V=h.rawSplit(g);q=c.getBestVersionForData(V,C)}A=h.fromString(g,q||40)}else throw new Error("Invalid data");const m=c.getBestVersionForData(A,C);if(!m)throw new Error("The amount of data is too big to be stored in a QR Code");if(!E)E=m;else if(E<m)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+m+`.
`);const y=I(E,C,A),w=r.getSymbolSize(E),T=new n(w);return f(T,E),N(T),M(T,E),R(T,C,0),E>=7&&S(T,E),P(T,y),isNaN(l)&&(l=u.getBestMask(T,R.bind(null,T,C))),u.applyMask(l,T),R(T,C,l),{modules:T,version:E,errorCorrectionLevel:C,maskPattern:l,segments:A}}return $.create=function(E,C){if(typeof E>"u"||E==="")throw new Error("No input text");let l=o.M,A,m;return typeof C<"u"&&(l=o.from(C.errorCorrectionLevel,o.M),A=c.from(C.version),m=u.from(C.maskPattern),C.toSJISFunc&&r.setToSJISFunction(C.toSJISFunc)),b(E,A,l,m)},$}var mt={},wt={},Gt;function te(){return Gt||(Gt=1,function(r){function o(i){if(typeof i=="number"&&(i=i.toString()),typeof i!="string")throw new Error("Color should be defined as hex string");let n=i.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+i);(n.length===3||n.length===4)&&(n=Array.prototype.concat.apply([],n.map(function(e){return[e,e]}))),n.length===6&&n.push("F","F");const t=parseInt(n.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:t&255,hex:"#"+n.slice(0,6).join("")}}r.getOptions=function(n){n||(n={}),n.color||(n.color={});const t=typeof n.margin>"u"||n.margin===null||n.margin<0?4:n.margin,e=n.width&&n.width>=21?n.width:void 0,u=n.scale||4;return{width:e,scale:e?4:u,margin:t,color:{dark:o(n.color.dark||"#000000ff"),light:o(n.color.light||"#ffffffff")},type:n.type,rendererOpts:n.rendererOpts||{}}},r.getScale=function(n,t){return t.width&&t.width>=n+t.margin*2?t.width/(n+t.margin*2):t.scale},r.getImageWidth=function(n,t){const e=r.getScale(n,t);return Math.floor((n+t.margin*2)*e)},r.qrToImageData=function(n,t,e){const u=t.modules.size,a=t.modules.data,s=r.getScale(u,e),c=Math.floor((u+e.margin*2)*s),d=e.margin*s,B=[e.color.light,e.color.dark];for(let h=0;h<c;h++)for(let f=0;f<c;f++){let N=(h*c+f)*4,M=e.color.light;if(h>=d&&f>=d&&h<c-d&&f<c-d){const S=Math.floor((h-d)/s),R=Math.floor((f-d)/s);M=B[a[S*u+R]?1:0]}n[N++]=M.r,n[N++]=M.g,n[N++]=M.b,n[N]=M.a}}}(wt)),wt}var Qt;function Ie(){return Qt||(Qt=1,function(r){const o=te();function i(t,e,u){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=u,e.width=u,e.style.height=u+"px",e.style.width=u+"px"}function n(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}r.render=function(e,u,a){let s=a,c=u;typeof s>"u"&&(!u||!u.getContext)&&(s=u,u=void 0),u||(c=n()),s=o.getOptions(s);const d=o.getImageWidth(e.modules.size,s),B=c.getContext("2d"),h=B.createImageData(d,d);return o.qrToImageData(h.data,e,s),i(B,c,d),B.putImageData(h,0,0),c},r.renderToDataURL=function(e,u,a){let s=a;typeof s>"u"&&(!u||!u.getContext)&&(s=u,u=void 0),s||(s={});const c=r.render(e,u,s),d=s.type||"image/png",B=s.rendererOpts||{};return c.toDataURL(d,B.quality)}}(mt)),mt}var Ct={},$t;function Te(){if($t)return Ct;$t=1;const r=te();function o(t,e){const u=t.a/255,a=e+'="'+t.hex+'"';return u<1?a+" "+e+'-opacity="'+u.toFixed(2).slice(1)+'"':a}function i(t,e,u){let a=t+e;return typeof u<"u"&&(a+=" "+u),a}function n(t,e,u){let a="",s=0,c=!1,d=0;for(let B=0;B<t.length;B++){const h=Math.floor(B%e),f=Math.floor(B/e);!h&&!c&&(c=!0),t[B]?(d++,B>0&&h>0&&t[B-1]||(a+=c?i("M",h+u,.5+f+u):i("m",s,0),s=0,c=!1),h+1<e&&t[B+1]||(a+=i("h",d),d=0)):s++}return a}return Ct.render=function(e,u,a){const s=r.getOptions(u),c=e.modules.size,d=e.modules.data,B=c+s.margin*2,h=s.color.light.a?"<path "+o(s.color.light,"fill")+' d="M0 0h'+B+"v"+B+'H0z"/>':"",f="<path "+o(s.color.dark,"stroke")+' d="'+n(d,c,s.margin)+'"/>',N='viewBox="0 0 '+B+" "+B+'"',S='<svg xmlns="http://www.w3.org/2000/svg" '+(s.width?'width="'+s.width+'" height="'+s.width+'" ':"")+N+' shape-rendering="crispEdges">'+h+f+`</svg>
`;return typeof a=="function"&&a(null,S),S},Ct}var Wt;function Ne(){if(Wt)return z;Wt=1;const r=ie(),o=Re(),i=Ie(),n=Te();function t(e,u,a,s,c){const d=[].slice.call(arguments,1),B=d.length,h=typeof d[B-1]=="function";if(!h&&!r())throw new Error("Callback required as last argument");if(h){if(B<2)throw new Error("Too few arguments provided");B===2?(c=a,a=u,u=s=void 0):B===3&&(u.getContext&&typeof c>"u"?(c=s,s=void 0):(c=s,s=a,a=u,u=void 0))}else{if(B<1)throw new Error("Too few arguments provided");return B===1?(a=u,u=s=void 0):B===2&&!u.getContext&&(s=a,a=u,u=void 0),new Promise(function(f,N){try{const M=o.create(a,s);f(e(M,u,s))}catch(M){N(M)}})}try{const f=o.create(a,s);c(null,e(f,u,s))}catch(f){c(f)}}return z.create=o.create,z.toCanvas=t.bind(null,i.render),z.toDataURL=t.bind(null,i.renderToDataURL),z.toString=t.bind(null,function(e,u,a){return n.render(e,a)}),z}var Me=Ne();const Se=oe(Me);export{Se as Q};
