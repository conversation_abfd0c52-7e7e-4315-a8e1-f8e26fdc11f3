import{aL as yn,aM as bn,d as tt,aC as ge,a as $t,_ as St,G as Ne,b as B,o as $,a9 as He,T as ne,i as l,O as Rt,k as o,h as p,n as xe,Q as De,aN as wn,aO as Zl,aP as Ql,E as It,ad as yt,aQ as El,c as ae,R as Oe,j as c,H as I,U as al,aR as Xl,a5 as _e,aS as kn,aT as yl,aU as Ml,aV as Cn,aW as Tl,aX as Bt,aY as $n,aZ as Sn,a_ as tl,a$ as ll,an as bl,t as Le,s as We,b0 as Vn,b1 as sl,ab as ut,b2 as en,b3 as En,b4 as et,b5 as Zt,b6 as tn,b7 as Pn,b8 as it,b9 as Ln,ba as xn,bb as Dn,bc as Rn,ac as ln,bd as Pl,af as Ot,be as Nn,bf as In,bg as Mn,bh as Tn,bi as On,bj as An,bk as qn,bl as Fn,ap as nl,a6 as ce,a7 as ot,r as Kt,f as nn,N as Ht,bm as Un,e as V,B as zn,bn as rl,bo as Ct,bp as wl,bq as Bn,br as Hn,bs as Kn,bt as Yn,g as jn,bu as Wn,L as Yt,M as il,P as dl,av as on,J as je,S as jt,bv as Nt,ak as ee,bw as Ol,bx as Ll,by as Gn,C as ol,al as Jn,X as Qe,ai as Zn,q as Dt,au as an,at as sn,aa as Xe,bz as Qn,bA as vt,bB as Al,bC as ql,aI as Xn,bD as eo,bE as pl,am as ml,bF as Fl,bG as Gt,aJ as kl,K as Cl,bH as Ut,bI as to,bJ as $l,bK as Ul,a2 as rn,a3 as dn,bL as lo,bM as no,Z as Ze,bN as oo,bO as ao,bP as Sl,bQ as zl,bR as Bl,bS as so,bT as ro,bU as io,bV as uo,bW as co,m as xl,bX as po,bY as mo,bZ as fo,b_ as go,b$ as Hl,ae as ho,ar as _o,ah as vo,c0 as yo,c1 as bo,c2 as wo,c3 as ko,c4 as Co,c5 as $o,aj as So,c6 as Vo,F as Kl,c7 as fl,c8 as Eo,c9 as Po,ca as Lo,cb as xo,cc as zt,cd as Do,ce as gl,cf as Ro,cg as No,ch as Io,ci as Mo,cj as To,ck as Oo,cl as Ao,cm as qo,cn as Fo,co as Uo,cp as zo,cq as Bo,cr as Ho,cs as Ko}from"./index-CmETSh6Y.js";import{E as un,a as cn,o as Jt}from"./el-progress-C10IkdN4.js";import{b as Dl,E as Rl,a as pn}from"./el-radio-DoZFrPxL.js";import{E as mn,a as Yo}from"./el-config-provider-DRHX7QJ5.js";import{E as fn}from"./el-input-number-V2-U9i7h.js";import{u as Yl,c as jl,E as jo}from"./el-date-picker-W7Ity7Gv.js";import{s as Wo}from"./porpery-DmNwVFIQ.js";import{E as Mt}from"./el-space-mudxGYIP.js";import{E as Go,a as Jo}from"./el-tab-pane-CjzQDPCe.js";import{a as Zo,u as Qo}from"./current-Auvi1tS_.js";/* empty css                          */import{d as dt}from"./dayjs.min-Bj8ADfnT.js";import{E as Xo,a as ea}from"./el-table-column-XN33CJP9.js";import"./el-tooltip-l0sNRNKZ.js";import{u as hl}from"./autoApply-BvulaZhm.js";import{Q as _l}from"./browser-GT_2EIZx.js";var ta=1,la=4;function gn(n){return yn(n,ta|la)}var na=1/0;function oa(n){var u=n==null?0:n.length;return u?bn(n,na):[]}const aa=()=>Math.floor(Math.random()*1e4);var sa=tt({name:"NodeContent",setup(){return{ns:$t("cascader-node")}},render(){const{ns:n}=this,{node:u,panel:h}=this.$parent,{data:v,label:g}=u,{renderLabelFn:E}=h;return ge("span",{class:n.e("label")},E?E({node:u,data:v}):g)}});const Nl=Symbol(),ra=tt({name:"ElCascaderNode",components:{ElCheckbox:yt,ElRadio:Dl,NodeContent:sa,ElIcon:It,Check:Ql,Loading:Zl,ArrowRight:wn},props:{node:{type:Object,required:!0},menuId:String},emits:["expand"],setup(n,{emit:u}){const h=El(Nl),v=$t("cascader-node"),g=ae(()=>h.isHoverMenu),E=ae(()=>h.config.multiple),i=ae(()=>h.config.checkStrictly),A=ae(()=>{var Y;return(Y=h.checkedNodes[0])==null?void 0:Y.uid}),H=ae(()=>n.node.isDisabled),q=ae(()=>n.node.isLeaf),C=ae(()=>i.value&&!q.value||!H.value),_=ae(()=>X(h.expandingNode)),P=ae(()=>i.value&&h.checkedNodes.some(X)),X=Y=>{var ye;const{level:oe,uid:be}=n.node;return((ye=Y==null?void 0:Y.pathNodes[oe-1])==null?void 0:ye.uid)===be},G=()=>{_.value||h.expandNode(n.node)},J=Y=>{const{node:ye}=n;Y!==ye.checked&&h.handleCheckChange(ye,Y)},a=()=>{h.lazyLoad(n.node,()=>{q.value||G()})},ue=Y=>{g.value&&(O(),!q.value&&u("expand",Y))},O=()=>{const{node:Y}=n;!C.value||Y.loading||(Y.loaded?G():a())},N=()=>{g.value&&!q.value||(q.value&&!H.value&&!i.value&&!E.value?R(!0):O())},M=Y=>{i.value?(J(Y),n.node.loaded&&G()):R(Y)},R=Y=>{n.node.loaded?(J(Y),!i.value&&G()):a()};return{panel:h,isHoverMenu:g,multiple:E,checkStrictly:i,checkedNodeId:A,isDisabled:H,isLeaf:q,expandable:C,inExpandingPath:_,inCheckedPath:P,ns:v,handleHoverExpand:ue,handleExpand:O,handleClick:N,handleCheck:R,handleSelectCheck:M}}}),ia=["id","aria-haspopup","aria-owns","aria-expanded","tabindex"],da=p("span",null,null,-1);function ua(n,u,h,v,g,E){const i=Ne("el-checkbox"),A=Ne("el-radio"),H=Ne("check"),q=Ne("el-icon"),C=Ne("node-content"),_=Ne("loading"),P=Ne("arrow-right");return $(),B("li",{id:`${n.menuId}-${n.node.uid}`,role:"menuitem","aria-haspopup":!n.isLeaf,"aria-owns":n.isLeaf?null:n.menuId,"aria-expanded":n.inExpandingPath,tabindex:n.expandable?-1:void 0,class:xe([n.ns.b(),n.ns.is("selectable",n.checkStrictly),n.ns.is("active",n.node.checked),n.ns.is("disabled",!n.expandable),n.inExpandingPath&&"in-active-path",n.inCheckedPath&&"in-checked-path"]),onMouseenter:u[2]||(u[2]=(...X)=>n.handleHoverExpand&&n.handleHoverExpand(...X)),onFocus:u[3]||(u[3]=(...X)=>n.handleHoverExpand&&n.handleHoverExpand(...X)),onClick:u[4]||(u[4]=(...X)=>n.handleClick&&n.handleClick(...X))},[He(" prefix "),n.multiple?($(),ne(i,{key:0,"model-value":n.node.checked,indeterminate:n.node.indeterminate,disabled:n.isDisabled,onClick:u[0]||(u[0]=Rt(()=>{},["stop"])),"onUpdate:modelValue":n.handleSelectCheck},null,8,["model-value","indeterminate","disabled","onUpdate:modelValue"])):n.checkStrictly?($(),ne(A,{key:1,"model-value":n.checkedNodeId,label:n.node.uid,disabled:n.isDisabled,"onUpdate:modelValue":n.handleSelectCheck,onClick:u[1]||(u[1]=Rt(()=>{},["stop"]))},{default:o(()=>[He(`
        Add an empty element to avoid render label,
        do not use empty fragment here for https://github.com/vuejs/vue-next/pull/2485
      `),da]),_:1},8,["model-value","label","disabled","onUpdate:modelValue"])):n.isLeaf&&n.node.checked?($(),ne(q,{key:2,class:xe(n.ns.e("prefix"))},{default:o(()=>[l(H)]),_:1},8,["class"])):He("v-if",!0),He(" content "),l(C),He(" postfix "),n.isLeaf?He("v-if",!0):($(),B(De,{key:3},[n.node.loading?($(),ne(q,{key:0,class:xe([n.ns.is("loading"),n.ns.e("postfix")])},{default:o(()=>[l(_)]),_:1},8,["class"])):($(),ne(q,{key:1,class:xe(["arrow-right",n.ns.e("postfix")])},{default:o(()=>[l(P)]),_:1},8,["class"]))],64))],42,ia)}var ca=St(ra,[["render",ua],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/cascader-panel/src/node.vue"]]);const pa=tt({name:"ElCascaderMenu",components:{Loading:Zl,ElIcon:It,ElScrollbar:al,ElCascaderNode:ca},props:{nodes:{type:Array,required:!0},index:{type:Number,required:!0}},setup(n){const u=kn(),h=$t("cascader-menu"),{t:v}=Xl(),g=aa();let E=null,i=null;const A=El(Nl),H=_e(null),q=ae(()=>!n.nodes.length),C=ae(()=>!A.initialLoaded),_=ae(()=>`cascader-menu-${g}-${n.index}`),P=a=>{E=a.target},X=a=>{if(!(!A.isHoverMenu||!E||!H.value))if(E.contains(a.target)){G();const ue=u.vnode.el,{left:O}=ue.getBoundingClientRect(),{offsetWidth:N,offsetHeight:M}=ue,R=a.clientX-O,Y=E.offsetTop,ye=Y+E.offsetHeight;H.value.innerHTML=`
          <path style="pointer-events: auto;" fill="transparent" d="M${R} ${Y} L${N} 0 V${Y} Z" />
          <path style="pointer-events: auto;" fill="transparent" d="M${R} ${ye} L${N} ${M} V${ye} Z" />
        `}else i||(i=window.setTimeout(J,A.config.hoverThreshold))},G=()=>{i&&(clearTimeout(i),i=null)},J=()=>{H.value&&(H.value.innerHTML="",G())};return{ns:h,panel:A,hoverZone:H,isEmpty:q,isLoading:C,menuId:_,t:v,handleExpand:P,handleMouseMove:X,clearHoverZone:J}}});function ma(n,u,h,v,g,E){const i=Ne("el-cascader-node"),A=Ne("loading"),H=Ne("el-icon"),q=Ne("el-scrollbar");return $(),ne(q,{key:n.menuId,tag:"ul",role:"menu",class:xe(n.ns.b()),"wrap-class":n.ns.e("wrap"),"view-class":[n.ns.e("list"),n.ns.is("empty",n.isEmpty)],onMousemove:n.handleMouseMove,onMouseleave:n.clearHoverZone},{default:o(()=>{var C;return[($(!0),B(De,null,Oe(n.nodes,_=>($(),ne(i,{key:_.uid,node:_,"menu-id":n.menuId,onExpand:n.handleExpand},null,8,["node","menu-id","onExpand"]))),128)),n.isLoading?($(),B("div",{key:0,class:xe(n.ns.e("empty-text"))},[l(H,{size:"14",class:xe(n.ns.is("loading"))},{default:o(()=>[l(A)]),_:1},8,["class"]),c(" "+I(n.t("el.cascader.loading")),1)],2)):n.isEmpty?($(),B("div",{key:1,class:xe(n.ns.e("empty-text"))},I(n.t("el.cascader.noData")),3)):(C=n.panel)!=null&&C.isHoverMenu?($(),B("svg",{key:2,ref:"hoverZone",class:xe(n.ns.e("hover-zone"))},null,2)):He("v-if",!0)]}),_:1},8,["class","wrap-class","view-class","onMousemove","onMouseleave"])}var fa=St(pa,[["render",ma],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/cascader-panel/src/menu.vue"]]);let ga=0;const ha=n=>{const u=[n];let{parent:h}=n;for(;h;)u.unshift(h),h=h.parent;return u};class Tt{constructor(u,h,v,g=!1){this.data=u,this.config=h,this.parent=v,this.root=g,this.uid=ga++,this.checked=!1,this.indeterminate=!1,this.loading=!1;const{value:E,label:i,children:A}=h,H=u[A],q=ha(this);this.level=g?0:v?v.level+1:1,this.value=u[E],this.label=u[i],this.pathNodes=q,this.pathValues=q.map(C=>C.value),this.pathLabels=q.map(C=>C.label),this.childrenData=H,this.children=(H||[]).map(C=>new Tt(C,h,this)),this.loaded=!h.lazy||this.isLeaf||!yl(H)}get isDisabled(){const{data:u,parent:h,config:v}=this,{disabled:g,checkStrictly:E}=v;return(Ml(g)?g(u,this):!!u[g])||!E&&(h==null?void 0:h.isDisabled)}get isLeaf(){const{data:u,config:h,childrenData:v,loaded:g}=this,{lazy:E,leaf:i}=h,A=Ml(i)?i(u,this):u[i];return Cn(A)?E&&!g?!1:!(Array.isArray(v)&&v.length):!!A}get valueByOption(){return this.config.emitPath?this.pathValues:this.value}appendChild(u){const{childrenData:h,children:v}=this,g=new Tt(u,this.config,this);return Array.isArray(h)?h.push(u):this.childrenData=[u],v.push(g),g}calcText(u,h){const v=u?this.pathLabels.join(h):this.label;return this.text=v,v}broadcast(u,...h){const v=`onParent${Tl(u)}`;this.children.forEach(g=>{g&&(g.broadcast(u,...h),g[v]&&g[v](...h))})}emit(u,...h){const{parent:v}=this,g=`onChild${Tl(u)}`;v&&(v[g]&&v[g](...h),v.emit(u,...h))}onParentCheck(u){this.isDisabled||this.setCheckState(u)}onChildCheck(){const{children:u}=this,h=u.filter(g=>!g.isDisabled),v=h.length?h.every(g=>g.checked):!1;this.setCheckState(v)}setCheckState(u){const h=this.children.length,v=this.children.reduce((g,E)=>{const i=E.checked?1:E.indeterminate?.5:0;return g+i},0);this.checked=this.loaded&&this.children.filter(g=>!g.isDisabled).every(g=>g.loaded&&g.checked)&&u,this.indeterminate=this.loaded&&v!==h&&v>0}doCheck(u){if(this.checked===u)return;const{checkStrictly:h,multiple:v}=this.config;h||!v?this.checked=u:(this.broadcast("check",u),this.setCheckState(u),this.emit("check"))}}const Vl=(n,u)=>n.reduce((h,v)=>(v.isLeaf?h.push(v):(!u&&h.push(v),h=h.concat(Vl(v.children,u))),h),[]);class Wl{constructor(u,h){this.config=h;const v=(u||[]).map(g=>new Tt(g,this.config));this.nodes=v,this.allNodes=Vl(v,!1),this.leafNodes=Vl(v,!0)}getNodes(){return this.nodes}getFlattedNodes(u){return u?this.leafNodes:this.allNodes}appendNode(u,h){const v=h?h.appendChild(u):new Tt(u,this.config);h||this.nodes.push(v),this.allNodes.push(v),v.isLeaf&&this.leafNodes.push(v)}appendNodes(u,h){u.forEach(v=>this.appendNode(v,h))}getNodeByValue(u,h=!1){return!u&&u!==0?null:this.getFlattedNodes(h).find(g=>Bt(g.value,u)||Bt(g.pathValues,u))||null}getSameNode(u){return u&&this.getFlattedNodes(!1).find(({value:v,level:g})=>Bt(u.value,v)&&u.level===g)||null}}const hn={modelValue:[Number,String,Array],options:{type:Array,default:()=>[]},props:{type:Object,default:()=>({})}},_a={expandTrigger:"click",multiple:!1,checkStrictly:!1,emitPath:!0,lazy:!1,lazyLoad:$n,value:"value",label:"label",children:"children",leaf:"leaf",disabled:"disabled",hoverThreshold:500},va=n=>ae(()=>({..._a,...n.props})),Gl=n=>{if(!n)return 0;const u=n.id.split("-");return Number(u[u.length-2])},ya=n=>{if(!n)return;const u=n.querySelector("input");u?u.click():Sn(n)&&n.click()},ba=(n,u)=>{const h=u.slice(0),v=h.map(E=>E.uid),g=n.reduce((E,i)=>{const A=v.indexOf(i.uid);return A>-1&&(E.push(i),h.splice(A,1),v.splice(A,1)),E},[]);return g.push(...h),g},wa=tt({name:"ElCascaderPanel",components:{ElCascaderMenu:fa},props:{...hn,border:{type:Boolean,default:!0},renderLabel:Function},emits:[tl,ll,"close","expand-change"],setup(n,{emit:u,slots:h}){let v=!1;const g=$t("cascader"),E=va(n);let i=null;const A=_e(!0),H=_e([]),q=_e(null),C=_e([]),_=_e(null),P=_e([]),X=ae(()=>E.value.expandTrigger==="hover"),G=ae(()=>n.renderLabel||h.default),J=()=>{const{options:T}=n,Z=E.value;v=!1,i=new Wl(T,Z),C.value=[i.getNodes()],Z.lazy&&yl(n.options)?(A.value=!1,a(void 0,he=>{he&&(i=new Wl(he,Z),C.value=[i.getNodes()]),A.value=!0,oe(!1,!0)})):oe(!1,!0)},a=(T,Z)=>{const he=E.value;T=T||new Tt({},he,void 0,!0),T.loading=!0;const se=$e=>{const Se=T,K=Se.root?null:Se;$e&&(i==null||i.appendNodes($e,K)),Se.loading=!1,Se.loaded=!0,Se.childrenData=Se.childrenData||[],Z&&Z($e)};he.lazyLoad(T,se)},ue=(T,Z)=>{var he;const{level:se}=T,$e=C.value.slice(0,se);let Se;T.isLeaf?Se=T.pathNodes[se-2]:(Se=T,$e.push(T.children)),((he=_.value)==null?void 0:he.uid)!==(Se==null?void 0:Se.uid)&&(_.value=T,C.value=$e,!Z&&u("expand-change",(T==null?void 0:T.pathValues)||[]))},O=(T,Z,he=!0)=>{const{checkStrictly:se,multiple:$e}=E.value,Se=P.value[0];v=!0,!$e&&(Se==null||Se.doCheck(!1)),T.doCheck(Z),ye(),he&&!$e&&!se&&u("close"),!he&&!$e&&!se&&N(T)},N=T=>{T&&(T=T.parent,N(T),T&&ue(T))},M=T=>i==null?void 0:i.getFlattedNodes(T),R=T=>{var Z;return(Z=M(T))==null?void 0:Z.filter(he=>he.checked!==!1)},Y=()=>{P.value.forEach(T=>T.doCheck(!1)),ye()},ye=()=>{var T;const{checkStrictly:Z,multiple:he}=E.value,se=P.value,$e=R(!Z),Se=ba(se,$e),K=Se.map(Me=>Me.valueByOption);P.value=Se,q.value=he?K:(T=K[0])!=null?T:null},oe=(T=!1,Z=!1)=>{const{modelValue:he}=n,{lazy:se,multiple:$e,checkStrictly:Se}=E.value,K=!Se;if(!(!A.value||v||!Z&&Bt(he,q.value)))if(se&&!T){const Ke=Yl(oa(jl(he))).map(qe=>i==null?void 0:i.getNodeByValue(qe)).filter(qe=>!!qe&&!qe.loaded&&!qe.loading);Ke.length?Ke.forEach(qe=>{a(qe,()=>oe(!1,Z))}):oe(!0,Z)}else{const Me=$e?jl(he):[he],Ke=Yl(Me.map(qe=>i==null?void 0:i.getNodeByValue(qe,K)));be(Ke,Z),q.value=gn(he)}},be=(T,Z=!0)=>{const{checkStrictly:he}=E.value,se=P.value,$e=T.filter(Me=>!!Me&&(he||Me.isLeaf)),Se=i==null?void 0:i.getSameNode(_.value),K=Z&&Se||$e[0];K?K.pathNodes.forEach(Me=>ue(Me,!0)):_.value=null,se.forEach(Me=>Me.doCheck(!1)),$e.forEach(Me=>Me.doCheck(!0)),P.value=$e,ut(de)},de=()=>{en&&H.value.forEach(T=>{const Z=T==null?void 0:T.$el;if(Z){const he=Z.querySelector(`.${g.namespace.value}-scrollbar__wrap`),se=Z.querySelector(`.${g.b("node")}.${g.is("active")}`)||Z.querySelector(`.${g.b("node")}.in-active-path`);En(he,se)}})},lt=T=>{const Z=T.target,{code:he}=T;switch(he){case et.up:case et.down:{T.preventDefault();const se=he===et.up?-1:1;Zt(tn(Z,se,`.${g.b("node")}[tabindex="-1"]`));break}case et.left:{T.preventDefault();const se=H.value[Gl(Z)-1],$e=se==null?void 0:se.$el.querySelector(`.${g.b("node")}[aria-expanded="true"]`);Zt($e);break}case et.right:{T.preventDefault();const se=H.value[Gl(Z)+1],$e=se==null?void 0:se.$el.querySelector(`.${g.b("node")}[tabindex="-1"]`);Zt($e);break}case et.enter:ya(Z);break}};return bl(Nl,Le({config:E,expandingNode:_,checkedNodes:P,isHoverMenu:X,initialLoaded:A,renderLabelFn:G,lazyLoad:a,expandNode:ue,handleCheckChange:O})),We([E,()=>n.options],J,{deep:!0,immediate:!0}),We(()=>n.modelValue,()=>{v=!1,oe()},{deep:!0}),We(()=>q.value,T=>{Bt(T,n.modelValue)||(u(tl,T),u(ll,T))}),Vn(()=>H.value=[]),sl(()=>!yl(n.modelValue)&&oe()),{ns:g,menuList:H,menus:C,checkedNodes:P,handleKeyDown:lt,handleCheckChange:O,getFlattedNodes:M,getCheckedNodes:R,clearCheckedNodes:Y,calculateCheckedValue:ye,scrollToExpandingNode:de}}});function ka(n,u,h,v,g,E){const i=Ne("el-cascader-menu");return $(),B("div",{class:xe([n.ns.b("panel"),n.ns.is("bordered",n.border)]),onKeydown:u[0]||(u[0]=(...A)=>n.handleKeyDown&&n.handleKeyDown(...A))},[($(!0),B(De,null,Oe(n.menus,(A,H)=>($(),ne(i,{key:H,ref_for:!0,ref:q=>n.menuList[H]=q,index:H,nodes:[...A]},null,8,["index","nodes"]))),128))],34)}var Qt=St(wa,[["render",ka],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/cascader-panel/src/index.vue"]]);Qt.install=n=>{n.component(Qt.name,Qt)};const Ca=Qt,$a={modifiers:[{name:"arrowPosition",enabled:!0,phase:"main",fn:({state:n})=>{const{modifiersData:u,placement:h}=n;["right","left","bottom","top"].includes(h)||(u.arrow.x=35)},requires:["arrow"]}]},Sa="ElCascader",Va=tt({name:Sa,components:{ElCascaderPanel:Ca,ElInput:Ot,ElTooltip:Pl,ElScrollbar:al,ElTag:ln,ElIcon:It,CircleClose:Rn,Check:Ql,ArrowDown:Dn},directives:{Clickoutside:xn},props:{...hn,size:{type:String,validator:Ln},placeholder:{type:String},disabled:Boolean,clearable:Boolean,filterable:Boolean,filterMethod:{type:Function,default:(n,u)=>n.text.includes(u)},separator:{type:String,default:" / "},showAllLevels:{type:Boolean,default:!0},collapseTags:Boolean,collapseTagsTooltip:{type:Boolean,default:!1},debounce:{type:Number,default:300},beforeFilter:{type:Function,default:()=>!0},popperClass:{type:String,default:""},teleported:it.teleported,tagType:{...Pn.type,default:"info"},validateEvent:{type:Boolean,default:!0}},emits:[tl,ll,"focus","blur","visible-change","expand-change","remove-tag"],setup(n,{emit:u}){let h=0,v=0;const g=$t("cascader"),E=$t("input"),{t:i}=Xl(),{form:A,formItem:H}=Nn(),q=_e(null),C=_e(null),_=_e(null),P=_e(null),X=_e(null),G=_e(!1),J=_e(!1),a=_e(!1),ue=_e(""),O=_e(""),N=_e([]),M=_e([]),R=_e([]),Y=_e(!1),ye=ae(()=>n.disabled||(A==null?void 0:A.disabled)),oe=ae(()=>n.placeholder||i("el.cascader.placeholder")),be=ae(()=>O.value||N.value.length>0?"":oe.value),de=In(),lt=ae(()=>["small"].includes(de.value)?"small":"default"),T=ae(()=>!!n.props.multiple),Z=ae(()=>!n.filterable||T.value),he=ae(()=>T.value?O.value:ue.value),se=ae(()=>{var k;return((k=P.value)==null?void 0:k.checkedNodes)||[]}),$e=ae(()=>!n.clearable||ye.value||a.value||!J.value?!1:!!se.value.length),Se=ae(()=>{const{showAllLevels:k,separator:S}=n,j=se.value;return j.length?T.value?"":j[0].calcText(k,S):""}),K=ae({get(){return gn(n.modelValue)},set(k){u(tl,k),u(ll,k),n.validateEvent&&(H==null||H.validate("change").catch(S=>Mn()))}}),Me=ae(()=>{var k,S;return(S=(k=q.value)==null?void 0:k.popperRef)==null?void 0:S.contentRef}),Ke=k=>{var S,j,z;ye.value||(k=k??!G.value,k!==G.value&&(G.value=k,(j=(S=C.value)==null?void 0:S.input)==null||j.setAttribute("aria-expanded",`${k}`),k?(qe(),ut((z=P.value)==null?void 0:z.scrollToExpandingNode)):n.filterable&&d(),u("visible-change",k)))},qe=()=>{ut(()=>{var k;(k=q.value)==null||k.updatePopper()})},bt=()=>{a.value=!1},mt=k=>{const{showAllLevels:S,separator:j}=n;return{node:k,key:k.uid,text:k.calcText(S,j),hitState:!1,closable:!ye.value&&!k.isDisabled,isCollapseTag:!1}},re=k=>{var S;const j=k.node;j.doCheck(!1),(S=P.value)==null||S.calculateCheckedValue(),u("remove-tag",j.valueByOption)},te=()=>{if(!T.value)return;const k=se.value,S=[],j=[];if(k.forEach(z=>j.push(mt(z))),M.value=j,k.length){const[z,...ve]=k,Ve=ve.length;S.push(mt(z)),Ve&&(n.collapseTags?S.push({key:-1,text:`+ ${Ve}`,closable:!1,isCollapseTag:!0}):ve.forEach(Ee=>S.push(mt(Ee))))}N.value=S},Ue=()=>{var k,S;const{filterMethod:j,showAllLevels:z,separator:ve}=n,Ve=(S=(k=P.value)==null?void 0:k.getFlattedNodes(!n.props.checkStrictly))==null?void 0:S.filter(Ee=>Ee.isDisabled?!1:(Ee.calcText(z,ve),j(Ee,he.value)));T.value&&(N.value.forEach(Ee=>{Ee.hitState=!1}),M.value.forEach(Ee=>{Ee.hitState=!1})),a.value=!0,R.value=Ve,qe()},_t=()=>{var k;let S;a.value&&X.value?S=X.value.$el.querySelector(`.${g.e("suggestion-item")}`):S=(k=P.value)==null?void 0:k.$el.querySelector(`.${g.b("node")}[tabindex="-1"]`),S&&(S.focus(),!a.value&&S.click())},Vt=()=>{var k,S;const j=(k=C.value)==null?void 0:k.input,z=_.value,ve=(S=X.value)==null?void 0:S.$el;if(!(!en||!j)){if(ve){const Ve=ve.querySelector(`.${g.e("suggestion-list")}`);Ve.style.minWidth=`${j.offsetWidth}px`}if(z){const{offsetHeight:Ve}=z,Ee=N.value.length>0?`${Math.max(Ve+6,h)}px`:`${h}px`;j.style.height=Ee,qe()}}},Et=k=>{var S;return(S=P.value)==null?void 0:S.getCheckedNodes(k)},wt=k=>{qe(),u("expand-change",k)},qt=k=>{var S;const j=(S=k.target)==null?void 0:S.value;if(k.type==="compositionend")Y.value=!1,ut(()=>we(j));else{const z=j[j.length-1]||"";Y.value=!Fn(z)}},Ft=k=>{if(!Y.value)switch(k.code){case et.enter:Ke();break;case et.down:Ke(!0),ut(_t),k.preventDefault();break;case et.esc:G.value===!0&&(k.preventDefault(),k.stopPropagation(),Ke(!1));break;case et.tab:Ke(!1);break}},b=()=>{var k;(k=P.value)==null||k.clearCheckedNodes(),!G.value&&n.filterable&&d(),Ke(!1)},d=()=>{const{value:k}=Se;ue.value=k,O.value=k},L=k=>{var S,j;const{checked:z}=k;T.value?(S=P.value)==null||S.handleCheckChange(k,!z,!1):(!z&&((j=P.value)==null||j.handleCheckChange(k,!0,!1)),Ke(!1))},F=k=>{const S=k.target,{code:j}=k;switch(j){case et.up:case et.down:{const z=j===et.up?-1:1;Zt(tn(S,z,`.${g.e("suggestion-item")}[tabindex="-1"]`));break}case et.enter:S.click();break}},Q=()=>{const k=N.value,S=k[k.length-1];v=O.value?0:v+1,!(!S||!v||n.collapseTags&&k.length>1)&&(S.hitState?re(S):S.hitState=!0)},le=Tn(()=>{const{value:k}=he;if(!k)return;const S=n.beforeFilter(k);On(S)?S.then(Ue).catch(()=>{}):S!==!1?Ue():bt()},n.debounce),we=(k,S)=>{!G.value&&Ke(!0),!(S!=null&&S.isComposing)&&(k?le():bt())};return We(a,qe),We([se,ye],te),We(N,()=>{ut(()=>Vt())}),We(Se,d,{immediate:!0}),sl(()=>{const k=C.value.input,S=Number.parseFloat(An(E.cssVarName("input-height"),k).value)-2;h=k.offsetHeight||S,qn(k,Vt)}),{popperOptions:$a,tooltipRef:q,popperPaneRef:Me,input:C,tagWrapper:_,panel:P,suggestionPanel:X,popperVisible:G,inputHover:J,inputPlaceholder:oe,currentPlaceholder:be,filtering:a,presentText:Se,checkedValue:K,inputValue:ue,searchInputValue:O,presentTags:N,allPresentTags:M,suggestions:R,isDisabled:ye,isOnComposition:Y,realSize:de,tagSize:lt,multiple:T,readonly:Z,clearBtnVisible:$e,nsCascader:g,nsInput:E,t:i,togglePopperVisible:Ke,hideSuggestionPanel:bt,deleteTag:re,focusFirstNode:_t,getCheckedNodes:Et,handleExpandChange:wt,handleKeyDown:Ft,handleComposition:qt,handleClear:b,handleSuggestionClick:L,handleSuggestionKeyDown:F,handleDelete:Q,handleInput:we}}}),Ea={key:0},Pa=["placeholder"],La=["onClick"];function xa(n,u,h,v,g,E){const i=Ne("circle-close"),A=Ne("el-icon"),H=Ne("arrow-down"),q=Ne("el-input"),C=Ne("el-tag"),_=Ne("el-tooltip"),P=Ne("el-cascader-panel"),X=Ne("check"),G=Ne("el-scrollbar"),J=nl("clickoutside");return $(),ne(_,{ref:"tooltipRef",visible:n.popperVisible,teleported:n.teleported,"popper-class":[n.nsCascader.e("dropdown"),n.popperClass],"popper-options":n.popperOptions,"fallback-placements":["bottom-start","bottom","top-start","top","right","left"],"stop-popper-mouse-event":!1,"gpu-acceleration":!1,placement:"bottom-start",transition:`${n.nsCascader.namespace.value}-zoom-in-top`,effect:"light",pure:"",persistent:"",onHide:n.hideSuggestionPanel},{default:o(()=>[ce(($(),B("div",{class:xe([n.nsCascader.b(),n.nsCascader.m(n.realSize),n.nsCascader.is("disabled",n.isDisabled),n.$attrs.class]),style:nn(n.$attrs.style),onClick:u[11]||(u[11]=()=>n.togglePopperVisible(n.readonly?void 0:!0)),onKeydown:u[12]||(u[12]=(...a)=>n.handleKeyDown&&n.handleKeyDown(...a)),onMouseenter:u[13]||(u[13]=a=>n.inputHover=!0),onMouseleave:u[14]||(u[14]=a=>n.inputHover=!1)},[l(q,{ref:"input",modelValue:n.inputValue,"onUpdate:modelValue":u[1]||(u[1]=a=>n.inputValue=a),placeholder:n.currentPlaceholder,readonly:n.readonly,disabled:n.isDisabled,"validate-event":!1,size:n.realSize,class:xe(n.nsCascader.is("focus",n.popperVisible)),onCompositionstart:n.handleComposition,onCompositionupdate:n.handleComposition,onCompositionend:n.handleComposition,onFocus:u[2]||(u[2]=a=>n.$emit("focus",a)),onBlur:u[3]||(u[3]=a=>n.$emit("blur",a)),onInput:n.handleInput},{suffix:o(()=>[n.clearBtnVisible?($(),ne(A,{key:"clear",class:xe([n.nsInput.e("icon"),"icon-circle-close"]),onClick:Rt(n.handleClear,["stop"])},{default:o(()=>[l(i)]),_:1},8,["class","onClick"])):($(),ne(A,{key:"arrow-down",class:xe([n.nsInput.e("icon"),"icon-arrow-down",n.nsCascader.is("reverse",n.popperVisible)]),onClick:u[0]||(u[0]=Rt(a=>n.togglePopperVisible(),["stop"]))},{default:o(()=>[l(H)]),_:1},8,["class"]))]),_:1},8,["modelValue","placeholder","readonly","disabled","size","class","onCompositionstart","onCompositionupdate","onCompositionend","onInput"]),n.multiple?($(),B("div",{key:0,ref:"tagWrapper",class:xe(n.nsCascader.e("tags"))},[($(!0),B(De,null,Oe(n.presentTags,a=>($(),ne(C,{key:a.key,type:n.tagType,size:n.tagSize,hit:a.hitState,closable:a.closable,"disable-transitions":"",onClose:ue=>n.deleteTag(a)},{default:o(()=>[a.isCollapseTag===!1?($(),B("span",Ea,I(a.text),1)):($(),ne(_,{key:1,disabled:n.popperVisible||!n.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],placement:"bottom",effect:"light"},{default:o(()=>[p("span",null,I(a.text),1)]),content:o(()=>[p("div",{class:xe(n.nsCascader.e("collapse-tags"))},[($(!0),B(De,null,Oe(n.allPresentTags.slice(1),(ue,O)=>($(),B("div",{key:O,class:xe(n.nsCascader.e("collapse-tag"))},[($(),ne(C,{key:ue.key,class:"in-tooltip",type:n.tagType,size:n.tagSize,hit:ue.hitState,closable:ue.closable,"disable-transitions":"",onClose:N=>n.deleteTag(ue)},{default:o(()=>[p("span",null,I(ue.text),1)]),_:2},1032,["type","size","hit","closable","onClose"]))],2))),128))],2)]),_:2},1032,["disabled"]))]),_:2},1032,["type","size","hit","closable","onClose"]))),128)),n.filterable&&!n.isDisabled?ce(($(),B("input",{key:0,"onUpdate:modelValue":u[4]||(u[4]=a=>n.searchInputValue=a),type:"text",class:xe(n.nsCascader.e("search-input")),placeholder:n.presentText?"":n.inputPlaceholder,onInput:u[5]||(u[5]=a=>n.handleInput(n.searchInputValue,a)),onClick:u[6]||(u[6]=Rt(a=>n.togglePopperVisible(!0),["stop"])),onKeydown:u[7]||(u[7]=Ht((...a)=>n.handleDelete&&n.handleDelete(...a),["delete"])),onCompositionstart:u[8]||(u[8]=(...a)=>n.handleComposition&&n.handleComposition(...a)),onCompositionupdate:u[9]||(u[9]=(...a)=>n.handleComposition&&n.handleComposition(...a)),onCompositionend:u[10]||(u[10]=(...a)=>n.handleComposition&&n.handleComposition(...a))},null,42,Pa)),[[Un,n.searchInputValue]]):He("v-if",!0)],2)):He("v-if",!0)],38)),[[J,()=>n.togglePopperVisible(!1),n.popperPaneRef]])]),content:o(()=>[ce(l(P,{ref:"panel",modelValue:n.checkedValue,"onUpdate:modelValue":u[15]||(u[15]=a=>n.checkedValue=a),options:n.options,props:n.props,border:!1,"render-label":n.$slots.default,onExpandChange:n.handleExpandChange,onClose:u[16]||(u[16]=a=>n.$nextTick(()=>n.togglePopperVisible(!1)))},null,8,["modelValue","options","props","render-label","onExpandChange"]),[[ot,!n.filtering]]),n.filterable?ce(($(),ne(G,{key:0,ref:"suggestionPanel",tag:"ul",class:xe(n.nsCascader.e("suggestion-panel")),"view-class":n.nsCascader.e("suggestion-list"),onKeydown:n.handleSuggestionKeyDown},{default:o(()=>[n.suggestions.length?($(!0),B(De,{key:0},Oe(n.suggestions,a=>($(),B("li",{key:a.uid,class:xe([n.nsCascader.e("suggestion-item"),n.nsCascader.is("checked",a.checked)]),tabindex:-1,onClick:ue=>n.handleSuggestionClick(a)},[p("span",null,I(a.text),1),a.checked?($(),ne(A,{key:0},{default:o(()=>[l(X)]),_:1})):He("v-if",!0)],10,La))),128)):Kt(n.$slots,"empty",{key:1},()=>[p("li",{class:xe(n.nsCascader.e("empty-text"))},I(n.t("el.cascader.noMatch")),3)])]),_:3},8,["class","view-class","onKeydown"])),[[ot,n.filtering]]):He("v-if",!0)]),_:3},8,["visible","teleported","popper-class","popper-options","transition","onHide"])}var Xt=St(Va,[["render",xa],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/cascader/src/index.vue"]]);Xt.install=n=>{n.component(Xt.name,Xt)};const Da=Xt,Ra=Da,Na=tt({inheritAttrs:!1});function Ia(n,u,h,v,g,E){return Kt(n.$slots,"default")}var Ma=St(Na,[["render",Ia],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection.vue"]]);const Ta=tt({name:"ElCollectionItem",inheritAttrs:!1});function Oa(n,u,h,v,g,E){return Kt(n.$slots,"default")}var Aa=St(Ta,[["render",Oa],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection-item.vue"]]);const qa="data-el-collection-item",Fa=n=>{const u=`El${n}Collection`,h=`${u}Item`,v=Symbol(u),g=Symbol(h),E={...Ma,name:u,setup(){const A=_e(null),H=new Map;bl(v,{itemMap:H,getItems:()=>{const C=V(A);if(!C)return[];const _=Array.from(C.querySelectorAll(`[${qa}]`));return[...H.values()].sort((X,G)=>_.indexOf(X.ref)-_.indexOf(G.ref))},collectionRef:A})}},i={...Aa,name:h,setup(A,{attrs:H}){const q=_e(null),C=El(v,void 0);bl(g,{collectionItemRef:q}),sl(()=>{const _=V(q);_&&C.itemMap.set(_,{ref:_,...H})}),zn(()=>{const _=V(q);C.itemMap.delete(_)})}};return{COLLECTION_INJECTION_KEY:v,COLLECTION_ITEM_INJECTION_KEY:g,ElCollection:E,ElCollectionItem:i}},vl=rl({trigger:wl.trigger,effect:{...it.effect,default:"light"},type:{type:Ct(String)},placement:{type:Ct(String),default:"bottom"},popperOptions:{type:Ct(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:Ct([Number,String]),default:0},maxHeight:{type:Ct([Number,String]),default:""},popperClass:{type:String,default:""},disabled:{type:Boolean,default:!1},role:{type:String,default:"menu"},buttonProps:{type:Ct(Object)},teleported:it.teleported});rl({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:Bn}});rl({onKeydown:{type:Ct(Function)}});Fa("Dropdown");const Ua=rl({trigger:wl.trigger,placement:vl.placement,disabled:wl.disabled,visible:it.visible,transition:it.transition,popperOptions:vl.popperOptions,tabindex:vl.tabindex,content:it.content,popperStyle:it.popperStyle,popperClass:it.popperClass,enterable:{...it.enterable,default:!0},effect:{...it.effect,default:"light"},teleported:it.teleported,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),za={"update:visible":n=>Hn(n),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},Ba="onUpdate:visible",Ha=tt({name:"ElPopover"}),Ka=tt({...Ha,props:Ua,emits:za,setup(n,{expose:u,emit:h}){const v=n,g=ae(()=>v[Ba]),E=$t("popover"),i=_e(),A=ae(()=>{var a;return(a=V(i))==null?void 0:a.popperRef}),H=ae(()=>[{width:Yn(v.width)},v.popperStyle]),q=ae(()=>[E.b(),v.popperClass,{[E.m("plain")]:!!v.content}]),C=ae(()=>v.transition===`${E.namespace.value}-fade-in-linear`),_=()=>{var a;(a=i.value)==null||a.hide()},P=()=>{h("before-enter")},X=()=>{h("before-leave")},G=()=>{h("after-enter")},J=()=>{h("update:visible",!1),h("after-leave")};return u({popperRef:A,hide:_}),(a,ue)=>($(),ne(V(Pl),Kn({ref_key:"tooltipRef",ref:i},a.$attrs,{trigger:a.trigger,placement:a.placement,disabled:a.disabled,visible:a.visible,transition:a.transition,"popper-options":a.popperOptions,tabindex:a.tabindex,content:a.content,offset:a.offset,"show-after":a.showAfter,"hide-after":a.hideAfter,"auto-close":a.autoClose,"show-arrow":a.showArrow,"aria-label":a.title,effect:a.effect,enterable:a.enterable,"popper-class":V(q),"popper-style":V(H),teleported:a.teleported,persistent:a.persistent,"gpu-acceleration":V(C),"onUpdate:visible":V(g),onBeforeShow:P,onBeforeHide:X,onShow:G,onHide:J}),{content:o(()=>[a.title?($(),B("div",{key:0,class:xe(V(E).e("title")),role:"title"},I(a.title),3)):He("v-if",!0),Kt(a.$slots,"default",{},()=>[c(I(a.content),1)])]),default:o(()=>[a.$slots.reference?Kt(a.$slots,"reference",{key:0}):He("v-if",!0)]),_:3},16,["trigger","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","persistent","gpu-acceleration","onUpdate:visible"]))}});var Ya=St(Ka,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popover/src/popover.vue"]]);const Jl=(n,u)=>{const h=u.arg||u.value,v=h==null?void 0:h.popperRef;v&&(v.triggerRef=n)};var ja={mounted(n,u){Jl(n,u)},updated(n,u){Jl(n,u)}};const Wa="popover",Ga=Wn(ja,Wa),_n=jn(Ya,{directive:Ga});var el=(n=>(n.LEFT="left",n.RIGHT="right",n))(el||{});const Ja={class:"collect-comment"},Za=p("p",null,"1.该选项决定了采集的每一条评论的文件夹名字",-1),Qa=p("p",null,"2.【按评论ID】，则每一条评论的文件夹名字是该评论的ID",-1),Xa=p("p",null,"3.【按数字顺序排序】,则按照（1,2,3...）的顺序定义评论文件夹名字（同名文件会被覆盖）",-1),es=tt({__name:"collectComment",emits:["log","finally"],setup(n,{expose:u,emit:h}){const v=Le({dialog:!1,commentList:[]}),g=ae(()=>{const{path:C,fileName:_}=E;return C?C+(_?`/${_}`:""):""}),E=Le({path:"",fileName:"",collect_type:["txt","img","video"],folderType:"review-id"});function i(C){v.commentList=C,E.fileName=dt(Date.now()).format("YYYYMMDDHHmmss"),v.dialog=!0}async function A(){const C=await Nt();C&&(E.path=C)}const H=Le({down:!1});async function q(){const C=g.value;if(!C)return ee.warning({message:"请选择文件路径",grouping:!0});H.down=!0;const _=[],{folderType:P,collect_type:X}=E,G=v.commentList.map((R,Y)=>new Promise(async ye=>{const oe=`${P==="number"?Y+1:R.review_id}`,be=`${C}/${oe}`;if(X.includes("txt")){if(R.comment.includes("该用户觉得商品较好")||R.comment.includes("该用户觉得商品很好，给出了5星好评")||R.comment.includes("该用户未填写文字评价")||R.desc_score<5){ye(!0);return}_.push({type:"txt",dest:`${be}/comment.txt`,txt:R.comment,folderName:oe})}if(X.includes("img")&&await Promise.allSettled(R.pictures.map((de,lt)=>new Promise(async T=>{const Z=await Ol("extname",de.url);_.push({type:"img",url:de.url,dest:`${be}/img${lt+1}${Z}`,folderName:oe}),T(!0)}))),X.includes("video")&&R.video&&R.video.url){const de=R.video.url;await new Promise(async lt=>{const T=await Ol("extname",de);_.push({type:"video",url:de,dest:`${be}/video${T}`,folderName:oe}),lt(!0)})}ye(!0)}));await Promise.allSettled(G),v.dialog=!1;const J=_.length;let a=0;const ue=[..._],O=_.splice(0,20);await new Promise(R=>{O.forEach(Y=>{const ye=oe=>{if(!oe){R(!0);return}new Promise(be=>{switch(oe.type){case"img":case"video":{Gn({url:oe.url,dest:oe.dest,delay:oe.type==="video"?3e5:void 0}).then(de=>{a++,h("log",{msg:`${oe.dest}下载成功(${a}/${J})`,type:"success"})}).catch(de=>{a++,h("log",{msg:`${oe.dest}下载失败，${de.msg}(${a}/${J})`,type:"danger"})}).finally(()=>{be(!0)});break}case"txt":Ll({dest:oe.dest,data:oe.txt}).finally(()=>{a++,h("log",{msg:`${oe.dest}下载成功(${a}/${J})`,type:"success"}),be(!0)})}}).finally(()=>{_.length?ye(_.shift()):R(!0)})};ye(Y)})}),H.down=!1,h("log",{type:"success",msg:"下载完成"});const M=[...new Set(ue.map(R=>R.folderName)).values()].map(R=>({name:R,path:`${C}/${R}`}));h("finally",{userSelectFolders:M,path:C})}return u({openConfigDialog:i}),(C,_)=>{const P=mn,X=Dl,G=Rl,J=dl,a=yt,ue=on,O=je,N=Mt,M=Ot,R=il,Y=jt,ye=Yt;return $(),B("div",Ja,[l(ye,{width:700,title:"采集评论",modelValue:v.dialog,"onUpdate:modelValue":_[4]||(_[4]=oe=>v.dialog=oe),"append-to-body":!0,class:"collect-comment-config","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:o(()=>[l(Y,{onClick:_[3]||(_[3]=oe=>v.dialog=!1)},{default:o(()=>[c("取消")]),_:1}),l(Y,{type:"primary",onClick:q,disabled:H.down,loading:H.down},{default:o(()=>[c("确定")]),_:1},8,["disabled","loading"])]),default:o(()=>[l(R,{"label-position":"top"},{default:o(()=>[l(J,{label:"文件规则"},{default:o(()=>[l(P,{type:"success",closable:!1},{default:o(()=>[Za,Qa,Xa]),_:1}),l(G,{modelValue:E.folderType,"onUpdate:modelValue":_[0]||(_[0]=oe=>E.folderType=oe),class:"m-t-10"},{default:o(()=>[l(X,{border:"",label:"review-id"},{default:o(()=>[c("按评论ID")]),_:1}),l(X,{border:"",label:"number"},{default:o(()=>[c("按数字顺序排序")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(J,{label:"采集内容"},{default:o(()=>[l(ue,{modelValue:E.collect_type,"onUpdate:modelValue":_[1]||(_[1]=oe=>E.collect_type=oe)},{default:o(()=>[l(a,{label:"txt"},{default:o(()=>[c("文字")]),_:1}),l(a,{label:"img"},{default:o(()=>[c("图片")]),_:1}),l(a,{label:"video"},{default:o(()=>[c("视频")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(J,{label:"存储路径"},{default:o(()=>[l(N,null,{default:o(()=>[p("span",null,I(E.path),1),l(O,{underline:!1,type:"primary",onClick:A},{default:o(()=>[c("选择")]),_:1})]),_:1})]),_:1}),l(J,{label:"文件名(可为空)"},{default:o(()=>[l(M,{modelValue:E.fileName,"onUpdate:modelValue":_[2]||(_[2]=oe=>E.fileName=oe)},null,8,["modelValue"])]),_:1}),l(J,{label:"最终路径"},{default:o(()=>[c(I(V(g)||"-"),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),ts={class:"collect-comment"},ls={class:"config"},ns=tt({__name:"proxyCollect",emits:["down"],setup(n,{expose:u,emit:h}){const v=Le({dialog:!1,reviewsMap:new Map}),g=Le({fullDsr:!0,widthImg:!1,filterDefault:!0,filterStr:""}),E=ae(()=>{const q=[];return v.reviewsMap.forEach((C,_)=>{q.push({goodsId:_,reviewMap:C})}),q});function i(q){return Jn().then(C=>C.success?(ee.success("已启动"),v.dialog=!0,Promise.resolve(C)):(ee.warning("启动失败"+C.msg.toString()),Promise.reject(C)))}async function A(q){const{value:C}=await Qe.prompt("请输入下载数量","下载评论",{showCancelButton:!0}),_=Number(C),P=q.slice(0,_||0);P.length?h("down",P):ee.warning({message:"没有下载内容",grouping:!0})}function H(q,C){console.log(C);try{const{goodsId:_}=C,P=JSON.parse(C.bodyStr).data,X=v.reviewsMap.get(_)||new Map;let G=0;P.forEach(J=>{var R;const{review_id:a}=J;if(X.has(a))return;const{fullDsr:ue,widthImg:O,filterDefault:N,filterStr:M}=g;if(!(ue&&J.desc_score<5)&&!(O&&!((R=J.pictures)!=null&&R.length))){if(N){const Y=J.comment;if(Y.includes("该用户觉得商品较好")||Y.includes("该用户觉得商品很好，给出了5星好评")||Y.includes("该用户未填写文字评价"))return}M&&J.comment.include(M.trim())||(X.set(a,J),G++)}}),v.reviewsMap.set(_,X),G&&Zn(`addLog('${_}-新增${G}条评论,总计共${X.size}条')`)}catch{}}return ol.off("analyzeController.collectRviews",H),ol.on("analyzeController.collectRviews",H),u({start:i}),(q,C)=>{const _=jt,P=Mt,X=yt,G=Ot,J=ea,a=je,ue=Xo,O=Yt;return $(),B("div",ts,[l(O,{width:700,title:"采集评论",modelValue:v.dialog,"onUpdate:modelValue":C[6]||(C[6]=N=>v.dialog=N),"append-to-body":!0,class:"collect-comment-proxy","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:o(()=>[l(_,{onClick:C[5]||(C[5]=N=>v.dialog=!1)},{default:o(()=>[c("关闭")]),_:1})]),default:o(()=>[l(P,null,{default:o(()=>[l(_,{type:"danger",onClick:C[0]||(C[0]=N=>v.reviewsMap.clear())},{default:o(()=>[c("清空数据")]),_:1})]),_:1}),p("div",ls,[l(P,null,{default:o(()=>[l(X,{modelValue:g.fullDsr,"onUpdate:modelValue":C[1]||(C[1]=N=>g.fullDsr=N),label:"仅5星评论"},null,8,["modelValue"]),l(X,{modelValue:g.widthImg,"onUpdate:modelValue":C[2]||(C[2]=N=>g.widthImg=N),label:"仅带图评论"},null,8,["modelValue"]),l(X,{modelValue:g.filterDefault,"onUpdate:modelValue":C[3]||(C[3]=N=>g.filterDefault=N),label:"过滤默认评论"},null,8,["modelValue"]),l(G,{modelValue:g.filterStr,"onUpdate:modelValue":C[4]||(C[4]=N=>g.filterStr=N),placeholder:"过滤关键词"},null,8,["modelValue"])]),_:1})]),l(ue,{height:400,data:V(E)},{default:o(()=>[l(J,{label:"商品ID",props:"goodsId"},{default:o(N=>[c(I(N.row.goodsId),1)]),_:1}),l(J,{label:"评论数量",props:""},{default:o(N=>[c(I(N.row.reviewMap.size),1)]),_:1}),l(J,{label:"操作",prop:"action",width:120},{default:o(N=>[l(a,{underline:!1,type:"primary",onClick:M=>A([...N.row.reviewMap.values()])},{default:o(()=>[c("下载")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}}),At=n=>(rn("data-v-4fc6bb9c"),n=n(),dn(),n),os={class:"eval-coll"},as={class:"wrapper"},ss={class:"left"},rs={class:"container collect"},is=At(()=>p("span",{class:"label"},"采集数量",-1)),ds={class:"log-container"},us=At(()=>p("span",{class:"label"},"分配方式：",-1)),cs={class:"container comment"},ps={class:"scrollar-container"},ms=["onDragstart","onContextmenu","onClick"],fs={class:"file-name"},gs={class:"folder-options"},hs=At(()=>p("span",{class:"label"},"分配方式：",-1)),_s={class:"right"},vs={class:"list-container"},ys={key:0,class:"commented"},bs=At(()=>p("div",{class:"mask"},null,-1)),ws=At(()=>p("div",{class:"txt"},"已评价",-1)),ks=[bs,ws],Cs={class:"row"},$s={class:"text-overflow",title:"item.order.sku_spec"},Ss={class:"img-container"},Vs=["onClick"],Es={key:0,class:"danger notice"},Ps={class:"bottom"},Ls={class:"config"},xs={class:"cycle"},Ds={class:"progress"},Rs={class:"success"},Ns={class:"danger"},Is={class:"m-r-10"},Ms={class:"m-r-10"},Ts={class:"imgs-container"},Os={class:"row"},As=At(()=>p("span",null,"填充方式",-1)),qs={class:"time m-r-12"},Fs={class:"msg"},Us=tt({__name:"eval&coll",props:{orderList:null},emits:["finishComment"],setup(n,{expose:u,emit:h}){const v=n,g=Zo(),E=Qo(),i=Le({tabActive:"collect",goodsID:"",collectMode:"normal",collectType:"txt-img",count:20,commentOrderList:[],comment_type:"order",comment_list:[],downloadTaskId:"",downloadCurrent:0,downloadMax:0,folderPath:"",commentFileList:[],userSelectFolders:[],ignoreCommentedOrder:!0,deleteCommentedFolder:!1}),A=Le({collect:!1,comment:!1}),H=ae(()=>{const b=new Map;return i.commentOrderList.forEach(d=>{b.set(d.folderName,d.index)}),b});function q(b,d=i.userFolderOption){switch(b){case"open":{d&&vt(d);break}case"delete":{const L=i.userSelectFolders.findIndex(Q=>Q.path==d);L>=0&&i.userSelectFolders.splice(L,1);const F=H.value.get(d||"");if(F!==void 0){const Q=i.commentOrderList.find(le=>le.index===F);mt([Q])}}}_()}function C(){for(;i.userSelectFolders.length;){const b=i.userSelectFolders.pop();q("delete",b.path)}}function _(){i.userFolderOption=void 0}document.removeEventListener("click",_),document.addEventListener("click",_);const P=Le({anonymousAll:!1,anonymousi_ideterminate:!1,batch_count:10,only5Stars:!1,pxq_All:!1,pxq_ideterminate:!1}),X=Dt();We(()=>P.batch_count,b=>{X.setAnySetting("commentBatchCount",b)}),X.getAnySetting("commentBatchCount").then(b=>{b!==void 0&&(P.batch_count=b)}),We(()=>i.commentOrderList,b=>{const d=b.filter(L=>L.anonymous);!b.length||!d.length?(P.anonymousi_ideterminate=!1,P.anonymousAll=!1):b.length===d.length?(P.anonymousi_ideterminate=!1,P.anonymousAll=!0):(P.anonymousi_ideterminate=!0,P.anonymousAll=!1)},{immediate:!0,deep:!0});function G(b){i.commentOrderList.forEach(d=>{d.anonymous=!!b})}We(()=>i.commentOrderList,b=>{const d=b.filter(L=>L.timeline_sync_type);!b.length||!d.length?(P.pxq_ideterminate=!1,P.pxq_All=!1):b.length===d.length?(P.pxq_ideterminate=!1,P.pxq_All=!0):(P.pxq_ideterminate=!0,P.pxq_All=!1)},{immediate:!0,deep:!0});function J(b){i.commentOrderList.forEach(d=>{d.timeline_sync_type=!!b})}We(()=>v.orderList,b=>{A.comment||b&&b.length&&(i.commentOrderList=b.map((d,L)=>({folderName:"",comment:"",anonymous:!1,imgs:[],videos:[],order:d,timeline_sync_type:!0,index:L+1})))},{immediate:!0});function a(){const b=new Map;i.commentOrderList.forEach(L=>{const F=L.order.goods_id;b.has(F)?b.get(F).push(L):b.set(F,[L])});const d=X.orderManage.commentFolder;d&&[...b.keys()].forEach(async L=>{const F=await ql(d+"/"+L);F.length&&Z(F.map(Q=>({path:`${d}\\${L}\\${Q}`,name:Q})),b.get(L))})}const ue=_e(),O=Le({list:[]});function N(b){O.list.push({...b,time:dt().format("YYYY-MM-DD HH:mm:ss")})}We(()=>O.list.length,b=>{ut(()=>{var L,F,Q;let d=((F=(L=ue.value)==null?void 0:L.wrapRef)==null?void 0:F.scrollHeight)||0;(Q=ue.value)==null||Q.setScrollTop(d<0?0:d)})});function M(){let{goodsID:b}=i;if(!(b.length<=5)){if(b.startsWith("https://")){const d=/goods_id=(\d+)/,L=b.match(d);L&&(b=L[1])}i.goodsID=b,E.changeUrl(Xn(i.goodsID))}}const R=_e(),Y=_e();async function ye(){const{collectMode:b,count:d}=i;let{goodsID:L}=i;if(!L)return ee.warning({message:"请输入商品id",grouping:!0});if(g.isErrorPage||E.isVerifiPage)return ee.warning({message:"检测到账号已被风控，请验证或换号",grouping:!0});const F=await lo("getCookies"),Q=new Map(F.map(k=>[k.name,k.value])),we=["api_uid","dilx","_nano_fp","webp","jrpl","njrpl","PDDAccessToken","pdd_user_id","pdd_user_uin","rec_list_personal","pdd_vds"].map(k=>`${k}=${Q.get(k)||""}`).join(";");i.goodsID=L,A.collect=!0,no({goodsID:L,collectMode:b,count:d,cookie:we}).then(k=>{var j;const{reviewList:S}=k.data;i.comment_list=S,N({msg:"已获取列表",type:"success"}),(j=Y.value)==null||j.openConfigDialog(S)}).catch(k=>{N({msg:"任务出错:"+k.msg||"",type:"danger"}),k.code==2?E.changeUrl("https://mobile.pinduoduo.com/login.html"):k.code==3&&E.changeUrl(`https://mobile.yangkeduo.com/psnl_verification.html?VerifyAuthToken=${k.verifyCode}`)}).finally(()=>{A.collect=!1})}async function oe(b){const d=b.dataTransfer.files,L=[];for(let F=0;F<d.length;F++){const{name:Q,path:le}=d[F];Q.includes(".")?L.push(eo(le).then(()=>({name:Q,path:le}))):L.push(Promise.resolve({name:Q,path:le}))}Promise.allSettled(L).then(F=>{const Q=F.filter(le=>le.status==="fulfilled").map(le=>le.value);lt(Q)})}async function be(){const b=await pl({properties:["createDirectory","multiSelections","openDirectory"]});b.canceled||lt(b.filePaths.map(d=>({name:d.split("\\").pop(),path:d})))}function de(b,d){b.dataTransfer.setData("text/plain",d.path)}function lt(b){const d=new Set(i.userSelectFolders.map(L=>L.path));b.forEach(L=>{if(d.has(L.path)){ee.warning({message:"检测到相同路径的文件夹，已过滤!",grouping:!0});return}else i.userSelectFolders.push(L)})}async function T(){if(!i.comment_list.length)return ee.warning("没有可保存的评价");const b=await Nt();if(!b)return;let d=`${i.comment_list.length}条评价${dt(Date.now()).format("YYYYMMDDHHmmss")}`,{value:L}=await Qe.prompt("请输入文件名称(不需要加.txt后缀)","提示",{showCancelButton:!0,inputPlaceholder:`默认为:${d}`});L||(L=d);const F=b+"/"+L;await Ll({dest:F+".txt",data:i.comment_list.map(Q=>Q.comment).join(`
`)}),vt(b)}function Z(b=i.userSelectFolders,d=i.commentOrderList){let L=[];if(b.forEach(le=>{H.value.has(le.path)||L.push(le)}),!L.length){ee.warning({message:"供填充的列表没有数据",grouping:!0});return}const{comment_type:F}=i;F==="random"&&(L=ml.shuffle(L)),N({msg:"开始分配"}),d.find((le,we)=>{if(le.commented||i.ignoreCommentedOrder&&(le.comment||le.imgs.length))return!1;const k=L.shift();return k?(Fl(k.path).then(S=>{const{comment:j,imgs:z,folder:ve,videos:Ve}=S.data;le.comment=j,le.folderName=ve,le.imgs=z.map(Ee=>`${ve}/${Ee}`),le.videos=Ve.map(Ee=>`${ve}/${Ee}`)}),!1):!0}),N({msg:"分配已完成",type:"success"})}function he(b,d){b.preventDefault(),b.stopPropagation(),i.userFolderOption=d.path}function se(b,d){if(b.imgs.length>=6)return ee.warning({message:"一条评价最多能有6张图片",grouping:!0});if(d){b.imgs.push(d);return}$l({title:"选择图片",properties:["openFile","multiSelections"],filters:[{name:"Images",extensions:["jpg","jpeg","png"]}]}).then(L=>{b.imgs.push(...L.data)})}function $e(b,d){if(d){b.videos.push(d);return}$l({title:"选择视频",properties:["openFile","multiSelections"],filters:[{name:"videos",extensions:[".mp4",".avi"]}]}).then(L=>{b.videos.push(...L.data)})}async function Se(){N({msg:"已停止评价(正在进行评价的订单不会停止)",type:"danger"}),A.comment=!1}const K=Le({max:0,success:0,failed:0}),Me=ae(()=>{const{max:b,success:d,failed:L}=K;return b?Number(((d+L)/b*100).toFixed(2)):0});async function Ke(){if(P.batch_count<=0){ee.warning({message:"检测到同时评论的订单数量小于等于0，或没有设置",grouping:!0});return}const{commentOrderList:b}=i,{only5Stars:d}=P;if(!b.length||A.comment)return;const L=b.filter(S=>S.commented?!1:d?!0:S.imgs.length||S.comment);if(!L.length)return ee.warning({message:"没有可以提交的订单!",grouping:!0});O.list.length=0,A.comment=!0;const F=Yo();N({msg:"正在做评价前的准备"}),await new Promise((S,j)=>{F.getList(L.length+10).then(z=>{N({msg:"准备开始评价--"}),S(!0)}).catch(z=>{j(z),N({type:"danger",msg:"准备阶段出现错误"+z.msg}),A.comment=!1})}),K.max=L.length,K.success=0,K.failed=0;let Q=!1,le=!1;const we=new Set;for(let S=0;S<P.batch_count;S++)if(L.length){await Ze(1e3*S);const j=L.shift();j&&k(j)}async function k(S){we.add(S.order.order_sn),await new Promise(async(j,z)=>{const{index:ve,order:Ve,videos:Ee}=S;let ft=S.imgs.slice(0,Ee.length?5:6);N({msg:`第${ve}单准备开始执行`});let at;if(Ee.length){let nt=function(){try{Ae&&zl(me)}catch{}};if(at={},!at)return;let Ae=!1,me=Ee[0];await new Promise(Te=>{oo({url:me}).then(Fe=>{me=Fe.data,N({msg:"调整大小成功"}),Ae=!0,Te(Fe)}).catch(Fe=>{N({msg:"调整大小失败"+Fe.msg,type:"danger"}),Te(Fe)})});const Ye=await ao({url:me,timestamps:[0]});if(Ye.code){N({msg:`第${ve}单处理视频失败：${Ye.msg}`,type:"danger"}),z(!1),nt();return}else{const{imgs:Te,info:Fe}=Ye.data,{duration:ke,size:Re}=Fe.format;at.duration=ke,at.size=Re,await new Promise(Be=>{let t="",e={max:5,current:0,delay:2e3},r={max:5,current:0,delay:2e3};function m(){N({msg:`正在上传第${ve}单的视频封面`}),Bl({image:t,order_sn:Ve.order_sn,tk:F.getItem()}).then(w=>{N({msg:`上传第${ve}单的视频封面成功`,type:"success"}),at.cove=w.data,f()}).catch(async w=>{e.current++,e.current<e.max?(await Ze(e.delay),m()):(N({msg:`上传第${ve}单的视频封面失败,${w.msg}`,type:"danger"}),nt(),Be({success:!1}))})}function f(){N({msg:`正在上传第${ve}单的视频`}),so({url:me,order_sn:Ve.order_sn,tk:F.getItem()}).then(w=>{N({msg:`上传第${ve}单的视频成功`,type:"success"}),at.videoUrl=w.data,Be({success:!0})}).catch(async w=>{r.current++,r.current<r.max?(await Ze(r.delay),f()):(N({msg:`上传第${ve}单的视频失败,${w.msg}`,type:"danger"}),Be({success:!1}))}).finally(()=>{nt()})}Sl(Te[0],!0).then(w=>{t=w,zl(Te[0]),m()}).catch(w=>{nt(),Be({success:!1,msg:"处理视频封面失败"})})})}}let Je=[];if(ft.length){const nt=await new Promise(me=>{const Ye=[],Te=[...ft];function Fe(){const ke=Te.shift();ke?Sl(ke,!0,30).then(Re=>{Ye.push(Re)}).finally(()=>{Fe()}):me(Ye)}Fe()});if(nt.length<ft.length){N({type:"danger",msg:`第${ve}单，图片失败`}),z(!1);return}const Ae=await new Promise(me=>{const Ye=[],Te={code:0,data:Ye,msg:""},Fe=nt.map((ke,Re)=>new Promise((Be,t)=>{let r=0;const m=2e3,f=Re+1;function w(){r||N({msg:`正在上传第${ve}单的第${f}张图片`}),Bl({image:ke,order_sn:Ve.order_sn,tk:F.getItem()}).then(x=>{console.log(x,ke),N({msg:`上传第${ve}单的第${f}张图片成功`,type:"success"}),Ye.push(x.data),Be(!0)}).catch(async x=>{r++,!Te.code&&r<5?(await Ze(m),w()):(N({msg:`上传第${ve}单的第${f}张图片失败,${x.msg}`,type:"danger"}),Te.code=1,Te.msg="图片上传失败",t())})}w()}));Promise.allSettled(Fe).then(ke=>{me(Te)})});if(console.log("uploadImageReponse",Ae),!Ae.code)Je=Ae.data;else{z(!1);return}}let Pt=0;function ze(nt=!1){const{order:Ae,anonymous:me,comment:Ye,timeline_sync_type:Te,index:Fe}=S;nt&&N({msg:`第${Fe}单：${Ae.order_sn}开始执行评价`});let ke;if(Ae.comment_status)ke=ro({comment:Ye,anonymous:me?1:0,pictures:JSON.stringify(Je),goods_id:Ae.goods_id,timeline_sync_type:Te?1:2,order_sn:Ae.order_sn,anti_content:window.get_anti_content()},{showErrorMsg:!1,repeatMax:10,beforeRequest(Re){Re.data.anti_content=window.get_anti_content(),console.log(Re)}});else{const Re={comment:Ye,anonymous:me?1:0,pictures:JSON.stringify(Je),goods_id:Ae.goods_id,timeline_sync_type:Te?1:2,order_sn:Ae.order_sn,anti_content:window.get_anti_content()};if(at){const{cove:Be,duration:t,size:e,videoUrl:r}=at;Re.video={duration:t,size:e,cover_image_height:Be.height,cover_image_width:Be.width,cover_image_url:Be.url,url:r.url,width:Be.width,height:Be.height}}ke=io(Re,{showErrorMsg:!1,repeatMax:10})}ke.then(Re=>{j(Re),uo({ids:Ae.order_sn},{showErrorMsg:!1}),N({type:"success",msg:`第${Fe}单：${Ae.order_sn}评价成功`}),S.commented=!0,i.deleteCommentedFolder&&qe(S)}).catch(async Re=>{Pt++,String(Re.msg).includes("网络")&&Pt<=3?(await Ze(1500),ze()):(z(Re),N({type:"danger",msg:`第${Fe}单：${Ae.order_sn}评价失败,${Re.msg}`}))})}ze(!0)}).then(j=>{K.success++}).catch(j=>{K.failed++}).finally(()=>{if(we.delete(S.order.order_sn),!A.comment){Q||(Q=!0,N({msg:"检测到暂停指令,停止后续执行",type:"warning"}));return}L.length?k(L.shift()):le||(le=!0,N({msg:"检测到所有订单已发送，停止功能将失效",type:"primary"})),we.size||(A.comment=!1,N({msg:"评价结束"}),h("finishComment"))})}}async function qe(b){if(!b)return;const d=b.folderName;if(!d)return;await co(d),i.userSelectFolders.find(F=>F.path==d)&&q("delete",d)}async function bt(b,d){var F;const L=(F=b.dataTransfer)==null?void 0:F.getData("text");if(L){Fl(L).then(Q=>{Q.data&&(d.imgs=Q.data.imgs.map(le=>`${L}/${le}`),d.folderName=Q.data.folder,d.comment=Q.data.comment,d.videos=Q.data.videos.map(le=>`${L}/${le}`))});return}if(b.dataTransfer&&b.dataTransfer.files.length){const Q=b.dataTransfer.files;for(let le=0;le<Q.length;le++){const we=Q[le],k=Ul(we.path);if(k==="image")se(d,we.path);else if(k==="txt")Gt({url:we.path}).then(S=>{S&&(d.comment=S)});else if(k==="video")$e(d,we.path);else if(!we.path.includes(".")){const S=await ql(we.path);d.folderName=we.path,S.forEach(j=>{const z=Ul(j);z==="image"?se(d,`${we.path}/${j}`):z==="video"?$e(d,`${we.path}/${j}`):z==="txt"&&Gt({url:`${we.path}/${j}`}).then(ve=>{ve&&(d.comment=ve)})})}}}}async function mt(b=i.commentOrderList){b===i.commentOrderList&&await Qe({title:"清空评价信息",message:"确定要清空吗？",type:"warning",showCancelButton:!0}),b.forEach(d=>{d.comment="",d.folderName="",d.imgs=[],d.videos=[]})}const re=Le({dialog:!1,imgs:[],commnetStr:"",insertType:"order",imgCount:1,cover_img:!1,cover_txt:!1}),te=ae(()=>{const{commnetStr:b}=re;return b.split(`
`).filter(d=>d)});async function Ue(){const b=await pl({properties:["createDirectory"],filters:[{extensions:["txt"],name:""}]});if(b.canceled)return ee.warning({message:"取消选择文件",grouping:!0});const d=b.filePaths[0],L=await Gt({url:d});console.log(L),re.commnetStr&&!re.commnetStr.endsWith(`
`)&&(re.commnetStr+=`
`),re.commnetStr+=L}async function _t(b){var L;const d=(L=b.dataTransfer)==null?void 0:L.files;if(d)for(let F=0;F<d.length;F++){const Q=d[F];Q.name.endsWith(".txt")&&Gt({url:Q.path}).then(le=>{re.commnetStr&&!re.commnetStr.endsWith(`
`)&&(re.commnetStr+=`
`),re.commnetStr+=le})}}async function Vt(){const b=await pl({properties:["createDirectory","multiSelections"],filters:[{extensions:["jpg","png","jpeg"],name:""}]});if(b.canceled)return ee.warning({message:"取消选择文件",grouping:!0});const d=new Set([...re.imgs,...b.filePaths]);console.log(d),re.imgs=[...d]}function Et(b){const d=re.imgs.findIndex(L=>L===b);d>=0&&re.imgs.splice(d,1)}function wt(){let{imgCount:b,insertType:d,cover_img:L,cover_txt:F}=re,Q=[...te.value],le=[...re.imgs];d=="random"&&(Q=ml.shuffle(Q),le=ml.shuffle(le)),i.commentOrderList.find(we=>Q.length||le.length?(le.length&&(L||!we.imgs.length)&&(we.imgs=le.splice(0,b)),Q.length&&(F||!we.comment)&&(we.comment=Q.shift()||""),!1):!0),re.dialog=!1}function qt(){return A}async function Ft(){const b=new Map;if(i.commentOrderList.forEach(F=>{F.comment&&(b.has(F.comment)?b.get(F.comment).push(F):b.set(F.comment,[F]))}),[...b.values()].forEach(F=>{F.length<=1&&b.delete(F[0].comment)}),!b.size)return ee.warning({message:"没有重复的评论",grouping:!0}),!1;await Qe({title:"重复评论",message:`检测到${b.size}个重复评论，是否重置？`,type:"warning",showCancelButton:!0}),b.forEach(F=>{F.forEach(Q=>{Q.comment=""})}),await Qe({title:"提示",message:"是否需要随机填充评论？(本地随机)",showCancelButton:!0});const L=(await Al()).split(`
`).filter(F=>F);b.forEach(F=>{F.forEach(Q=>{const le=kl(0,L.length-1);Q.comment=L[le]||""})})}return u({getButtonLoading:qt}),(b,d)=>{const L=Jo,F=Go,Q=sn,le=an,we=Ot,k=fn,S=jt,j=al,z=je,ve=Mt,Ve=yt,Ee=pn,ft=Rl,at=Cl,Je=_n,Pt=It,ze=dl,nt=Cl,Ae=cn,me=to,Ye=il,Te=Ne("vxe-list"),Fe=un,ke=Yt,Re=nl("blur"),Be=nl("drop");return $(),B("div",os,[l(es,{ref_key:"collectCommentEl",ref:Y,onLog:d[0]||(d[0]=t=>N(t)),onFinally:d[1]||(d[1]=async({userSelectFolders:t})=>{lt(t),await V(Qe)({title:"提示",message:"下载已完成，是否使用下载内容填充订单?",showCancelButton:!0}),Z(t)})},null,512),l(ns,{ref_key:"ProxyCollectEl",ref:R,onDown:d[2]||(d[2]=t=>{var e;return(e=Y.value)==null?void 0:e.openConfigDialog(t)})},null,512),p("div",as,[p("div",ss,[l(F,{type:"border-card",stretch:!0,modelValue:i.tabActive,"onUpdate:modelValue":d[3]||(d[3]=t=>i.tabActive=t)},{default:o(()=>[l(L,{label:"文件夹批量上评",name:"comment"}),l(L,{label:"采集自定义上评价",name:"collect"})]),_:1},8,["modelValue"]),ce(p("div",rs,[l(we,{placeholder:"请输入商品ID或链接",modelValue:i.goodsID,"onUpdate:modelValue":d[5]||(d[5]=t=>i.goodsID=t),onChange:M,onKeyup:Ht(M,["enter"])},{prepend:o(()=>[l(le,{placeholder:"采集模式",style:{width:"115px"},size:"default",modelValue:i.collectMode,"onUpdate:modelValue":d[4]||(d[4]=t=>i.collectMode=t)},{default:o(()=>[l(Q,{label:"普通采集",value:"normal"})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","onKeyup"]),p("p",null,[is,l(k,{modelValue:i.count,"onUpdate:modelValue":d[6]||(d[6]=t=>i.count=t),precision:0,min:20,controls:!1,class:"m-l-5 m-r-5"},null,8,["modelValue"])]),p("p",null,[ce(($(),ne(S,{type:"success",disabled:A.collect||!V(g).infos||!V(E).isGoodsDetailsPage,loading:A.collect,onClick:ye},{default:o(()=>[c("开始采集")]),_:1},8,["disabled","loading"])),[[Re]]),l(S,{type:"success",onClick:d[7]||(d[7]=()=>{var t;i.goodsID&&V(Xe)("https://mobile.yangkeduo.com/goods.html?goods_id="+i.goodsID),(t=R.value)==null||t.start()})},{default:o(()=>[c("微信解析采集")]),_:1})]),l(j,null,{default:o(()=>[p("div",ds,[($(!0),B(De,null,Oe(i.comment_list,t=>($(),B("p",null,I(t.comment),1))),256))])]),_:1}),p("p",null,[p("span",null,"共"+I(i.comment_list.length)+"条评价 ",1),l(ve,null,{default:o(()=>[l(z,{onClick:T,underline:!1,type:"success",disabled:!i.comment_list.length},{default:o(()=>[c("保存评价")]),_:1},8,["disabled"]),l(z,{onClick:d[8]||(d[8]=t=>i.comment_list.length=0),underline:!1,type:"danger",disabled:!i.comment_list.length},{default:o(()=>[c("清空")]),_:1},8,["disabled"])]),_:1})]),p("p",null,[l(Ve,{modelValue:i.ignoreCommentedOrder,"onUpdate:modelValue":d[9]||(d[9]=t=>i.ignoreCommentedOrder=t),label:"已有评价信息的订单不自动填充"},null,8,["modelValue"]),l(Ve,{modelValue:i.deleteCommentedFolder,"onUpdate:modelValue":d[10]||(d[10]=t=>i.deleteCommentedFolder=t),label:"删除评价成功文件夹"},null,8,["modelValue"])]),p("p",null,[us,l(ft,{modelValue:i.comment_type,"onUpdate:modelValue":d[11]||(d[11]=t=>i.comment_type=t)},{default:o(()=>[l(Ee,{label:"random"},{default:o(()=>[c("随机评")]),_:1}),l(Ee,{label:"order"},{default:o(()=>[c("顺序评")]),_:1})]),_:1},8,["modelValue"])])],512),[[ot,i.tabActive==="collect"]]),ce(p("div",cs,[p("div",ps,[l(j,null,{default:o(()=>[ce(($(),B("div",{class:"log-container folder-container",onDrop:oe},[($(!0),B(De,null,Oe(i.userSelectFolders,t=>($(),ne(Je,{trigger:"contextmenu",visible:i.userFolderOption==t.path,"popper-class":"folder-options-popover","show-arrow":!1,placement:"right",offset:-50},{reference:o(()=>{var e;return[p("div",{class:xe(["folder",{active:t.path==((e=i.userSelectFolder)==null?void 0:e.path),used:V(H).has(t.path)}]),onDragstart:r=>de(r,t),onContextmenu:r=>he(r,t),onClick:r=>i.userSelectFolder=t},[l(at,{href:"icon-file"}),p("p",fs,I(t.name),1),ce(p("span",{class:"used-mask"},"已使用（"+I(V(H).get(t.path))+"）",513),[[ot,V(H).has(t.path)]])],42,ms)]}),default:o(()=>[p("ul",gs,[p("li",{onClick:d[12]||(d[12]=e=>q("open"))},"打开"),p("li",{onClick:d[13]||(d[13]=e=>q("delete"))},"删除")])]),_:2},1032,["visible"]))),256)),p("div",{class:"folder add",onClick:d[14]||(d[14]=t=>be())},[l(Pt,null,{default:o(()=>[l(V(Qn))]),_:1})])],32)),[[Be]])]),_:1})]),p("p",null,[l(z,{type:"success",underline:!1,disabled:!i.userSelectFolders.length,onClick:d[15]||(d[15]=t=>Z())},{default:o(()=>[c("使用以上文件夹评价("+I(i.userSelectFolders.length)+")",1)]),_:1},8,["disabled"]),l(z,{underline:!1,type:"danger",onClick:d[16]||(d[16]=()=>{C()})},{default:o(()=>[c("清空")]),_:1}),l(z,{underline:!1,type:"primary",disabled:!i.userSelectFolder,onClick:d[17]||(d[17]=t=>V(vt)(i.userSelectFolder.path))},{default:o(()=>[c("查看文件夹")]),_:1},8,["disabled"])]),p("p",null,[l(Ve,{modelValue:i.ignoreCommentedOrder,"onUpdate:modelValue":d[18]||(d[18]=t=>i.ignoreCommentedOrder=t),label:"已有评价信息的订单不自动填充"},null,8,["modelValue"]),l(Ve,{modelValue:i.deleteCommentedFolder,"onUpdate:modelValue":d[19]||(d[19]=t=>i.deleteCommentedFolder=t),label:"删除评价成功文件夹"},null,8,["modelValue"])]),p("p",null,[hs,l(ft,{modelValue:i.comment_type,"onUpdate:modelValue":d[20]||(d[20]=t=>i.comment_type=t)},{default:o(()=>[l(Ee,{label:"random"},{default:o(()=>[c("随机评")]),_:1}),l(Ee,{label:"order"},{default:o(()=>[c("顺序评")]),_:1})]),_:1},8,["modelValue"])])],512),[[ot,i.tabActive==="comment"]])]),p("div",_s,[p("header",null,[c("订单列表("+I(i.commentOrderList.length)+") ",1),ce(($(),ne(S,{type:"danger",plain:"",class:"clear-form-button",onClick:d[21]||(d[21]=t=>mt())},{default:o(()=>[c("清空所有")]),_:1})),[[Re]]),l(S,{type:"success",onClick:d[22]||(d[22]=t=>re.dialog=!0)},{default:o(()=>[c("自定义评价和图片")]),_:1}),l(S,{onClick:a},{default:o(()=>[c("使用商品预设评价文件夹")]),_:1}),l(S,{onClick:Ft},{default:o(()=>[c("评论去重")]),_:1})]),p("div",vs,[l(Te,{data:i.commentOrderList,height:546},{default:o(({items:t})=>[($(!0),B(De,null,Oe(t,e=>ce(($(),ne(Ye,{"label-width":"140px",onDrop:r=>bt(r,e)},{default:o(()=>[ce(($(),ne(S,{type:"danger",plain:"",class:"clear-form-button",onClick:r=>mt([e])},{default:o(()=>[c("清空")]),_:2},1032,["onClick"])),[[Re]]),e.commented?($(),B("div",ys,ks)):He("",!0),p("div",Cs,[l(ze,{label:`(${e.index})订单号：`},{default:o(()=>[c(I(e.order.order_sn),1)]),_:2},1032,["label"]),l(ze,{label:"产品ID："},{default:o(()=>[c(I(e.order.goods_id),1)]),_:2},1024)]),l(ze,{label:"规格："},{default:o(()=>[p("div",$s,I(e.order.sku_spec),1)]),_:2},1024),l(ze,{label:"评价图片："},{default:o(()=>[l(ve,{size:8,wrap:!0},{default:o(()=>[($(!0),B(De,null,Oe(e.imgs,(r,m)=>($(),B("div",Ss,[p("div",{class:"close",onClick:()=>{e.imgs.splice(m,1)}},[l(nt,{href:"icon-close"})],8,Vs),l(Ae,{"preview-teleported":!0,"initial-index":m,"preview-src-list":e.imgs.map(f=>V(Ut)(f)),src:V(Ut)(r)},null,8,["initial-index","preview-src-list","src"])]))),256)),l(S,{onClick:r=>se(e),disabled:e.imgs.length>=6},{default:o(()=>[c("导入图片")]),_:2},1032,["onClick","disabled"]),p("span",null,"["+I(e.imgs.length)+"张](加视频最大6张)",1)]),_:2},1024)]),_:2},1024),l(ze,{label:"评价视频："},{default:o(()=>[($(!0),B(De,null,Oe(e.videos,(r,m)=>($(),ne(me,{onAction:f=>{f==="delete"&&e.videos.splice(m,1)},src:V(Ut)(r)},null,8,["onAction","src"]))),256)),e.videos.length?($(),B("span",Es,"多个视频只取第一个视频")):He("",!0)]),_:2},1024),l(ze,{label:"文件夹："},{default:o(()=>[l(z,{type:"primary",underline:!1,onClick:r=>V(vt)(e.folderName)},{default:o(()=>[c(I(e.folderName),1)]),_:2},1032,["onClick"])]),_:2},1024),l(ze,{label:"评语："},{default:o(()=>[l(we,{type:"textarea",rows:5,modelValue:e.comment,"onUpdate:modelValue":r=>e.comment=r},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(ze,{label:" "},{default:o(()=>[l(Ve,{label:"匿名",modelValue:e.anonymous,"onUpdate:modelValue":r=>e.anonymous=r},null,8,["modelValue","onUpdate:modelValue"]),l(Ve,{label:"同步到拼小圈",modelValue:e.timeline_sync_type,"onUpdate:modelValue":r=>e.timeline_sync_type=r},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["onDrop"])),[[Be]])),256))]),_:1},8,["data"])]),p("footer",Ps,[p("div",Ls,[l(Ve,{onChange:G,label:"匿名",modelValue:P.anonymousAll,"onUpdate:modelValue":d[23]||(d[23]=t=>P.anonymousAll=t),indeterminate:P.anonymousi_ideterminate},null,8,["modelValue","indeterminate"]),l(Ve,{onChange:J,label:"拼小圈",modelValue:P.pxq_All,"onUpdate:modelValue":d[24]||(d[24]=t=>P.pxq_All=t),indeterminate:P.pxq_ideterminate},null,8,["modelValue","indeterminate"]),l(Ve,{label:"仅5星好评",modelValue:P.only5Stars,"onUpdate:modelValue":d[25]||(d[25]=t=>P.only5Stars=t)},null,8,["modelValue"]),p("div",xs,[c(" 同时评价 "),l(k,{modelValue:P.batch_count,"onUpdate:modelValue":d[26]||(d[26]=t=>P.batch_count=t),min:1,max:30,precision:0,controls:!1},null,8,["modelValue"]),c(" 单 ")]),ce(($(),ne(S,{type:"primary",disabled:!i.commentOrderList.length||A.comment,onClick:Ke,loading:A.comment},{default:o(()=>[c("开始评价")]),_:1},8,["disabled","loading"])),[[Re]]),ce(($(),ne(S,{type:"danger",disabled:!A.comment,onClick:Se},{default:o(()=>[c(" 停止评价 ")]),_:1},8,["disabled"])),[[Re]])]),p("div",Ds,[l(Fe,{percentage:V(Me)},null,8,["percentage"]),l(ve,null,{default:o(()=>[p("span",Rs,"成功："+I(`${K.success} `),1),p("span",Ns,"失败：："+I(`${K.failed} `),1)]),_:1})])])])]),l(ke,{title:"自定义评价和图片","close-on-press-escape":!1,"append-to-body":!0,modelValue:re.dialog,"onUpdate:modelValue":d[36]||(d[36]=t=>re.dialog=t),class:"diy-comment-dialog"},{footer:o(()=>[l(S,{onClick:d[35]||(d[35]=t=>re.dialog=!1)},{default:o(()=>[c("取消")]),_:1}),l(S,{onClick:wt,type:"primary"},{default:o(()=>[c("填充")]),_:1})]),default:o(()=>[l(Ye,{"label-position":"top"},{default:o(()=>[l(ze,null,{label:o(()=>[p("span",Is,I(`评价集合(${V(te).length})`),1),l(S,{type:"primary",onClick:Ue},{default:o(()=>[c("选择文件填充")]),_:1}),l(S,{type:"success",onClick:d[27]||(d[27]=()=>{V(Al)().then(t=>{t&&(re.commnetStr=t)})})},{default:o(()=>[c("植入通用评价")]),_:1}),l(S,{plain:"",type:"danger",onClick:d[28]||(d[28]=t=>re.commnetStr="")},{default:o(()=>[c("清空")]),_:1})]),default:o(()=>[ce(l(we,{onDrop:_t,type:"textarea",modelValue:re.commnetStr,"onUpdate:modelValue":d[29]||(d[29]=t=>re.commnetStr=t),rows:5,resize:"none",placeholder:"换行隔开"},null,8,["modelValue"]),[[Be]])]),_:1}),l(ze,{class:"img-form-item"},{label:o(()=>[p("span",Ms,I(`图片集合(${re.imgs.length})`),1),l(S,{type:"primary",onClick:Vt},{default:o(()=>[c("选择图片文件(jpg,png,jpeg)")]),_:1}),l(S,{plain:"",type:"danger",onClick:d[30]||(d[30]=t=>re.imgs.length=0)},{default:o(()=>[c("清空")]),_:1})]),default:o(()=>[l(j,{height:200},{default:o(()=>[p("div",Ts,[($(!0),B(De,null,Oe(re.imgs,(t,e)=>($(),B("div",{class:"item",key:t},[l(Ae,{"preview-src-list":re.imgs.map(r=>V(Ut)(r)),"initial-index":e,src:V(Ut)(t),alt:t},null,8,["preview-src-list","initial-index","src","alt"]),l(z,{underline:!1,type:"danger",onClick:r=>Et(t)},{default:o(()=>[c("删除")]),_:2},1032,["onClick"])]))),128))])]),_:1})]),_:1}),p("div",Os,[l(ze,{label:"填充方式"},{label:o(()=>[As]),default:o(()=>[l(ft,{modelValue:re.insertType,"onUpdate:modelValue":d[31]||(d[31]=t=>re.insertType=t)},{default:o(()=>[l(Ee,{label:"order"},{default:o(()=>[c("顺序")]),_:1}),l(Ee,{label:"random"},{default:o(()=>[c("随机")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(ze,{label:"填充设置"},{default:o(()=>[l(Ve,{label:"覆盖订单已有评价",modelValue:re.cover_txt,"onUpdate:modelValue":d[32]||(d[32]=t=>re.cover_txt=t)},null,8,["modelValue"]),l(Ve,{label:"覆盖订单已有图片",modelValue:re.cover_img,"onUpdate:modelValue":d[33]||(d[33]=t=>re.cover_img=t)},null,8,["modelValue"])]),_:1}),l(ze,{label:"一条评价填充图片数量(1-6)"},{default:o(()=>[l(k,{modelValue:re.imgCount,"onUpdate:modelValue":d[34]||(d[34]=t=>re.imgCount=t),precision:0,min:1,max:6},null,8,["modelValue"])]),_:1})])]),_:1})]),_:1},8,["modelValue"]),l(j,{ref_key:"logScroll",ref:ue},{default:o(()=>[($(!0),B(De,null,Oe(O.list,t=>($(),B("p",{class:xe([t.type,"m-t-5 m-b-5"])},[p("span",qs,I(t.time),1),p("span",Fs,I(t.msg),1)],2))),256))]),_:1},512)])}}}),zs=xl(Us,[["__scopeId","data-v-4fc6bb9c"]]),Bs={class:"export-order"},Hs={class:"item"},Ks={class:"folder-container"},Ys=tt({__name:"exportOrder",props:{columns:null},setup(n,{expose:u}){const h=n,v=ae(()=>{const C=[{title:"收件人",key:"name",dataKey:"name",width:80,cellRenderer:_=>ge("div",{},_.rowData.name),excelRule:{value:_=>_.name,"!cols":{wch:"10"}}},{title:"手机号",key:"phone",dataKey:"phone",width:120,cellRenderer:_=>ge("div",{},_.rowData.phone),excelRule:{value:_=>_.phone,"!cols":{wch:"10"}}},{title:"省",key:"province",dataKey:"province",width:120,cellRenderer:_=>ge("div",{},_.rowData.province),excelRule:{value:_=>_.province,"!cols":{wch:"10"}}},{title:"市",key:"city",dataKey:"city",width:140,cellRenderer:_=>ge("div",{},_.rowData.city),excelRule:{value:_=>_.city,"!cols":{wch:"10"}}},{title:"区",key:"district",dataKey:"district",width:120,cellRenderer:_=>ge("div",{},_.rowData.district),excelRule:{value:_=>_.district,"!cols":{wch:"20"}}},{title:"详细地址",key:"address",dataKey:"address",width:120,cellRenderer:_=>_.rowData.address,excelRule:{value:_=>_.address,"!cols":{wch:"20"}}}];return[...h.columns,...C]}),g=ae(()=>{const C=new Map;return v.value.forEach(_=>{C.set(_.dataKey,_)}),C}),E=Dt();We(()=>E.exportOrder,()=>{const C={...E.exportOrder};C.config=[...C.config],E.setOrderSetting("exportOrder",C)},{deep:!0});const i=Le({configShow:!1,list:[],fileName:""});function A(C){i.list=C;let _=`${C.length}单-${dt().format("YYYYMMDDHHmmss")}.xls`;i.fileName=_,i.configShow=!0}u({openConfig:A});async function H(){const C=i.list,_=E.exportOrder.savePath;let P=i.fileName||`${C.length}单-${dt().format("YYYYMMDDHHmmss")}.xls`;if(!P.endsWith(".xls")&&!P.endsWith(".xlsx")&&(P+=".xls"),!C.length)return ee.warning({message:"没有需要导出的订单",grouping:!0});if(!E.exportOrder.config.length)return ee.warning({message:"请选择需要导出的内容",grouping:!0});const X=E.exportOrder.config.map(G=>{const J=g.value.get(G),a={...J.excelRule};return a.label=a.label||J.title||"",a});po({path:`${_}/${P}`,data:[{name:P,data:[X.map(G=>G.label),...C.map(G=>X.map(J=>J.value(G)))]}],options:{sheetOptions:{"!cols":X.map(G=>G["!cols"])}}}).then(G=>{vt(_),i.configShow=!1}).catch(G=>{ee.warning("写入失败"+G)})}async function q(){const C=await Nt();if(!C)return ee.warning({message:"取消选择文件夹"});E.exportOrder.savePath=C}return(C,_)=>{const P=yt,X=on,G=dl,J=Mt,a=je,ue=Ot,O=il,N=jt,M=Yt;return $(),B("div",Bs,[l(M,{width:800,modelValue:i.configShow,"onUpdate:modelValue":_[3]||(_[3]=R=>i.configShow=R),title:"导出内容",class:"export-order-dialog","append-to-body":!0},{footer:o(()=>[l(N,{size:"default",type:"",onClick:_[2]||(_[2]=R=>i.configShow=!1)},{default:o(()=>[c("取消")]),_:1}),l(N,{size:"default",type:"primary",onClick:H,disabled:!V(E).exportOrder.config.length||!V(E).exportOrder.savePath||!i.fileName},{default:o(()=>[c("确定")]),_:1},8,["disabled"])]),default:o(()=>[l(O,{"label-position":"top"},{default:o(()=>[l(G,{label:"导出数据(按选择的顺序排位)"},{default:o(()=>[l(X,{modelValue:V(E).exportOrder.config,"onUpdate:modelValue":_[0]||(_[0]=R=>V(E).exportOrder.config=R)},{default:o(()=>[($(!0),B(De,null,Oe(V(v),R=>($(),B(De,null,[R.excelRule?($(),ne(P,{key:0,label:R.dataKey},{default:o(()=>[c(I(R.excelRule.label||R.title),1)]),_:2},1032,["label"])):He("",!0)],64))),256))]),_:1},8,["modelValue"])]),_:1}),l(G,{label:"预览"},{default:o(()=>[l(J,{wrap:!0},{default:o(()=>[($(!0),B(De,null,Oe(V(E).exportOrder.config,R=>{var Y,ye,oe;return $(),B("span",Hs,I(((ye=(Y=V(g).get(R))==null?void 0:Y.excelRule)==null?void 0:ye.label)||((oe=V(g).get(R))==null?void 0:oe.title)||""),1)}),256))]),_:1})]),_:1}),l(G,{label:"文件夹"},{default:o(()=>[l(J,{wrap:!0},{default:o(()=>[p("span",Ks,I(V(E).exportOrder.savePath),1),l(a,{underline:!1,type:"primary",onClick:q},{default:o(()=>[c(I(V(E).exportOrder.savePath?"更换":"添加"),1)]),_:1})]),_:1})]),_:1}),l(G,{label:"文件名"},{default:o(()=>[l(ue,{modelValue:i.fileName,"onUpdate:modelValue":_[1]||(_[1]=R=>i.fileName=R)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),js=xl(Ys,[["__scopeId","data-v-e8acf140"]]),ht=n=>(rn("data-v-3b718dfa"),n=n(),dn(),n),Ws={class:"table-header"},Gs={class:"btns"},Js={class:"table-contaienr"},Zs={class:"table-row-menu"},Qs={class:"row"},Xs={class:"row"},er={class:"row"},tr=ht(()=>p("p",{class:"title"},"↓预设商品评价↓",-1)),lr={key:1,class:"row"},nr={class:"table-footer"},or={class:"left"},ar=ht(()=>p("span",{class:"m-l-5"},"条",-1)),sr=ht(()=>p("p",{class:"m-t-8 f-s-12",style:{color:"var(--el-text-color-secondary)"}}," 提示：也可以在订单管理，【与此商家聊天】进行手工聊天 ",-1)),rr={class:"controls"},ir={class:"btns"},dr={class:"btns"},ur={class:"link-button-group success"},cr={class:"link-button-group primary"},pr={class:"controls"},mr={class:"btns"},fr=ht(()=>p("p",{class:"btns"},null,-1)),gr=ht(()=>p("strong",null,"默认全选",-1)),hr=["onClick"],_r={class:"store-id"},vr={class:"store-name"},yr={class:""},br={class:"container"},wr=ht(()=>p("span",null,"评价与采集",-1)),kr={class:"content"},Cr={class:"btns"},$r={class:"log-list able-select"},Sr={class:"time"},Vr={class:"msg"},Er={class:"img-container"},Pr={key:0},Lr=["src"],xr={key:2,class:"danger"},Dr=ht(()=>p("span",null,"获取支付链接错误 ",-1)),Rr=ht(()=>p("br",null,null,-1)),Nr={class:"danger f-s-20"},Ir={class:"btn"},Mr=ht(()=>p("span",null,"无",-1)),Tr={class:"imgs",style:{display:"flex","flex-wrap":"wrap"}},Or=tt({__name:"orderManage",setup(n){mo(t=>({"66591f54":me.x,"66591f55":me.y}));const u=Le({limit:1e3,total:0,page:1}),h=function(){const t=new Date;return{year:t.getFullYear(),month:t.getMonth(),day:t.getDate()}}(),v={pendingPay:0,pendingShare:1,payed:2,pendingReceive:3,pendingComment:4},g=Le({status:-1,statusList:[{label:"全部",value:-1,count:0},{label:"待支付",value:0,count:0},{label:"待发货",value:2,count:0},{label:"待收货",value:3,count:0},{label:"已签收",value:-3,count:0},{label:"待评价",value:4,count:0},{label:"已评价",value:-4,count:0},{label:"已退款",value:7,count:0},{label:"交易已取消",value:5,count:0},{label:"风控订单",value:8,count:0},{label:"回收站",value:99,count:0}],type:"",time:[new Date(h.year,h.month,h.day,0,0,0).valueOf(),new Date(h.year,h.month,h.day,23,59,59).valueOf()],commonSearch:{key:"goods_id",list:[{label:"商品ID",value:"goods_id"},{label:"店铺名",value:"shop_name"},{label:"订单编号",value:"order_sn"},{label:"商品价格",value:"price"},{label:"规格",value:"sku"}],value:""}});We(()=>g.commonSearch.value,t=>{t&&(String(t).length>5&&/^\d+$/.test(t)?g.commonSearch.key="goods_id":/^[0-9\.]+$/.test(t)?g.commonSearch.key="price":/^\d{6}-\d+/.test(t)?g.commonSearch.key="order_sn":String(t).length>15?g.commonSearch.key="sku":g.commonSearch.key="shop_name")});const E=_e(),i=Le({isChildShow:!1,commentOrderList:[],orderList:[],checkAll:!1,indeterminate:!1,requestTids:new Set}),A=_e(!0);fo(()=>{A.value=!1,ut(()=>{A.value=!0})});function H(t,e){e?q.selectionMap.set(t.order_sn,t):q.selectionMap.delete(t.order_sn)}const q=Le({selectionMap:new Map});function C(){ut(()=>{const t=E.value;if(t){const e=t.getCheckboxRecords();q.selectionMap.clear(),e.forEach(r=>{q.selectionMap.set(r.order_sn,r)})}})}function _(t){return ge(je,{class:"text-overflow",underline:!1,onClick:()=>{Xe(t)}},()=>t)}function P(t,e,r=!1){return ge(Pl,{popperClass:"table-tooltip",content:e,showAfter:200},{default:()=>r?_(t):ge("div",{class:"text-overflow pointer"},{default:()=>t})})}const X=[{key:"selection",width:35,cellRenderer:({rowData:t})=>ge(yt,{onChange:e=>H(t,e),modelValue:t.checked}),headerCellRenderer:()=>ge(yt,{onChange:t=>{Y(t?"check-all":"remove-all")},indeterminate:i.indeterminate,modelValue:i.checkAll}),fixed:el.LEFT},{title:"序号",key:"index",width:50,cellRenderer:t=>{const{limit:e,page:r}=u;return ge("div",{},t.rowIndex+1+(r-1)*e)},fixed:el.LEFT},{title:"所属店铺",key:"shop_name",dataKey:"shop_name",width:120,cellRenderer:t=>_(t.rowData.shop_name),excelRule:{value:t=>t.shop_name,"!cols":{wch:25}}},{title:"下单时间",key:"add_time",dataKey:"add_time",width:140,cellRenderer:t=>P(dt(t.rowData.add_time*1e3).format("YYYY-MM-DD HH:mm:ss"),dt(t.rowData.add_time*1e3).format("YYYY-MM-DD HH:mm:ss")),excelRule:{value:t=>dt(t.add_time*1e3).format("YYYY-MM-DD HH:mm:ss"),"!cols":{wch:50}}},{title:"下单类型",key:"type",dataKey:"type",width:70,cellRenderer:t=>{var e;return ge("div",{},(e=Jt.find(r=>r.value==t.rowData.type))==null?void 0:e.label)},excelRule:{value:t=>{var e;return((e=Jt.find(r=>r.value==t.type))==null?void 0:e.label)||""},"!cols":{wch:20}}},{title:"订单状态",key:"order_status",dataKey:"order_status",width:70,cellRenderer:t=>{var r;const e=t.rowData.order_status_prompt||((r=g.statusList.find(m=>m.value==Number(t.rowData.combined_order_status)))==null?void 0:r.label)||"";return P(e,e)},excelRule:{value:t=>t.order_status_prompt,"!cols":{wch:25}}},{title:"订单编号",key:"order_sn",dataKey:"order_sn",width:170,cellRenderer:t=>_(t.rowData.order_sn),excelRule:{value:t=>t.order_sn,"!cols":{wch:25}}},{title:"价格(元)",key:"order_amount",dataKey:"order_amount",width:70,cellRenderer:t=>ge("div",{},Number(t.rowData.order_amount)/100),excelRule:{value:t=>Number(t.order_amount)/100,"!cols":{wch:15}}},{title:"购买数量",key:"goods_number",dataKey:"goods_number",width:70,cellRenderer:t=>ge("div",{},t.rowData.goods_number),excelRule:{value:t=>String(t.goods_number),"!cols":{wch:10}}},{title:"商品ID",key:"goods_id",dataKey:"goods_id",width:110,cellRenderer:t=>_(t.rowData.goods_id),excelRule:{value:t=>String(t.goods_id),"!cols":{wch:15}}},{title:"小号",key:"account",dataKey:"account",width:110,cellRenderer:t=>_(t.rowData.account),excelRule:{value:t=>String(t.account),"!cols":{wch:30}}},{title:"商品名称",key:"goods_name",dataKey:"goods_name",width:200,cellRenderer:t=>P(t.rowData.goods_name,t.rowData.goods_name),excelRule:{value:t=>t.goods_name,"!cols":{wch:50}}},{title:"购买规格",key:"sku_spec",dataKey:"sku_spec",width:200,maxWidth:600,minWidth:200,sortable:!0,headerCellRenderer:()=>ge(Mt,{},{default:()=>[ge("span",{},"规格"),ge(je,{underline:!1,type:"primary",onClick:()=>{const t=new Map;i.orderList.forEach(r=>{var f;const{sku_spec:m}=r;t.has(m)?(f=t.get(m))==null||f.push(r):t.set(m,[r])});const e=[...t.values()];i.orderList=e.flat(1)}},()=>"排序")]}),cellRenderer:t=>P(t.rowData.sku_spec,t.rowData.sku_spec,!0),excelRule:{value:t=>t.sku_spec,"!cols":{wch:50}}},{title:"收货信息",width:200,dataKey:"recipient",cellRenderer:t=>{const{name:e,phone:r,province:m,city:f,district:w,address:x}=t.rowData,y=`${e} ${r} 
 ${m} ${f} ${w} 
 ${x}`;return P(y,y)},excelRule:{label:"订单地址",value:t=>t.province+t.city+t.district+t.address,"!cols":{wch:100}}},{title:"快递公司",key:"express",dataKey:"express",width:120,cellRenderer:t=>P(t.rowData.express,t.rowData.express),excelRule:{value:t=>t.express||""}},{title:"快递单号",key:"tracking_number",dataKey:"tracking_number",width:120,cellRenderer:t=>_(t.rowData.tracking_number),excelRule:{value:t=>String(t.tracking_number),"!cols":{wch:45}}},{title:"物流信息",key:"logistics",dataKey:"logistics",width:130,cellRenderer:t=>P(t.rowData.logistics,t.rowData.logistics),excelRule:{value:t=>t.logistics,"!cols":{wch:80}}},{title:"店铺收藏",key:"is_collect",dataKey:"is_collect",width:100,cellRenderer:t=>ge("div",{},t.rowData.is_collect?"是":"否")},{title:"店铺关注",key:"is_attention",dataKey:"is_attention",width:100,cellRenderer:t=>ge("div",{},t.rowData.is_attention?"是":"否")},{title:"操作",key:"action",dataKey:"action",width:60,fixed:el.RIGHT,cellRenderer:t=>ge(_n,{popperClass:"table-action-popover",placement:"bottom-end",showArrow:!1,showAfter:100},{reference:()=>ge("span",{class:"pointer"},ge(It,{},{default:()=>ge(go)})),default:()=>ge("div",{class:"action-list"},[ge("p",{onClick:()=>J([t.rowData])},"手工评价"),ge("p",{onClick:()=>J([t.rowData])},"追加评价"),ge("p",{onClick:()=>Me("chat_dialog",t.rowData)},"与此商聊天"),ge("p",{onClick:()=>Me("change_addr_dialog",t.rowData)},"修改地址")])})}],G=_e();function J(t=R.value){var r;if((r=G.value)!=null&&r.getButtonLoading().comment){ee.info({message:"当前有评价正在执行",grouping:!0}),i.isChildShow=!0;return}const e=t.filter(m=>m.combined_order_status===v.pendingComment);e.length&&(i.commentOrderList=e),i.isChildShow=!0}const a=Le({getList:!1,isOrderSync:!1,isDelete:!1,isCancel:!1,changePrice:!1,isRemark:!1,isRefund:!1,cancelRefund:!1,isConfirm:!1,exportCard:!1,exportPayCode:!1,autoSell:!1,autoSpell:!1,chatWidthShop:!1,changeAddr:!1,apply_code:!1,apply_auto:!1,apply_wechat:!1,orderRecover:!1,intervene:!1}),ue=_e(),O=Le({dialog:!1,list:[{label:"取消订单",value:"cancel",buttonLoading:"isCancel"},{label:"订单改价",value:"changeOrderPrice",buttonLoading:"changePrice"},{label:"订单删除",value:"delete",buttonLoading:"isDelete"},{label:"订单退款",value:"refund",buttonLoading:"isRefund"},{label:"取消退款",value:"cancelRefund",buttonLoading:"cancelRefund"},{label:"订单确认",value:"confirm",buttonLoading:"isConfirm"},{label:"券自动核销",value:"autoSell",buttonLoading:"autoSell"},{label:"订单免拼",value:"autoSpell",buttonLoading:"autoSpell"},{label:"订单备注",value:"remark",buttonLoading:"isRemark"}],active:"cancel",map:new Map}),N=ae(()=>{const{list:t,active:e}=O;return t.find(r=>r.value===e)});We([()=>O.map,()=>O.active],t=>{ut(()=>{var r,m,f;let e=((m=(r=ue.value)==null?void 0:r.wrapRef)==null?void 0:m.scrollHeight)||0;(f=ue.value)==null||f.setScrollTop(e<0?0:e)})},{deep:!0});function M(t,e){var r;O.map.get(t)||O.map.set(t,[]),(r=O.map.get(t))==null||r.push({...e,time:dt().format("YYYY-MM-DD HH:mm:ss")})}const R=ae(()=>[...q.selectionMap.values()]);We([()=>R.value.length,()=>i.orderList.length],()=>{const t=i.orderList.length,e=R.value.length;e?e===t?(i.indeterminate=!1,i.checkAll=!0):(i.indeterminate=!0,i.checkAll=!1):(i.indeterminate=!1,i.checkAll=!1)},{immediate:!0});async function Y(t){var e,r;switch(t){case"check-all":{(e=E.value)==null||e.setAllCheckboxRow(!0);break}case"remove-all":{(r=E.value)==null||r.clearCheckboxRow();break}case"reverse":{i.orderList.forEach(m=>{var f;(f=E.value)==null||f.toggleCheckboxRow(m)});break}case"area-select":await Qe.prompt(`请输入需要选中的订单(用-隔开),留空表示起始值或末尾值
(例：20-30 ; -30 ; 20- ; -)`,"区域选择",{showCancelButton:!0,inputPlaceholder:"请输入范围"}).then(m=>{var U;const{value:f}=m;let[w,x]=f.split("-").map(W=>Number(W.trim()));if(w=w-1<0?0:w-1,x=x||i.orderList.length,w>x)return ee.warning({message:"最小值大于最大值",grouping:!0});Y("remove-all");const y=((U=E.value)==null?void 0:U.getTableData().visibleData.slice(w,x))||[],D=new Set(y.map(W=>W.order_sn));i.orderList.find(W=>{var ie;return D.has(W.order_sn)&&((ie=E.value)==null||ie.setCheckboxRow(W,!0),D.delete(W.order_sn)),!D.size})})}C()}async function ye(t){switch(t){case"Statistics":{const e={all:0,selection:0},r=q.selectionMap;return i.orderList.forEach(m=>{const f=Number(m.order_amount/100);r.has(m.order_sn)&&(e.selection+=f),e.all+=f}),Qe({title:"订单金额统计",message:ge("div",{},[ge("p",{class:"success "},`选中订单：${e.selection.toFixed(2)}元`),ge("p",{class:"danger m-t-10"},`本页订单：${e.all.toFixed(2)}元`)])})}}}function oe(t){a.getList||(g.status=t.value,be())}function be(t=!0){const{limit:e,page:r}=u,{status:m,time:f,type:w,commonSearch:x}=g,y=Date.now(),D={limit:e,page:r,status:m===-1||m===99?void 0:Math.abs(m),time:f&&f.map(U=>Math.floor(U/1e3)).join(" - "),type:w,comment_status:m===-4?1:void 0,shipping_status:m===-3?1:void 0,is_delete:m===99?1:void 0};Reflect.set(D,x.key,x.value.trim()),a.getList=!0,console.log(D),wo(D).then(U=>{console.log("getList - success",U),i.orderList=U.data.data,u.total=U.data.total,ut(()=>{let W=q.selectionMap.size;i.orderList.find(ie=>{var Pe;return q.selectionMap.has(ie.order_sn)&&(W--,(Pe=E.value)==null||Pe.setCheckboxRow(ie,!0)),W<=0}),C()})}).catch(U=>{console.log("getList - error",U)}).finally(()=>{console.log("用时:",Date.now()-y),a.getList=!1})}const de=Le({map:new Map,checkedSet:new Set,dialog:!1,type:"price",price:.01,cut:1});function lt(t=R.value){if(t=t.filter(r=>Number(r.combined_order_status)===v.pendingPay),!t.length)return ee.warning({message:"选择的订单中没有符合条件的订单"});const{map:e}=de;e.clear(),t.forEach(r=>{const m=e.get(r.shop_id);m?m.push(r):e.set(r.shop_id,[r])}),de.checkedSet=new Set([...e.keys()]),de.dialog=!0}async function T(){a.changePrice=!0,de.dialog=!1;const t="changeOrderPrice";O.active=t;const{map:e,type:r,cut:m,checkedSet:f}=de,w=de.price*100,x=[];f.forEach(y=>{e.has(y)&&x.push(new Promise(async(D,U)=>{const W=e.get(y),ie=W.length;let Pe=0;function Ie(Ce=7){if(!W.length){U();return}const fe=[];for(;fe.length<Ce&&W.length;)fe.push(W.shift());Promise.allSettled(fe.map((ct,st)=>{const{order_amount:pt,order_sn:gt,shop_name:Lt,id:ul,shop_id:cl}=ct;let xt;switch(r){case"cut":{xt=pt*(10-m)/10;break}case"price":{xt=pt-w;break}}return xo({store_id:cl,goodsDiscount:xt,order_sn:gt,shop_name:Lt}).then(kt=>{M(t,{type:"success",msg:`【${Lt}】:${gt}修改成功,(${++Pe}/${ie})`})}).catch(kt=>{console.log(kt),kt.code==2?(M(t,{type:"danger",msg:`【${Lt}】:${gt}修改失败：会话已过期,请重新登录店铺,(${++Pe}/${ie})`}),W.length=0):M(t,{type:"danger",msg:`【${Lt}】:${gt}修改失败：${kt.msg},(${++Pe}/${ie})`})})})).then(async()=>{await Promise.allSettled([zt({ids:fe.map(ct=>ct.order_sn).join(",")},{showErrorMsg:!1})]),a.changePrice?W.length?(await Ze(500),Ie()):(await Ze(1e3),D(!0)):(M(t,{msg:"检测到暂停改价触发，已停止后续操作"}),await Ze(1e3),U())})}Ie(1)}))}),await Promise.allSettled(x),a.changePrice=!1,Je(t,1e3)}const Z=Le({dialog:!1,remark:"",tag:"",tagList:[{label:"红色",value:"red"},{label:"紫色",value:"purple"},{label:"绿色",value:"green"},{label:"蓝色",value:"blue"},{label:"黄色",value:"yellow",color:"orange"}]});function he(t=R.value){if(Z.dialog=!1,!t.length){ee.warning({message:"请选择订单",grouping:!0});return}a.isRemark=!0;const e="remark";O.active=e;const{remark:r,tag:m,tagList:f}=Z,w=f.find(W=>W.value==m),x=t.length;let y=1;const D=[...t],U=async(W=10)=>{D.length?(await Promise.allSettled(D.splice(0,W).map(ie=>{const{order_sn:Pe,shop_id:Ie,shop_name:Ce}=ie;return Do({orderSn:Pe,store_id:Ie,remark:r,remarkTagName:(w==null?void 0:w.label)||null,remarkTag:(w==null?void 0:w.value.toUpperCase())||null}).then(fe=>{M(e,{type:"success",msg:`【${Ce}】:${Pe}备注成功,(${y}/${x})`})}).catch(fe=>{if(fe.code==2){M(e,{type:"danger",msg:`【${Ce}】:${Pe}备注失败：会话已过期,请重新登录店铺,(${y}/${x})`});const ct=Kl(),st=ct.tableList.find(pt=>pt.mallId===Number(Ie));st&&ct.disabledStore(st),D.length=0}else M(e,{type:"danger",msg:`【${Ce}】:${Pe}备注失败：${fe.msg},(${y}/${x})`})}).finally(()=>{y++})})),a.isRemark?U():M(e,{msg:"检测到暂停备注触发，已停止后续操作"})):(a.isRemark=!1,Je(e))};U(1)}const se=Le({progressShow:!1,max:0,current:0,stopFlag:!1}),$e=ae(()=>{const{max:t,current:e}=se;if(t){const r=Math.ceil(e/t*100);return r>=100?100:r}else return 0});function Se(t=R.value){if(!t.length)return ee.warning({message:"请选择需要同步的订单",grouping:!0});const e=[...t];se.current=0,se.max=e.length,se.stopFlag=!1,se.progressShow=!0;const r=20;a.isOrderSync=!0;function m(){const f=[];for(let w=0;w<r;w++){const x=e.shift();x&&f.push(x)}return zt({ids:f.map(w=>w.order_sn).join(",")},{loading:!1,showErrorMsg:!1}).finally(async()=>{se.current+=f.length,e.length?m():(a.isOrderSync=!1,se.progressShow=!1,be(!1))})}m()}sl(()=>{be()});const K=Le({row:null,chat_dialog:!1,chat_type:"order",chat_num:3,chat_content:"",change_addr_dialog:!1,change_addr:{phone:"",name:"",p_c_d:[],address:"",options:[]}});function Me(t,e){switch(K[t]=!0,K.row=e,t){case"change_addr_dialog":{K.change_addr.name=e.name||"",K.change_addr.phone=e.phone||"",K.change_addr.address=e.address||"",K.change_addr.p_c_d=[e.province,e.city,e.district];break}}}Hl({pid:0}).then(t=>{console.log("addressList",t),K.change_addr.options=t.data});const Ke=_e(),qe=_e();async function bt(){var w;await((w=qe.value)==null?void 0:w.validate());const{row:t}=K,{name:e,p_c_d:r,phone:m,address:f}=K.change_addr;a.changeAddr=!0,ko({order_sn:t.order_sn,name:e,phone:m,address:f,province:r[0],city:r[1],district:r[2]}).then(x=>{ee.success("修改成功"),K.change_addr_dialog=!1,be()}).finally(()=>{a.changeAddr=!1})}function mt(){const{row:t,chat_num:e,chat_type:r,chat_content:m}=K;if(!t||!m)ee.warning({message:"请填写聊天内容",grouping:!0});else{const f=m.split(`
`).filter(D=>D);if(!f.length)return ee.warning({message:"聊天内容列表为空",grouping:!0});let w=0;const x=()=>{switch(r){case"order":return(f.length+w++)%f.length;case"random":return kl(0,f.length)}},y=[];a.chatWidthShop=!0;for(let D=0;D<e;D++)y.push(new Promise((U,W)=>{setTimeout(()=>{Co({shop_id:t.shop_id,account:t.account,content:f[x()]}).then(U).catch(W)},D*1e3)}));Promise.allSettled(y).then(D=>{a.chatWidthShop=!1,K.chat_dialog=!1,ee.success("消息发送完成")})}}const re={lazy:!0,lazyLoad(t,e){const{data:r,level:m}=t;Hl({pid:r.id}).then(f=>{e(f.data.map(w=>({...w,leaf:m>=2})))})},label:"name",value:"name"},te=Le({dialog:!1,codePaylist:[],index:0}),Ue=ae(()=>te.codePaylist[te.index]);function _t(t,e=R.value){const r=e.filter(D=>D.combined_order_status===v.pendingPay);if(!r.length)return ee.warning({message:e.length?"选择的订单没有未支付的订单":"请选择订单",grouping:!0});if(t==="code"){Ft(e);return}else if(t==="wechat"){b(e);return}if(!Dt().pay.zfb_pass)return ee.warning({message:"没有设置支付密码，不能自动支付",grouping:!0});const f=10;let w=0;const x=hl();a.apply_auto=!0;function y(){const D=r.slice(w,w+f);w+=D.length;const U=D.map(W=>gl({order_sn:W.order_sn},{showErrorMsg:!1,repeatWhenFailed:!0}).then(ie=>{if(!ie.code&&ie.data.url){const{id:Pe,order_sn:Ie}=W;x.addToPendding([{url:ie.data.url,order_sn:Ie,id:Pe||Ie,type:"auto"}])}}).catch(ie=>{ee.error({message:"获取支付链接失败",grouping:!0})}));Promise.allSettled(U).then(()=>{w>=r.length?a.apply_auto=!1:a.apply_auto&&y()})}y()}async function Vt(){await Qe({title:"提示",type:"warning",message:"确定要停止自动支付吗？",showCancelButton:!0}),$o(),hl().stop(),a.apply_auto=!1}async function Et(t,e){const r=hl();switch(e.type){case"success":{r.resolveResult(e),e.left===0&&(await Ze(1500),be());break}case"close":{await Ze(1500),be();break}case"error":e.msg&&ee.error({grouping:!0,message:`${e.order_sn}:${e.msg}`})}}ol.off("autoapply-complete",Et),ol.on("autoapply-complete",Et);async function wt(){te.dialog&&(await new Promise(async t=>{!Ue.value||Ue.value.pay?(await Ze(1e3),t(!0)):qt(Ue.value).finally(async()=>{await Ze(800),t(!0)})}),wt())}function qt(t){return Ro({order_sn:t.order.order_sn}).then(async e=>{if(e.data[0]){const{order_status:r,order_sn:m}=e.data[0];if(r==1){const f=te.codePaylist.find(w=>w.order.order_sn==m);f&&(f.pay=!0),te.codePaylist.find(w=>!w.pay)?te.index=(te.codePaylist.length+te.index+1)%te.codePaylist.length:(await Ze(1e3),te.dialog=!1,be())}}return e})}function Ft(t=R.value){a.apply_code=!0,te.codePaylist=t.map(f=>({order:f,type:"code"})),te.index=0,te.dialog=!0;try{So()}catch{a.apply_code=!0}const e=10;let r=0;function m(){const f=te.codePaylist.slice(r,r+e);r+=f.length;const w=f.map(x=>gl({order_sn:x.order.order_sn},{showErrorMsg:!1}).then(y=>{x.payInfo=y,!y.code&&y.data.url?_l.toDataURL(y.data.url).then(D=>{x.payInfo.imgBase64=D}):x.payInfo.msg="没有获取到支付链接"}).catch(y=>{x.payInfo=y}));Promise.allSettled(w).then(()=>{te.dialog?r>=te.codePaylist.length?a.apply_code=!1:m():a.apply_code=!1})}m(),wt()}function b(t=R.value){a.apply_wechat=!0,te.index=0,te.dialog=!0,te.codePaylist=t.map(f=>({order:f,type:"wechat"}));const e=10;let r=0;function m(){const f=te.codePaylist.slice(r,r+e);r+=f.length;const w=f.map(x=>No({order_sn:x.order.order_sn},{showErrorMsg:!1}).then(y=>{x.payInfo=y,!y.code&&y.data?_l.toDataURL(y.data).then(D=>{x.payInfo.imgBase64=D}):x.payInfo.msg="没有获取到支付链接"}).catch(y=>{x.payInfo=y}));Promise.allSettled(w).then(()=>{r>=te.codePaylist.length?a.apply_wechat=!1:m()})}m(),wt()}async function d(t=R.value){if(!t.length)return ee.warning({message:"请选择需要导出的订单",grouping:!0});a.exportPayCode=!0;const e=10;let r=0;const m=[];async function f(){if(a.exportPayCode=!1,!m.length){ee.warning({message:"没有获取到支付链接",grouping:!0});return}const x=await Nt();if(!x){ee.warning({message:"取消选择存放位置"});return}ee.success("开始保存，完成后自动打开文件夹"),await Promise.allSettled(m.map(y=>new Promise((D,U)=>{_l.toDataURL(y.data.url).then(W=>Uo({dest:`${x}/${y.data.order_sn}.png`,data:W})).then(()=>{D(!0)}).catch(()=>{U()})}))),vt(x)}function w(){const x=t.slice(r,r+e);r+=x.length;const y=x.map(D=>gl({order_sn:D.order_sn},{showErrorMsg:!1}).then(U=>{!U.code&&U.data.url&&m.push(U)}));Promise.allSettled(y).then(D=>{a.exportPayCode?r>=t.length?f():w():f()})}w()}async function L(t=R.value){const e=t.filter(r=>r.order_status_prompt=="电子券码待使用");if(!e.length)return ee.warning({message:t.length?"没有可以导出的订单":"请选择订单"});a.exportCard=!0,Vo({order_sns:e.map(r=>r.order_sn).join(",")}).then(async r=>{const m=await Nt();return m?(await Ll({dest:`${m}/导出卡券${dt().format("YYYYMMDDhhmmss")}.txt`,data:r.data.map(f=>`订单：${f.order_sn}	卡券核销码：${f.card}`).join(`
`)}),m):Promise.reject()}).then(r=>{vt(r)}).finally(()=>{a.exportCard=!1})}const F=_e();async function Q(t=R.value){var r;if(!t.length)return ee.warning({message:"请选择需要导出的订单!",grouping:!0});t.find(m=>m.status===v.pendingPay)&&await Qe({title:"提示",message:"检测到选中的订单中有 待支付 订单,是否继续导出？",showCancelButton:!0,type:"warning"}),(r=F.value)==null||r.openConfig(t)}async function le(t=R.value){const e=t.filter(D=>D.combined_order_status===v.pendingPay);if(!e.length)return ee.warning({message:t.length?"选择的订单中没有可以取消的订单":"请选择需要取消的订单",grouping:!0});await Qe({title:"提示",message:"确定取消选中订单吗?",showCancelButton:!0}),a.isCancel=!0;const r="cancel";M(r,{msg:"*".repeat(99)});const m=10;let f=0,w=0;const x=e.length;O.dialog=!0,O.active=r;function y(){const D=e.slice(f,f+m);f+=D.length;const U=D.map(W=>Io({order_sn:W.order_sn},{showErrorMsg:!1}).then(ie=>{w++,M(r,{type:"success",msg:`${W.order_sn},取消成功,(${w}/${x})`})}).catch(ie=>{w++,M(r,{type:"danger",msg:`${W.order_sn},取消失败,${ie.msg},(${w}/${x})`})}));Promise.allSettled(U).then(async()=>{await Promise.allSettled([zt({ids:D.map(W=>W.order_sn).join(",")},{showErrorMsg:!1})]),a.isCancel?f>=e.length?(Je(r),a.isCancel=!1):y():M(r,{type:"warning",msg:"检测到任务已被暂停，停止后续请求"})})}y()}async function we(t=R.value){const e=[...t];if(!e.length)return ee.warning({message:t.length?"选择的订单中没有可以删除的订单":"请选择需要删除的订单",grouping:!0});await Qe({title:"提示",message:"是否确定删除选中并（永久删除）小号内订单吗?",showCancelButton:!0}),a.isDelete=!0;const r="delete";M(r,{msg:"*".repeat(99)}),M(r,{msg:`本次已成功筛选出${e.length}单`,type:"success"});const m=10;let f=0;O.dialog=!0,O.active=r;function w(){const x=e.slice(f,f+m);f+=x.length;const y=x.map((D,U)=>Mo({order_sn:D.order_sn},{showErrorMsg:!1}).then(W=>{M(r,{type:"success",msg:`${D.order_sn},删除成功(${f-y.length+U+1}),${W.data}`})}).catch(W=>{M(r,{type:"danger",msg:`${D.order_sn},删除失败(${f-y.length+U+1}),${W.msg}`})}).finally(()=>{}));Promise.allSettled(y).then(()=>{M(r,{msg:`${f} -- ${e.length}`}),a.isDelete?f>=e.length?(Je(r),a.isDelete=!1):w():M(r,{msg:"检测到暂停指令，已停止后续操作"})})}w()}async function k(t=R.value){const e=t.filter(y=>y.order_status_prompt=="电子券码待使用");if(!e.length)return ee.warning({message:t.length?"没有可以核销的订单":"请选择订单"});const r=new Map;e.forEach(y=>{const D=r.get(y.shop_id);if(D)D.orders.push(y);else{const{shop_name:U,shop_id:W}=y;r.set(y.shop_id,{shopInfo:{shop_id:W,shop_name:U},orders:[y]})}}),a.autoSell=!0;let m=0;function f(){m++,m>=r.size&&(a.autoSell=!1,Je(w))}const w="autoSell";M(w,{msg:"*".repeat(100)}),O.active=w,O.dialog=!0;const x=Kl();for(const[y,D]of r){let U=function(){const Ce=Ie.splice(0,10);M(w,{msg:`正在获取店铺${ie}当前批次的核销码-----`}),To({order_sns:Ce.map(fe=>fe.order_sn).join(",")},{showErrorMsg:!1}).then(async fe=>{const ct=fe.data;await Promise.allSettled(ct.map(async(st,pt)=>(await Ze(pt*300),st.code?(M(w,{msg:`店铺${ie}的订单${st.order_sn}获取核销码失败`,type:"danger"}),Promise.reject()):Oo(Pe,st).then(gt=>(M(w,{msg:`店铺${ie}的订单${st.order_sn}操作成功`,type:"success"}),zt({ids:st.order_sn},{showErrorMsg:!1}))).catch(gt=>{M(w,{msg:`店铺${ie}的订单${st.order_sn}操作失败:${gt.msg}`,type:"danger"})})))).then(()=>{be()})}).catch(fe=>{M(w,{msg:`店铺${ie}的订单操作失败：${fe.msg}`,type:"danger"})}).finally(()=>{if(!a.autoSell){M(w,{msg:"检测到暂停指令,停止后续操作",type:"warning"}),f();return}Ie.length?U():f()})};const{shop_id:W,shop_name:ie}=D.shopInfo,Pe=x.tableList.find(Ce=>Ce.mallId==W);if(!Pe){M(w,{msg:ie+"未找到店铺信息",type:"warning"}),f();return}if(!x.checkAvailable(Pe)){M(w,{msg:ie+"店铺已过期",type:"warning"}),f();return}const Ie=D.orders;U()}}async function S(t=R.value){const e=t.filter(x=>x.order_status_prompt==="待分享");if(!e.length)return ee.warning({message:t.length?"请选择订单":"没有可以免拼的订单",grouping:!0});a.autoSpell=!0;const r="autoSpell";O.active=r,O.dialog=!0,M(r,{msg:"*".repeat(100)});const m=10;let f=0;function w(){const x=e.slice(f,f+m);f+=x.length;const y=x.map(D=>Ao({order_sn:D.order_sn},{showErrorMsg:!1}).then(U=>{M(r,{msg:`${D.order_sn}免拼成功`,type:"success"})}).catch(U=>{M(r,{msg:`${D.order_sn}免拼失败，${U.msg}`,type:"danger"})}));Promise.allSettled(y).then(()=>{a.autoSpell?f>=e.length?(Je(r),a.autoSpell=!1):w():M(r,{type:"warning",msg:"检测到暂停指令，已停止后续操作"})})}w()}async function j(t=R.value){const e=t.filter(r=>r.is_delete);if(!e.length)return ee.warning({message:t.length?"没有可恢复的订单":"请选择订单",grouping:!0});a.orderRecover=!0,await fl(e,{request:async r=>zo({order_sns:r.map(m=>m.order_sn).join(",")}),isStop:()=>!a.orderRecover}),be(),a.orderRecover=!1}const z=Le({dialog:!1,after_sales_type:1,user_ship_status:1,question_type:95,question_desc:"",user_phone:"",list:[]}),ve=_e();function Ve(){z.dialog=!0}async function Ee(t=R.value){const e=t.filter(r=>r.combined_order_status===v.payed||r.combined_order_status==v.pendingReceive||r.combined_order_status==v.pendingComment||r.combined_order_status==v.pendingShare);if(!e.length)return ee.warning({message:t.length?"选择的订单中没有可以退款的订单":"请选择需要退款的订单",grouping:!0});Ve(),z.user_phone=e[0].phone,z.question_desc="不想要了",z.list=e}async function ft(t=z.list){var W;await((W=ve.value)==null?void 0:W.validate()),a.isRefund=!0;const e="refund";M(e,{msg:"*".repeat(99)});const{after_sales_type:r,user_ship_status:m,question_desc:f,question_type:w,user_phone:x}=z;z.dialog=!1,O.dialog=!0,O.active=e;const y=10;let D=0;function U(){const ie=t.slice(D,D+y);D+=ie.length;const Pe=ie.map(Ie=>qo({order_sn:Ie.order_sn,after_sales_type:r,user_ship_status:m,question_desc:f,question_type:w,user_phone:x,apply_amount:Ie.order_amount/100},{showErrorMsg:!1}).then(Ce=>{M(e,{type:"success",msg:`${Ie.order_sn},退款成功`})}).catch(Ce=>{M(e,{type:"danger",msg:`${Ie.order_sn},退款失败,${Ce.msg}`})}).finally(()=>{}));Promise.allSettled(Pe).then(()=>{a.isRefund?D>=t.length?(Je(e),a.isRefund=!1):U():M(e,{type:"warning",msg:"检测到暂停指令，已停止后续操作"})})}U()}async function at(t=R.value){const e=[...t];a.cancelRefund=!0;const r="cancelRefund";M(r,{msg:"*".repeat(99)}),O.dialog=!0,O.active=r;const m=10;let f=0;function w(){const x=e.slice(f,f+m);f+=x.length;const y=x.map(D=>Fo({order_sn:D.order_sn},{showErrorMsg:!1}).then(U=>{M(r,{type:"success",msg:`${D.order_sn},取消退款成功`})}).catch(U=>{M(r,{type:"danger",msg:`${D.order_sn},取消退款失败,${U.msg}`})}).finally(()=>{}));Promise.allSettled(y).then(()=>{a.cancelRefund?f>=e.length?(Je(r),a.cancelRefund=!1):w():M(r,{type:"warning",msg:"检测到暂停指令，已停止后续操作"})})}w()}async function Je(t,e=500){M(t,{msg:"本次请求任务已结束"}),M(t,{msg:"*".repeat(100)}),await Ze(e),be()}async function Pt(t=R.value){if(a.isConfirm)return ee.warning({message:"当前有订单正在请求",grouping:!0});const e=t.filter(U=>U.combined_order_status===v.pendingReceive);if(!e.length)return ee.warning({message:t.length?"选择的订单中没有可以确认的订单":"请选择需要确认的订单",grouping:!0});await Qe({title:"提示",message:"确定对选中的订单做确认操作吗?",showCancelButton:!0}),a.isConfirm=!0;const r="confirm";M(r,{msg:"*".repeat(99)}),O.dialog=!0,O.active=r;const m=Dt();let f=300;const{active:w,min:x,max:y}=m.visit.confirm;w?f=kl(x,y)*1e3:f=300;const D=e.map((U,W)=>new Promise((ie,Pe)=>{const Ie=setTimeout(()=>{Eo({order_sn:U.order_sn},{showErrorMsg:!1}).then(Ce=>{ie(Ce),M(r,{type:"success",msg:`${U.order_sn},确认成功,还剩${i.requestTids.size-1}单`})}).catch(Ce=>{Pe(Ce),M(r,{type:"danger",msg:`${U.order_sn},确认失败,${Ce.msg}，还剩${i.requestTids.size-1}单`})}).finally(()=>{i.requestTids.delete(Ie)})},f*W);i.requestTids.add(Ie)}));Promise.allSettled(D).then(()=>{Je(r),a.isConfirm=!1})}function ze(){a.isConfirm=!1,i.requestTids.forEach(t=>{clearTimeout(t)}),i.requestTids.clear(),a.isCancel=!1,a.changePrice=!1,a.isDelete=!1,a.isRefund=!1,a.autoSpell=!1,a.autoSell=!1}function nt(t){if(t){switch(t){case"isConfirm":{i.requestTids.forEach(e=>{clearTimeout(e)}),i.requestTids.clear(),M("refund",{type:"warning",msg:"发出暂停指令,将取消后续请求"});break}}a[t]=!1}}function Ae(t){if(!t)return;const e=O.map.get(t);e&&(e.length=0)}const me=Le({show:!1,x:"0px",y:"0px",hover:[]});We(()=>me.show,t=>{t||(me.row=void 0)},{immediate:!0});function Ye(t){t.stopPropagation(),me.row=void 0;const{scale:e}=Dt().appWindow;let r=t.x/e,m=(t.y-70)/e;r>1200&&(r=1200),me.x=r+"px",me.y=m+"px",me.show=!0;const f=t.path,w=f==null?void 0:f.find(x=>{var y;return(y=x.classList)==null?void 0:y.contains("vxe-body--row")});w&&w.classList.forEach(x=>{const y=x.match(/^rowIndex-(\d+)$/);if(!y)return;const D=y[1],U=i.orderList[Number(D)];U&&(me.row=U)})}async function Te(t){const e=Dt(),r=me.row;if(r)switch(t){case"open-comFolder":{e.orderManage.commentFolder||(ee.warning({message:"检测到没有设定评论文件夹,请先选定文件夹",grouping:!0}),await Te("set-comFolder"));const m=e.orderManage.commentFolder+"/"+r.goods_id;await Po(m)||await Lo({path:m}),vt(m);break}case"set-comFolder":{await Qe({showCancelButton:!0,title:"预设评论文件夹",type:"warning",message:ge("div",{},[ge("p",{},"注意"),ge("p",{class:"danger"},"请不要选择C盘文件夹，可能导致权限不足无法创建"),ge("p",{class:"danger"},"请不要选择软件(.exe文件)所在目录的文件夹，否则更新时会被覆盖")]),confirmButtonText:"我已知晓"});const m=await Nt();return m?(e.orderManage.commentFolder=m,e.setOrderSetting("orderManage",{...e.orderManage}),ee.success("已成功设置评价文件夹"),m):Promise.reject()}}}const Fe=({row:t,rowIndex:e})=>{var m;let r=`rowIndex-${e}`;return((m=me.row)==null?void 0:m.order_sn)===t.order_sn&&(r+=" row-contextmenu-active"),r},ke=Le({dialog:!1,remark:"",images:[]});async function Re(){if(ke.images.length>6){ee.warning("最多只能上传6张图片");return}(await $l({title:"选择图片",properties:["openFile","multiSelections"],filters:[{name:"Images",extensions:["jpg","jpeg","png"]}]})).data.some(r=>(ke.images.length<6&&ke.images.push(r),ke.images.length>=6))}async function Be(){const{images:t}=ke;console.log(R.value);const e=R.value.filter(f=>f.after_sales_id);if(e.length===0){ee.warning("没有可申请的订单");return}const r=await Promise.all(t.map(f=>Sl(f,!0,50))),m="intervene";a.intervene=!0,ke.dialog=!1,await fl(e,{batch:10,isStop:()=>!a.intervene,request:async f=>{await Promise.allSettled(f.map(async w=>{const{account:x,order_sn:y}=w,D=[];if(await fl(r,{batch:1,request:async W=>{await Promise.allSettled(W.map(async ie=>{const Ie=(await Bo({account:x})).data.signature,Ce=await Ho({image:ie,upload_sign:Ie});D.push(Ce.url)}))}}),D.length!==r.length)return M(m,{msg:y+"有图片上传失败,不会申请",type:"danger"}),Promise.reject("有图片上传失败");const U={order_sn:y,images:JSON.stringify(D),remark:ke.remark};return Ko(U,{showErrorMsg:!1}).then(W=>{M(m,{msg:y+"申请介入成功",type:"success"})}).catch(W=>{M(m,{msg:y+"申请介入失败"+W.msg,type:"danger"})})})),await zt({ids:e.map(w=>w.order_sn).join(",")})}}),be(),a.intervene=!1}return(t,e)=>{const r=jo,m=dl,f=ho,w=sn,x=an,y=jt,D=Ot,U=il,W=vo,ie=pn,Pe=Rl,Ie=un,Ce=Yt,fe=Ne("vxe-column"),ct=Ne("vxe-table"),st=Ne("ds-pagination"),pt=fn,gt=Ra,Lt=mn,ul=al,cl=Cl,xt=ln,kt=Dl,vn=cn,Ge=nl("blur");return $(),B("div",{class:"order-manage no-padding",onClick:e[100]||(e[100]=()=>{me.show=!1})},[l(js,{ref_key:"exportOrderEl",ref:F,columns:X},null,512),l(W,{size:"default"},{default:o(()=>[l(U,{class:"search-form",inline:!0},{default:o(()=>[l(m,{label:"时间："},{default:o(()=>[l(r,{modelValue:g.time,"onUpdate:modelValue":e[0]||(e[0]=s=>g.time=s),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","value-format":"x","end-placeholder":"结束时间",shortcuts:V(Wo),"default-time":[new Date(V(h).year,V(h).month,V(h).day,0,0,0),new Date(V(h).year,V(h).month,V(h).day,23,59,59)],"disabled-date":s=>s.getTime()<Date.now()-1e3*60*60*24*30,"popper-class":"date-range-select ordermanage-timerange-select-popper"},null,8,["modelValue","shortcuts","default-time","disabled-date"])]),_:1}),l(f,{direction:"vertical"}),l(m,{label:"下单类型："},{default:o(()=>[l(x,{modelValue:g.type,"onUpdate:modelValue":e[1]||(e[1]=s=>g.type=s),"popper-class":"ordermanage-ordertype-select-popper"},{default:o(()=>[l(w,{label:"全部",value:""}),($(!0),B(De,null,Oe(V(Jt),s=>($(),B(De,null,[s.disabled?He("",!0):($(),ne(w,{key:0,value:s.value,label:s.label},{default:o(()=>[c(I(s.label),1)]),_:2},1032,["value","label"]))],64))),256))]),_:1},8,["modelValue"])]),_:1}),l(f,{direction:"vertical"}),l(m,null,{default:o(()=>[l(D,{placeholder:"请输入",modelValue:g.commonSearch.value,"onUpdate:modelValue":e[4]||(e[4]=s=>g.commonSearch.value=s),onKeyup:e[5]||(e[5]=Ht(s=>oe(g.statusList.find(pe=>pe.value===g.status)),["enter"])),onClick:e[6]||(e[6]=()=>{V(_o)().then(s=>{g.commonSearch.value=s})})},{prepend:o(()=>[l(x,{modelValue:g.commonSearch.key,"onUpdate:modelValue":e[2]||(e[2]=s=>g.commonSearch.key=s),placeholder:"选择搜索类型",style:{width:"85px"},"popper-class":"ordermanage-searchkey-select-popper"},{default:o(()=>[($(!0),B(De,null,Oe(g.commonSearch.list,s=>($(),ne(w,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),append:o(()=>[l(y,{onClick:e[3]||(e[3]=s=>be()),disabled:a.getList},{default:o(()=>[c("搜索")]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),p("div",Ws,[l(Pe,{size:"default",modelValue:g.status,"onUpdate:modelValue":e[7]||(e[7]=s=>g.status=s),disabled:a.getList},{default:o(()=>[($(!0),B(De,null,Oe(g.statusList,s=>($(),ne(ie,{onClick:Rt(pe=>oe(s),["prevent"]),key:s.value,label:s.value},{default:o(()=>[c(I(`${s.label}`),1)]),_:2},1032,["onClick","label"]))),128))]),_:1},8,["modelValue","disabled"]),p("div",Gs,[ce(($(),ne(y,{size:"default",type:"primary",loading:a.isOrderSync,disabled:a.isOrderSync,onClick:e[8]||(e[8]=s=>Se())},{default:o(()=>[c("同步订单状态")]),_:1},8,["loading","disabled"])),[[Ge]])])]),l(Ce,{title:"正在同步最新订单状态...","append-to-body":!0,modelValue:se.progressShow,"onUpdate:modelValue":e[9]||(e[9]=s=>se.progressShow=s),width:480,class:"dialog-480 order-sync-dialog",top:"300px"},{default:o(()=>[l(Ie,{percentage:V($e)},null,8,["percentage"])]),_:1},8,["modelValue"]),p("div",Js,[l(ct,{ref_key:"tableRef",ref:E,border:!0,stripe:"",onContextmenu:Ye,"row-class-name":Fe,loading:a.getList,"checkbox-config":{checkField:"checkField"},height:540,"row-config":{height:50,keyField:"order_sn"},"scroll-y":{enabled:!0},"column-config":{resizable:!0},data:i.orderList,onCheckboxAll:e[10]||(e[10]=()=>{C()}),onCheckboxChange:e[11]||(e[11]=()=>{C()}),"show-header-overflow":"tooltip","show-overflow":"tooltip"},{default:o(()=>[l(fe,{type:"checkbox",width:40,fixed:"left",resizable:!1}),l(fe,{field:"sort",width:50,fixed:"left",title:"序号",resizable:!1},{default:o(s=>[c(I(s._rowIndex+u.limit*(u.page-1)+1),1)]),_:1}),l(fe,{width:120,field:"shop_name",title:"所属店铺"},{default:o(s=>[l(V(je),{underline:!1,onClick:pe=>V(Xe)(s.row.shop_name)},{default:o(()=>[c(I(s.row.shop_name),1)]),_:2},1032,["onClick"])]),_:1}),l(fe,{width:140,field:"add_time",title:"下单时间",sortable:!0},{default:o(s=>[c(I(V(dt)(s.row.add_time*1e3).format("YYYY-MM-DD HH:mm:ss")),1)]),_:1}),l(fe,{width:100,field:"type",title:"下单类型",sortable:!0},{default:o(s=>{var pe;return[c(I(((pe=V(Jt).find(rt=>rt.value==s.row.type))==null?void 0:pe.label)||""),1)]}),_:1}),l(fe,{width:70,field:"order_status",title:"订单状态"},{default:o(s=>[c(I(s.row.order_status_prompt),1)]),_:1}),l(fe,{width:170,field:"order_sn",title:"订单编号"},{default:o(s=>[l(V(je),{underline:!1,onClick:pe=>V(Xe)(s.row.order_sn)},{default:o(()=>[c(I(s.row.order_sn),1)]),_:2},1032,["onClick"])]),_:1}),l(fe,{width:70,field:"order_amount",title:"价格",sortable:!0},{default:o(s=>[c(I(Number(s.row.order_amount)/100),1)]),_:1}),l(fe,{width:70,field:"goods_number",title:"购买数量"}),l(fe,{width:110,field:"goods_id",title:"商品ID",sortable:!0},{default:o(s=>[l(V(je),{underline:!1,onClick:pe=>V(Xe)(s.row.goods_id)},{default:o(()=>[c(I(s.row.goods_id),1)]),_:2},1032,["onClick"])]),_:1}),l(fe,{width:110,field:"account",title:"小号"},{default:o(s=>[l(V(je),{underline:!1,onClick:pe=>V(Xe)(s.row.account)},{default:o(()=>[c(I(s.row.account),1)]),_:2},1032,["onClick"])]),_:1}),l(fe,{width:200,field:"goods_name",title:"商品名称"},{default:o(s=>[c(I(s.row.goods_name),1)]),_:1}),l(fe,{width:200,field:"sku_spec",title:"购买规格",sortable:!0},{default:o(s=>[l(V(je),{underline:!1,onClick:pe=>V(Xe)(s.row.sku_spec)},{default:o(()=>[c(I(s.row.sku_spec),1)]),_:2},1032,["onClick"])]),_:1}),l(fe,{width:200,field:"recipient",title:"收货信息"},{default:o(({row:s})=>[c(I(s.province+s.city+s.district+s.address),1)]),_:1}),l(fe,{width:100,field:"name",title:"收件人"},{default:o(({row:s})=>[c(I(s.name),1)]),_:1}),l(fe,{width:100,field:"phone",title:"手机号"},{default:o(({row:s})=>[c(I(s.phone),1)]),_:1}),l(fe,{width:120,field:"express",title:"快递公司"},{default:o(({row:s})=>[c(I(s.express),1)]),_:1}),l(fe,{width:120,field:"tracking_number",title:"快递单号"},{default:o(({row:s})=>[l(V(je),{underline:!1,onClick:pe=>V(Xe)(s.tracking_number)},{default:o(()=>[c(I(s.tracking_number),1)]),_:2},1032,["onClick"])]),_:1}),l(fe,{width:130,field:"logistics",title:"物流信息"},{default:o(({row:s})=>[c(I(s.logistics),1)]),_:1}),l(fe,{width:100,field:"is_collect",title:"店铺收藏"},{default:o(({row:s})=>[c(I(s.is_collect?"是":"否"),1)]),_:1}),l(fe,{width:100,field:"is_attention",title:"店铺关注"},{default:o(({row:s})=>[c(I(s.is_attention?"是":"否"),1)]),_:1})]),_:1},8,["loading","data"]),ce(p("div",Zs,[me.row?($(),B(De,{key:0},[p("div",Qs,[p("p",{onClick:e[12]||(e[12]=s=>J([me.row]))},"手工评价"),p("p",{onClick:e[13]||(e[13]=s=>J([me.row]))},"追加评价"),p("p",{onClick:e[14]||(e[14]=s=>Me("chat_dialog",me.row))},"与此商聊天"),p("p",{onClick:e[15]||(e[15]=s=>Me("change_addr_dialog",me.row))},"修改地址")]),p("div",Xs,[p("p",{onClick:e[16]||(e[16]=s=>V(Xe)(me.row.goods_id))},"复制宝贝ID"),p("p",{onClick:e[17]||(e[17]=s=>V(Xe)(me.row.shop_name))},"复制店铺名"),p("p",{onClick:e[18]||(e[18]=s=>V(Xe)(me.row.order_sn))},"复制订单号"),ce(p("p",{onClick:e[19]||(e[19]=()=>V(Xe)(V(R).map(s=>s.order_sn).join(`
`)))}," 复制选中订单号",512),[[ot,V(R).length]]),p("p",{onClick:e[20]||(e[20]=()=>V(Xe)(i.orderList.map(s=>s.order_sn).join(`
`)))},"复制当前页订单号")]),p("div",er,[tr,p("p",{onClick:e[21]||(e[21]=s=>Te("open-comFolder"))},"打开评价文件夹"),p("p",{onClick:e[22]||(e[22]=s=>Te("set-comFolder"))},"设定总文件夹")])],64)):($(),B("div",lr,[p("p",{onClick:e[23]||(e[23]=s=>be())},"刷新列表")]))],512),[[ot,me.show]]),p("footer",nr,[p("div",or,[l(V(yt),{label:"全选订单",modelValue:i.checkAll,"onUpdate:modelValue":e[24]||(e[24]=s=>i.checkAll=s),indeterminate:i.indeterminate,onChange:e[25]||(e[25]=s=>{Y(s?"check-all":"remove-all")})},null,8,["modelValue","indeterminate"]),l(f,{direction:"vertical"}),l(V(je),{underline:!1,onClick:e[26]||(e[26]=s=>Y("reverse"))},{default:o(()=>[c("反选订单")]),_:1}),l(f,{direction:"vertical"}),l(V(je),{underline:!1,onClick:e[27]||(e[27]=s=>Y("area-select"))},{default:o(()=>[c("区域选择")]),_:1}),l(f,{direction:"vertical"}),l(V(je),{underline:!1,onClick:e[28]||(e[28]=s=>Y("remove-all"))},{default:o(()=>[c("取消选择")]),_:1}),l(f,{direction:"vertical"}),l(V(je),{underline:!1,onClick:e[29]||(e[29]=s=>ye("Statistics"))},{default:o(()=>[c("统计金额")]),_:1})]),l(st,{"current-page":u.page,"onUpdate:current-page":e[30]||(e[30]=s=>u.page=s),"page-size":u.limit,"onUpdate:page-size":e[31]||(e[31]=s=>u.limit=s),"page-sizes":[50,500,1e3,2e3],total:u.total,small:!0,onCurrentChange:be,onSizeChange:be},null,8,["current-page","page-size","total"])])]),l(Ce,{width:480,title:"与此商家聊天",modelValue:K.chat_dialog,"onUpdate:modelValue":e[36]||(e[36]=s=>K.chat_dialog=s),class:"dialog-480"},{footer:o(()=>[l(y,{onClick:e[35]||(e[35]=s=>K.chat_dialog=!1)},{default:o(()=>[c("取消")]),_:1}),l(y,{type:"primary",onClick:mt,loading:a.chatWidthShop,disabled:a.chatWidthShop},{default:o(()=>[c("确定")]),_:1},8,["loading","disabled"])]),default:o(()=>[l(U,{"label-width":"80"},{default:o(()=>[l(m,{label:"开启假聊："},{default:o(()=>[l(Pe,{modelValue:K.chat_type,"onUpdate:modelValue":e[32]||(e[32]=s=>K.chat_type=s)},{default:o(()=>[l(ie,{label:"random"},{default:o(()=>[c("随机发送")]),_:1}),l(ie,{label:"order"},{default:o(()=>[c("固定发送")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"发送数量："},{default:o(()=>[l(pt,{modelValue:K.chat_num,"onUpdate:modelValue":e[33]||(e[33]=s=>K.chat_num=s),controls:!1,precision:0,min:1},null,8,["modelValue"]),ar]),_:1}),l(D,{type:"textarea",rows:5,placeholder:"换行隔开",modelValue:K.chat_content,"onUpdate:modelValue":e[34]||(e[34]=s=>K.chat_content=s)},null,8,["modelValue"]),sr]),_:1})]),_:1},8,["modelValue"]),l(Ce,{width:480,title:"修改地址",modelValue:K.change_addr_dialog,"onUpdate:modelValue":e[42]||(e[42]=s=>K.change_addr_dialog=s),class:"change-addr-dialog dialog-480"},{footer:o(()=>[l(y,{onClick:e[41]||(e[41]=s=>K.change_addr_dialog=!1)},{default:o(()=>[c("取消")]),_:1}),l(y,{type:"primary",onClick:bt,loading:a.changeAddr,disabled:a.changeAddr},{default:o(()=>[c("确定")]),_:1},8,["loading","disabled"])]),default:o(()=>[l(U,{"label-width":"80",model:K.change_addr,ref_key:"changeAddrForm",ref:qe},{default:o(()=>[l(m,{label:"收件人",prop:"name",rules:[{required:!0,message:"请输入收件人"}]},{default:o(()=>{var s;return[l(D,{modelValue:K.change_addr.name,"onUpdate:modelValue":e[37]||(e[37]=pe=>K.change_addr.name=pe),placeholder:`收货人(原：${(s=K.row)==null?void 0:s.name})`},null,8,["modelValue","placeholder"])]}),_:1}),l(m,{label:"手机号",prop:"phone",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[^0-2]\d{9}$/,message:"请输入合法的手机号"}]},{default:o(()=>{var s;return[l(D,{modelValue:K.change_addr.phone,"onUpdate:modelValue":e[38]||(e[38]=pe=>K.change_addr.phone=pe),placeholder:`手机号(原：${(s=K.row)==null?void 0:s.phone})`},null,8,["modelValue","placeholder"])]}),_:1}),l(m,{label:"省市区",prop:"p_c_d",rules:[{required:!0,message:"请选择省市区"},{validator:(s,pe,rt)=>{pe.filter(Wt=>Wt).length>=3?rt():rt("请选择正确的省市区")}}]},{default:o(()=>[l(gt,{ref_key:"addressCascader",ref:Ke,style:{"flex-grow":"1"},modelValue:K.change_addr.p_c_d,"onUpdate:modelValue":e[39]||(e[39]=s=>K.change_addr.p_c_d=s),props:re,options:K.change_addr.options},null,8,["modelValue","options"])]),_:1},8,["rules"]),l(m,{label:"详细地址",prop:"address",rules:[{required:!0,message:"请输入详细地址"}]},{default:o(()=>[l(D,{placeholder:"具体地址",modelValue:K.change_addr.address,"onUpdate:modelValue":e[40]||(e[40]=s=>K.change_addr.address=s)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(W,{size:"default"},{default:o(()=>[ce(p("footer",rr,[p("p",null,[p("span",null,[c("已选中 "),p("strong",null,I(q.selectionMap.size),1),c(" 项")]),p("div",ir,[ce(($(),ne(y,{type:"success",onClick:e[43]||(e[43]=s=>O.dialog=!0)},{default:o(()=>[c("操作日志")]),_:1})),[[Ge]]),ce(($(),ne(y,{onClick:e[44]||(e[44]=s=>Q())},{default:o(()=>[c("导出订单")]),_:1})),[[Ge]]),ce(($(),ne(y,{onClick:e[45]||(e[45]=s=>L()),loading:a.exportCard,disabled:a.exportCard},{default:o(()=>[c("导出卡券")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{onClick:e[46]||(e[46]=s=>d()),loading:a.exportPayCode,disabled:a.exportPayCode},{default:o(()=>[c("导出二维码")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{onClick:e[47]||(e[47]=s=>k()),loading:a.autoSell,disabled:a.autoSell},{default:o(()=>[c("券自动核销")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{onClick:e[48]||(e[48]=s=>S()),loading:a.autoSpell,disabled:a.autoSpell},{default:o(()=>[c("一键免拼")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{onClick:e[49]||(e[49]=s=>lt()),loading:a.changePrice,disabled:a.changePrice},{default:o(()=>[c("一键改价格 ")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{loading:a.isRemark,disabled:a.isRemark,onClick:e[50]||(e[50]=s=>Z.dialog=!0)},{default:o(()=>[c("订单备注")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{loading:a.isDelete,disabled:a.isDelete,type:"danger",plain:"",onClick:e[51]||(e[51]=s=>we())},{default:o(()=>[c("删除订单")]),_:1},8,["loading","disabled"])),[[Ge]])])]),p("p",dr,[l(V(Mt),null,{default:o(()=>[p("div",ur,[l(y,{type:"success",onClick:e[52]||(e[52]=s=>_t("wechat")),loading:a.apply_wechat,disabled:a.apply_wechat},{default:o(()=>[c(" 微信支付 ")]),_:1},8,["loading","disabled"]),l(f,{direction:"vertical"}),l(y,{type:"success",onClick:e[53]||(e[53]=s=>_t("code")),loading:a.apply_code,disabled:a.apply_code},{default:o(()=>[c(" 扫码支付 ")]),_:1},8,["loading","disabled"]),l(f,{direction:"vertical"}),l(y,{type:"success",onClick:e[54]||(e[54]=s=>_t("auto")),loading:a.apply_auto,disabled:a.apply_auto},{default:o(()=>[c(" 自动支付 ")]),_:1},8,["loading","disabled"]),l(f,{direction:"vertical"}),l(y,{type:"success",onClick:Vt},{default:o(()=>[c("停止自动支付")]),_:1})]),p("div",cr,[l(y,{type:"primary",onClick:e[55]||(e[55]=s=>Pt()),loading:a.isConfirm,disabled:a.isConfirm},{default:o(()=>[c("确认收货")]),_:1},8,["loading","disabled"]),l(f,{direction:"vertical"}),l(y,{type:"primary",onClick:e[56]||(e[56]=s=>J())},{default:o(()=>[c("评价与采集")]),_:1}),l(f,{direction:"vertical"}),l(y,{type:"primary",onClick:e[57]||(e[57]=s=>J())},{default:o(()=>[c("追加评价")]),_:1})]),ce(($(),ne(y,{loading:a.isCancel,disabled:a.isCancel,onClick:e[58]||(e[58]=s=>le())},{default:o(()=>[c("取消订单")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{loading:a.isRefund,disabled:a.isRefund,onClick:e[59]||(e[59]=s=>Ee())},{default:o(()=>[c("订单退款")]),_:1},8,["loading","disabled"])),[[Ge]]),ce(($(),ne(y,{loading:a.cancelRefund,disabled:a.cancelRefund,onClick:e[60]||(e[60]=s=>at())},{default:o(()=>[c("取消退款")]),_:1},8,["loading","disabled"])),[[Ge]])]),_:1})])],512),[[ot,g.status!==99]]),ce(p("footer",pr,[p("p",null,[p("span",null,[c("已选中 "),p("strong",null,I(q.selectionMap.size),1),c(" 项")]),p("div",mr,[ce(($(),ne(y,{type:"success",onClick:e[61]||(e[61]=s=>O.dialog=!0)},{default:o(()=>[c("操作日志")]),_:1})),[[Ge]]),ce(($(),ne(y,{type:"success",disabled:a.orderRecover,loading:a.orderRecover,onClick:e[62]||(e[62]=s=>j())},{default:o(()=>[c("恢复订单")]),_:1},8,["disabled","loading"])),[[Ge]])])]),fr],512),[[ot,g.status===99]])]),_:1}),l(Ce,{modelValue:de.dialog,"onUpdate:modelValue":e[68]||(e[68]=s=>de.dialog=s),title:"修改订单价格",top:"70px",width:1028,class:"change-price-dialog","append-to-body":!0,center:""},{footer:o(()=>[l(y,{onClick:e[66]||(e[66]=s=>de.dialog=!1)},{default:o(()=>[c("取消")]),_:1}),l(y,{onClick:e[67]||(e[67]=s=>T()),disabled:!de.checkedSet.size},{default:o(()=>[c("提交")]),_:1},8,["disabled"])]),default:o(()=>[l(U,{"label-position":"top",inline:!0},{default:o(()=>[l(m,{label:"修改方式"},{default:o(()=>[l(Pe,{modelValue:de.type,"onUpdate:modelValue":e[63]||(e[63]=s=>de.type=s),size:"default"},{default:o(()=>[l(ie,{label:"price"},{default:o(()=>[c("按目标价格修改")]),_:1}),l(ie,{label:"cut"},{default:o(()=>[c("按折扣修改")]),_:1})]),_:1},8,["modelValue"])]),_:1}),ce(l(m,{label:"修改目标价格"},{default:o(()=>[l(pt,{size:"default",precision:2,step:.1,min:.01,modelValue:de.price,"onUpdate:modelValue":e[64]||(e[64]=s=>de.price=s)},null,8,["step","min","modelValue"])]),_:1},512),[[ot,de.type==="price"]]),ce(l(m,{label:"修改目标折扣"},{default:o(()=>[l(pt,{size:"default",precision:1,step:.1,min:.1,max:10,modelValue:de.cut,"onUpdate:modelValue":e[65]||(e[65]=s=>de.cut=s)},null,8,["step","min","modelValue"])]),_:1},512),[[ot,de.type==="cut"]])]),_:1}),l(Lt,{type:"success"},{default:o(()=>[c(" 您可以从下面店铺中选择一个或多个店铺进行改价操作。 "),gr]),_:1}),l(ul,{height:"500"},{default:o(()=>[($(!0),B(De,null,Oe(de.map,([s,pe])=>{var rt;return $(),B("div",{class:xe(["box",{is_checked:de.checkedSet.has(s)}])},[p("h4",{onClick:()=>{de.checkedSet.has(s)?de.checkedSet.delete(s):de.checkedSet.add(s)}},[p("span",_r,"店铺ID："+I(s),1),p("span",vr,"店铺名称："+I((rt=pe[0])==null?void 0:rt.shop_name),1),p("span",yr,"改价订单数量："+I(pe.length),1)],8,hr)],2)}),256))]),_:1})]),_:1},8,["modelValue"]),l(Ce,{"append-to-body":!0,modelValue:z.dialog,"onUpdate:modelValue":e[76]||(e[76]=s=>z.dialog=s),title:"申请退款",width:480,class:"dialog-480 refund-dialog"},{footer:o(()=>[l(y,{onClick:e[74]||(e[74]=s=>z.dialog=!1)},{default:o(()=>[c("取消")]),_:1}),ce(($(),ne(y,{type:"primary",onClick:e[75]||(e[75]=s=>ft())},{default:o(()=>[c("确定")]),_:1})),[[Ge]])]),default:o(()=>[l(U,{"label-position":"top",model:z,ref_key:"refundFormEl",ref:ve},{default:o(()=>[l(m,{label:"申请类型",prop:"after_sales_type",rules:[{required:!0,message:"请选择申请类型"}]},{default:o(()=>[l(x,{modelValue:z.after_sales_type,"onUpdate:modelValue":e[69]||(e[69]=s=>z.after_sales_type=s)},{default:o(()=>[l(w,{label:"仅退款",value:1}),l(w,{label:"退款退货",value:2}),l(w,{label:"换货",value:3})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"收货状态",prop:"user_ship_status",rules:[{required:!0,message:"请选择收货状态"}]},{default:o(()=>[l(x,{modelValue:z.user_ship_status,"onUpdate:modelValue":e[70]||(e[70]=s=>z.user_ship_status=s)},{default:o(()=>[l(w,{label:"未收到货",value:1}),l(w,{label:"已收到货",value:2})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"手机号码",prop:"user_phone",rules:[{required:!0,message:"请输入手机账号"},{pattern:/^1[^0-2]\d{9}$/,message:"请输入合法手机号"}]},{default:o(()=>[l(D,{modelValue:z.user_phone,"onUpdate:modelValue":e[71]||(e[71]=s=>z.user_phone=s)},null,8,["modelValue"])]),_:1}),l(m,{label:"申请原因",prop:"question_type",rules:[{required:!0,message:"请选择申请原因"}]},{default:o(()=>[l(x,{modelValue:z.question_type,"onUpdate:modelValue":e[72]||(e[72]=s=>z.question_type=s)},{default:o(()=>[l(w,{label:"质量问题",value:81}),l(w,{label:"不喜欢、效果不好",value:87}),l(w,{label:"不想要了",value:88}),l(w,{label:"货物与描述不符",value:288}),l(w,{label:"缺货",value:290}),l(w,{label:"其他原因",value:95})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"退款原因",prop:"question_desc",rules:[{required:!0,message:"请输入退款原因"}]},{default:o(()=>[l(D,{type:"textarea",modelValue:z.question_desc,"onUpdate:modelValue":e[73]||(e[73]=s=>z.question_desc=s),rows:5,resize:"none"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(Ce,{modelValue:i.isChildShow,"onUpdate:modelValue":e[79]||(e[79]=s=>i.isChildShow=s),"append-to-body":!1,"show-close":!1,class:"sub-operate",width:1028,"close-on-press-escape":!1,"close-on-click-modal":!1,top:"65px",modal:!1,draggable:!1},{default:o(()=>[p("div",br,[p("h3",null,[wr,p("div",{class:"close",onClick:e[77]||(e[77]=s=>i.isChildShow=!1)},[l(cl,{href:"icon-close"})])]),p("div",kr,[l(zs,{ref_key:"EVALandCOLLRef",ref:G,"order-list":i.commentOrderList,onFinishComment:e[78]||(e[78]=s=>be())},null,8,["order-list"])])])]),_:1},8,["modelValue"]),l(Ce,{modelValue:O.dialog,"onUpdate:modelValue":e[83]||(e[83]=s=>O.dialog=s),title:"订单操作日志",top:"60px","append-to-body":!0,class:"log-dialog",width:1028},{default:o(()=>{var s;return[p("header",null,[l(Pe,{size:"default",modelValue:O.active,"onUpdate:modelValue":e[80]||(e[80]=pe=>O.active=pe)},{default:o(()=>[($(!0),B(De,null,Oe(O.list,pe=>($(),ne(ie,{key:pe.value,label:pe.value},{default:o(()=>[c(I(pe.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),p("div",Cr,[l(y,{type:"danger",plain:"",size:"default",disabled:!((s=O.map.get(O.active))!=null&&s.length),onClick:e[81]||(e[81]=pe=>Ae(O.active))},{default:o(()=>[c("清除当前日志")]),_:1},8,["disabled"]),l(y,{type:"danger",size:"default",plain:"",disabled:!a[V(N).buttonLoading],onClick:e[82]||(e[82]=pe=>nt(V(N).buttonLoading))},{default:o(()=>[c("停止"+I(V(N).label),1)]),_:1},8,["disabled"]),l(y,{type:"danger",size:"default",onClick:ze},{default:o(()=>[c("停止全部")]),_:1})]),l(ul,{ref_key:"logScrollEl",ref:ue},{default:o(()=>[p("div",$r,[($(!0),B(De,null,Oe(O.map.get(O.active),pe=>($(),B("p",{class:xe(pe.type)},[p("span",Sr,I(pe.time),1),p("span",Vr,I(pe.msg),1)],2))),256))])]),_:1},512)]}),_:1},8,["modelValue"]),l(Ce,{modelValue:te.dialog,"onUpdate:modelValue":e[89]||(e[89]=s=>te.dialog=s),title:"支付",width:480,class:"dialog-480 apply","close-on-click-modal":!1,"close-on-press-escape":!1,"before-close":async s=>{await V(Qe)({title:"提示",type:"warning",message:"确定关闭吗?",showCancelButton:!0}),s()}},{default:o(()=>{var s,pe,rt;return[p("div",{class:"pay-container",onKeyup:[e[87]||(e[87]=Ht(()=>{te.index=(te.codePaylist.length+te.index+1)%te.codePaylist.length},["right"])),e[88]||(e[88]=Ht(()=>{te.index=(te.codePaylist.length+te.index-1)%te.codePaylist.length},["left"]))]},[p("h3",null,[p("span",{class:xe(V(Ue).type==="wechat"?"success":"primary")},I(V(Ue).type==="wechat"?"微信":"支付宝"),3),c(" 扫码 ("+I(te.index+1)+"/"+I(te.codePaylist.length)+") ",1),ce(l(xt,{type:"success"},{default:o(()=>[c("已支付")]),_:1},512),[[ot,V(Ue).pay]]),ce(l(xt,{type:"info"},{default:o(()=>[c("待支付")]),_:1},512),[[ot,!V(Ue).pay]])]),p("p",null," 当前已支付订单("+I(te.codePaylist.filter(Wt=>Wt.pay).length)+"/"+I(te.codePaylist.length)+") ",1),p("div",Er,[V(Ue).payInfo?V(Ue).payInfo.code?($(),B("p",xr,[Dr,Rr,p("span",null,I((s=V(Ue).payInfo)==null?void 0:s.msg),1)])):($(),B("img",{key:1,src:V(Ue).payInfo.imgBase64},null,8,Lr)):($(),B("p",Pr," 等待获取支付链接 "))]),p("p",null,[c("订单金额："),p("span",Nr,I(((pe=V(Ue))==null?void 0:pe.order.order_amount)/100),1)]),p("p",null,[c("订单编号："+I((rt=V(Ue))==null?void 0:rt.order.order_sn)+" ",1),l(V(It),{onClick:e[84]||(e[84]=Wt=>{var Il;return V(Xe)((Il=V(Ue))==null?void 0:Il.order.order_sn)}),class:"primary"},{default:o(()=>[l(V(yo))]),_:1})]),p("p",Ir,[l(y,{size:"large",onClick:e[85]||(e[85]=()=>{te.index=(te.codePaylist.length+te.index-1)%te.codePaylist.length})},{default:o(()=>[c("上一单")]),_:1}),l(y,{size:"large",type:"success",onClick:e[86]||(e[86]=()=>{te.index=(te.codePaylist.length+te.index+1)%te.codePaylist.length})},{default:o(()=>[c("下一单")]),_:1})])],32)]}),_:1},8,["modelValue","before-close"]),l(Ce,{modelValue:Z.dialog,"onUpdate:modelValue":e[94]||(e[94]=s=>Z.dialog=s),title:"订单备注(可在商家后台看到 )",width:480,class:"dialog-480"},{footer:o(()=>[l(y,{onClick:e[92]||(e[92]=s=>Z.dialog=!1)},{default:o(()=>[c("取消")]),_:1}),l(y,{onClick:e[93]||(e[93]=s=>he()),type:"primary"},{default:o(()=>[c("确定")]),_:1})]),default:o(()=>[l(U,{"label-position":"top"},{default:o(()=>[l(m,{label:"旗帜颜色",prop:"tag"},{default:o(()=>[l(Pe,{modelValue:Z.tag,"onUpdate:modelValue":e[90]||(e[90]=s=>Z.tag=s)},{default:o(()=>[l(kt,{label:""},{default:o(()=>[Mr]),_:1}),($(!0),B(De,null,Oe(Z.tagList,s=>($(),ne(kt,{label:s.value},{default:o(()=>[p("span",{style:nn({color:s.color||s.value})},I(s.label),5)]),_:2},1032,["label"]))),256))]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"备注"},{default:o(()=>[l(D,{type:"textarea",resize:"none",rows:10,modelValue:Z.remark,"onUpdate:modelValue":e[91]||(e[91]=s=>Z.remark=s),placeholder:"仅平台客服和商家可见，需在店铺管理受录店铺才能备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(Ce,{modelValue:ke.dialog,"onUpdate:modelValue":e[99]||(e[99]=s=>ke.dialog=s),title:"平台介入",width:480,class:"dialog-480"},{footer:o(()=>[l(y,{onClick:e[97]||(e[97]=s=>ke.dialog=!1)},{default:o(()=>[c("取消")]),_:1}),l(y,{type:"primary",onClick:e[98]||(e[98]=s=>Be()),loading:a.intervene},{default:o(()=>[c("确定")]),_:1},8,["loading"])]),default:o(()=>[l(U,{"label-position":"top"},{default:o(()=>[l(m,{label:"图片(只处理前6张)"},{default:o(()=>[p("div",null,[p("div",Tr,[($(!0),B(De,null,Oe(ke.images.slice(0,6),(s,pe)=>($(),B("div",{class:"img-box",key:s},[l(vn,{src:V(bo)(s),style:{width:"60px",height:"60px","margin-right":"5px"}},null,8,["src"]),p("p",null,[l(V(je),{underline:!1,type:"danger",onClick:rt=>ke.images.splice(pe,1)},{default:o(()=>[c("删除")]),_:2},1032,["onClick"])])]))),128))]),p("p",null,[l(y,{type:"danger",onClick:e[95]||(e[95]=s=>ke.images=[])},{default:o(()=>[c("清空")]),_:1}),ke.images.length<6?($(),ne(y,{key:0,onClick:Re},{default:o(()=>[c("添加")]),_:1})):He("",!0)])])]),_:1}),l(m,{label:"备注"},{default:o(()=>[l(D,{type:"textarea",resize:"none",rows:4,modelValue:ke.remark,"onUpdate:modelValue":e[96]||(e[96]=s=>ke.remark=s)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),ei=xl(Or,[["__scopeId","data-v-3b718dfa"]]);export{ei as default};
