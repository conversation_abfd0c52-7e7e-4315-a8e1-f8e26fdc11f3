import{d2 as a,ak as n,d3 as l}from"./index-CmETSh6Y.js";const h=a("currentGoods",{state(){return{infos:null}},getters:{goods(){var t;return(t=this.infos)==null?void 0:t.store.initDataObj.goods},mall(){var t;return(t=this.infos)==null?void 0:t.store.initDataObj.mall},shelfTime(){var e,o;const t=/\/(20\d{2}-\d{1,2}-\d{1,2})\//,s=(o=(e=this.infos)==null?void 0:e.store.initDataObj)==null?void 0:o.goods.topGallery;if(s&&typeof s=="object"){let r=[];if(s.forEach(u=>{const i=u.url.match(t);i&&r.push(i[1])}),r.sort(),r.length)return r[0]}return"-"},dsr(){var t,s,e;return(e=(s=(t=this.infos)==null?void 0:t.store.initDataObj)==null?void 0:s.mall)==null?void 0:e.dsr},isErrorPage(){if(!this.infos)return!1;const t=this.infos.store.initDataObj.goods.status,s=this.infos.store.initDataObj.goods.statusExplain;return!!(t==5||s.includes("商品已告罄"))}},actions:{}}),f=a("currentPddUrl",{state:()=>({url:""}),getters:{isVerifiPage(){return this.url.startsWith("https://mobile.pinduoduo.com/psnl_verification.html?")||this.url.startsWith("https://mobile.yangkeduo.com/psnl_verification.html?")},isGoodsDetailsPage(){return/^(https|http):\/\/mobile\.(pinduoduo|yangkeduo)\.com\/goods\d*\.html\?.*goods_id=/.test(this.url)}},actions:{changeUrl(t,s=!1){if(t){switch(t){case"home":case"back":case"next":case"reload":break;default:if(!t.startsWith("https://mobile.pinduoduo.com/")&&!t.startsWith("https://mobile.yangkeduo.com/"))return n.warning("不能跳转到拼多多以外的链接")}return l(t,s)}else return n.warning("请输入地址")}}});export{h as a,f as u};
