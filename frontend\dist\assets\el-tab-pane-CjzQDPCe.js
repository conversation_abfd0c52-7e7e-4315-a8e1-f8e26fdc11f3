import{ct as ge,cu as _e,cv as Ne,bn as U,cw as ne,bo as Y,_ as le,d as V,aQ as J,cx as Z,a as W,a5 as E,s as R,bk as oe,b as re,o as ce,f as Ce,n as ie,e as O,aS as H,aW as B,ab as ee,cy as we,cz as Te,c as D,b1 as ue,cA as Ee,i as v,E as I,cB as Se,aN as Pe,b4 as K,cC as $e,cD as xe,cE as ke,cF as Be,an as Oe,cG as Re,r as de,aV as te,a_ as be,u as ze,cH as ae,t as Ae,cI as Fe,a6 as Me,a9 as Ve,a7 as Le,g as De,w as Ie}from"./index-CmETSh6Y.js";const q=Symbol("tabsRootContextKey"),Ke=(e,s,f)=>_e(e.subTree).filter(t=>{var n;return Ne(t)&&((n=t.type)==null?void 0:n.name)===s&&!!t.component}).map(t=>t.component.uid).map(t=>f[t]).filter(t=>!!t),Ue=(e,s)=>{const f={},C=ge([]);return{children:C,addChild:n=>{f[n.uid]=n,C.value=Ke(e,s,f)},removeChild:n=>{delete f[n],C.value=C.value.filter(x=>x.uid!==n)}}},We=U({tabs:{type:Y(Array),default:()=>ne([])}}),ve="ElTabBar",He=V({name:ve}),qe=V({...He,props:We,setup(e,{expose:s}){const f=e,C=H(),c=J(q);c||Z(ve,"<el-tabs><el-tab-bar /></el-tabs>");const t=W("tabs"),n=E(),x=E(),d=()=>{let m=0,r=0;const u=["top","bottom"].includes(c.props.tabPosition)?"width":"height",b=u==="width"?"x":"y";return f.tabs.every(S=>{var P,z,a,_;const p=(z=(P=C.parent)==null?void 0:P.refs)==null?void 0:z[`tab-${S.uid}`];if(!p)return!1;if(!S.active)return!0;r=p[`client${B(u)}`];const $=b==="x"?"left":"top";m=p[`offset${B($)}`]-((_=(a=p.parentElement)==null?void 0:a[`offset${B($)}`])!=null?_:0);const F=p.closest(".is-scrollable");if(F){const L=window.getComputedStyle(F);m+=Number.parseFloat(L[`padding${B($)}`])}const M=window.getComputedStyle(p);return u==="width"&&(f.tabs.length>1&&(r-=Number.parseFloat(M.paddingLeft)+Number.parseFloat(M.paddingRight)),m+=Number.parseFloat(M.paddingLeft)),!1}),{[u]:`${r}px`,transform:`translate${B(b)}(${m}px)`}},y=()=>x.value=d();return R(()=>f.tabs,async()=>{await ee(),y()},{immediate:!0}),oe(n,()=>y()),s({ref:n,update:y}),(m,r)=>(ce(),re("div",{ref_key:"barRef",ref:n,class:ie([O(t).e("active-bar"),O(t).is(O(c).props.tabPosition)]),style:Ce(x.value)},null,6))}});var je=le(qe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-bar.vue"]]);const Ge=U({panes:{type:Y(Array),default:()=>ne([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),Qe={tabClick:(e,s,f)=>f instanceof Event,tabRemove:(e,s)=>s instanceof Event},se="ElTabNav",Xe=V({name:se,props:Ge,emits:Qe,setup(e,{expose:s,emit:f}){const C=H(),c=J(q);c||Z(se,"<el-tabs><tab-nav /></el-tabs>");const t=W("tabs"),n=we(),x=Te(),d=E(),y=E(),m=E(),r=E(!1),u=E(0),b=E(!1),S=E(!0),P=D(()=>["top","bottom"].includes(c.props.tabPosition)?"width":"height"),z=D(()=>({transform:`translate${P.value==="width"?"X":"Y"}(-${u.value}px)`})),a=()=>{if(!d.value)return;const l=d.value[`offset${B(P.value)}`],i=u.value;if(!i)return;const o=i>l?i-l:0;u.value=o},_=()=>{if(!d.value||!y.value)return;const l=y.value[`offset${B(P.value)}`],i=d.value[`offset${B(P.value)}`],o=u.value;if(l-o<=i)return;const w=l-o>i*2?o+i:l-i;u.value=w},p=async()=>{const l=y.value;if(!r.value||!m.value||!d.value||!l)return;await ee();const i=m.value.querySelector(".is-active");if(!i)return;const o=d.value,w=["top","bottom"].includes(c.props.tabPosition),N=i.getBoundingClientRect(),g=o.getBoundingClientRect(),k=w?l.offsetWidth-g.width:l.offsetHeight-g.height,T=u.value;let h=T;w?(N.left<g.left&&(h=T-(g.left-N.left)),N.right>g.right&&(h=T+N.right-g.right)):(N.top<g.top&&(h=T-(g.top-N.top)),N.bottom>g.bottom&&(h=T+(N.bottom-g.bottom))),h=Math.max(h,0),u.value=Math.min(h,k)},$=()=>{if(!y.value||!d.value)return;const l=y.value[`offset${B(P.value)}`],i=d.value[`offset${B(P.value)}`],o=u.value;if(i<l){const w=u.value;r.value=r.value||{},r.value.prev=w,r.value.next=w+i<l,l-w<i&&(u.value=l-i)}else r.value=!1,o>0&&(u.value=0)},F=l=>{const i=l.code,{up:o,down:w,left:N,right:g}=K;if(![o,w,N,g].includes(i))return;const k=Array.from(l.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),T=k.indexOf(l.target);let h;i===N||i===o?T===0?h=k.length-1:h=T-1:T<k.length-1?h=T+1:h=0,k[h].focus({preventScroll:!0}),k[h].click(),M()},M=()=>{S.value&&(b.value=!0)},L=()=>b.value=!1;return R(n,l=>{l==="hidden"?S.value=!1:l==="visible"&&setTimeout(()=>S.value=!0,50)}),R(x,l=>{l?setTimeout(()=>S.value=!0,50):S.value=!1}),oe(m,$),ue(()=>setTimeout(()=>p(),0)),Ee(()=>$()),s({scrollToActiveTab:p,removeFocus:L}),R(()=>e.panes,()=>C.update(),{flush:"post"}),()=>{const l=r.value?[v("span",{class:[t.e("nav-prev"),t.is("disabled",!r.value.prev)],onClick:a},[v(I,null,{default:()=>[v(Se,null,null)]})]),v("span",{class:[t.e("nav-next"),t.is("disabled",!r.value.next)],onClick:_},[v(I,null,{default:()=>[v(Pe,null,null)]})])]:null,i=e.panes.map((o,w)=>{var N,g,k,T;const h=o.uid,j=o.props.disabled,G=(g=(N=o.props.name)!=null?N:o.index)!=null?g:`${w}`,Q=!j&&(o.isClosable||e.editable);o.index=`${w}`;const pe=Q?v(I,{class:"is-icon-close",onClick:A=>f("tabRemove",o,A)},{default:()=>[v($e,null,null)]}):null,he=((T=(k=o.slots).label)==null?void 0:T.call(k))||o.props.label,ye=!j&&o.active?0:-1;return v("div",{ref:`tab-${h}`,class:[t.e("item"),t.is(c.props.tabPosition),t.is("active",o.active),t.is("disabled",j),t.is("closable",Q),t.is("focus",b.value)],id:`tab-${G}`,key:`tab-${h}`,"aria-controls":`pane-${G}`,role:"tab","aria-selected":o.active,tabindex:ye,onFocus:()=>M(),onBlur:()=>L(),onClick:A=>{L(),f("tabClick",o,G,A)},onKeydown:A=>{Q&&(A.code===K.delete||A.code===K.backspace)&&f("tabRemove",o,A)}},[he,pe])});return v("div",{ref:m,class:[t.e("nav-wrap"),t.is("scrollable",!!r.value),t.is(c.props.tabPosition)]},[l,v("div",{class:t.e("nav-scroll"),ref:d},[v("div",{class:[t.e("nav"),t.is(c.props.tabPosition),t.is("stretch",e.stretch&&["top","bottom"].includes(c.props.tabPosition))],ref:y,style:z.value,role:"tablist",onKeydown:F},[e.type?null:v(je,{tabs:[...e.panes]},null),i])])])}}}),Ye=U({type:{type:String,values:["card","border-card",""],default:""},activeName:{type:[String,Number]},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:Y(Function),default:()=>!0},stretch:Boolean}),X=e=>xe(e)||ke(e),Je={[be]:e=>X(e),tabClick:(e,s)=>s instanceof Event,tabChange:e=>X(e),edit:(e,s)=>["remove","add"].includes(s),tabRemove:e=>X(e),tabAdd:()=>!0};var Ze=V({name:"ElTabs",props:Ye,emits:Je,setup(e,{emit:s,slots:f,expose:C}){var c,t;const n=W("tabs"),{children:x,addChild:d,removeChild:y}=Ue(H(),"ElTabPane"),m=E(),r=E((t=(c=e.modelValue)!=null?c:e.activeName)!=null?t:"0"),u=a=>{r.value=a,s(be,a),s("tabChange",a)},b=async a=>{var _,p,$;if(!(r.value===a||te(a)))try{await((_=e.beforeLeave)==null?void 0:_.call(e,a,r.value))!==!1&&(u(a),($=(p=m.value)==null?void 0:p.removeFocus)==null||$.call(p))}catch{}},S=(a,_,p)=>{a.props.disabled||(b(_),s("tabClick",a,p))},P=(a,_)=>{a.props.disabled||te(a.props.name)||(_.stopPropagation(),s("edit",a.props.name,"remove"),s("tabRemove",a.props.name))},z=()=>{s("edit",void 0,"add"),s("tabAdd")};return Be({from:'"activeName"',replacement:'"model-value" or "v-model"',scope:"ElTabs",version:"2.3.0",ref:"https://element-plus.org/en-US/component/tabs.html#attributes",type:"Attribute"},D(()=>!!e.activeName)),R(()=>e.activeName,a=>b(a)),R(()=>e.modelValue,a=>b(a)),R(r,async()=>{var a;await ee(),(a=m.value)==null||a.scrollToActiveTab()}),Oe(q,{props:e,currentName:r,registerPane:d,unregisterPane:y}),C({currentName:r}),()=>{const a=e.editable||e.addable?v("span",{class:n.e("new-tab"),tabindex:"0",onClick:z,onKeydown:$=>{$.code===K.enter&&z()}},[v(I,{class:n.is("icon-plus")},{default:()=>[v(Re,null,null)]})]):null,_=v("div",{class:[n.e("header"),n.is(e.tabPosition)]},[a,v(Xe,{ref:m,currentName:r.value,editable:e.editable,type:e.type,panes:x.value,stretch:e.stretch,onTabClick:S,onTabRemove:P},null)]),p=v("div",{class:n.e("content")},[de(f,"default")]);return v("div",{class:[n.b(),n.m(e.tabPosition),{[n.m("card")]:e.type==="card",[n.m("border-card")]:e.type==="border-card"}]},[...e.tabPosition!=="bottom"?[_,p]:[p,_]])}}});const et=U({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),tt=["id","aria-hidden","aria-labelledby"],fe="ElTabPane",at=V({name:fe}),st=V({...at,props:et,setup(e){const s=e,f=H(),C=ze(),c=J(q);c||Z(fe,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const t=W("tab-pane"),n=E(),x=D(()=>s.closable||c.props.closable),d=ae(()=>{var b;return c.currentName.value===((b=s.name)!=null?b:n.value)}),y=E(d.value),m=D(()=>{var b;return(b=s.name)!=null?b:n.value}),r=ae(()=>!s.lazy||y.value||d.value);R(d,b=>{b&&(y.value=!0)});const u=Ae({uid:f.uid,slots:C,props:s,paneName:m,active:d,index:n,isClosable:x});return ue(()=>{c.registerPane(u)}),Fe(()=>{c.unregisterPane(u.uid)}),(b,S)=>O(r)?Me((ce(),re("div",{key:0,id:`pane-${O(m)}`,class:ie(O(t).b()),role:"tabpanel","aria-hidden":!O(d),"aria-labelledby":`tab-${O(m)}`},[de(b.$slots,"default")],10,tt)),[[Le,O(d)]]):Ve("v-if",!0)}});var me=le(st,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-pane.vue"]]);const lt=De(Ze,{TabPane:me}),ot=Ie(me);export{lt as E,ot as a};
