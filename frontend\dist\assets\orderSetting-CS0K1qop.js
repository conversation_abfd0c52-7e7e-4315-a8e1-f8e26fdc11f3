import{d as G,a5 as J,s as $,b as z,o as E,i as a,k as o,Q as F,R as M,T as le,ad as C,j as r,H as X,av as se,q as ae,t as B,b$ as ie,G as oe,h as i,P as de,e as s,au as ne,at as re,aw as ue,af as pe,S as me,M as ce,bd as ve,a9 as _e,a6 as fe,a7 as Ve,J as be,L as ge,X as P,ak as T,d4 as ye,a2 as Ce,a3 as we,bJ as xe,bG as Se,aC as K,m as Ue}from"./index-CmETSh6Y.js";import{E as he,a as De}from"./el-table-column-XN33CJP9.js";import"./el-tooltip-l0sNRNKZ.js";import{E as ke}from"./el-space-mudxGYIP.js";import{E as Ee,b as Ae,a as <PERSON>}from"./el-radio-DoZFrPxL.js";import{E as $e}from"./el-input-number-V2-U9i7h.js";import{g as ze,d as Ie,a as Ne,m as Pe}from"./address-DsxfsM69.js";/* empty css                          */import{a as Be}from"./autoApply-izhrV0Ni.js";const j=G({__name:"checkGroup",props:{list:null,title:{default:"title"},valueKey:{default:"value"},defaultValue:null},emits:["change"],setup(b,{emit:t}){const S=b;function A(){const{list:n,valueKey:c,defaultValue:U}=S;if(U){const w=new Set(n.map(v=>v[c]));return U.filter(v=>w.has(v))}else return[]}const g=J(A());return console.log(g),$(g,n=>{t("change",n)},{immediate:!0}),(n,c)=>{const U=C,w=se;return E(),z("div",null,[a(w,{modelValue:g.value,"onUpdate:modelValue":c[0]||(c[0]=v=>g.value=v),size:"default"},{default:o(()=>[(E(!0),z(F,null,M(b.list,v=>(E(),le(U,{key:v[b.valueKey],label:v[b.valueKey]},{default:o(()=>[r(X(v[b.title]),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])])}}}),u=b=>(Ce("data-v-147ad9e9"),b=b(),we(),b),Te={class:"order-setting"},Fe={class:"box pay-setting"},Oe=u(()=>i("h4",{class:"title"},"支付设置",-1)),Re={class:"container"},Ke={class:"el-radio no-bg"},je=u(()=>i("span",{class:"tips"},"（首次自动需要填写验证码）",-1)),Ge=u(()=>i("span",{class:"m-l-10"},"单",-1)),Je=u(()=>i("span",{class:"tips"},"（最多支持10单同时付款）",-1)),Me=u(()=>i("span",{class:"tips"},"（请不要在执行支付时修改）",-1)),Xe=u(()=>i("p",{class:"tips"},"服务器不保存账号密码，仅本地自动填写",-1)),qe={class:"box view-setting"},He=u(()=>i("h4",{class:"title"},"浏览设置",-1)),Qe={class:"container"},We=u(()=>i("span",null,"至",-1)),Ye=u(()=>i("span",null,"家",-1)),Ze=u(()=>i("span",null,"至",-1)),et=u(()=>i("span",null,"秒",-1)),tt=u(()=>i("span",null,"至",-1)),lt=u(()=>i("span",null,"个",-1)),st=u(()=>i("span",null,"至",-1)),at=u(()=>i("span",null,"秒",-1)),it=u(()=>i("p",null,"由于执行自动支付流程需要时间(大概3-5秒)",-1)),ot=u(()=>i("p",null,"所以5秒以下的间隔，在视觉上没有间隔",-1)),dt=u(()=>i("p",null,"但真实执行上是有间隔的",-1)),nt=u(()=>i("p",null,null,-1)),rt=u(()=>i("span",null,"至",-1)),ut=u(()=>i("span",null,"秒",-1)),pt=u(()=>i("span",null,"至",-1)),mt=u(()=>i("span",null,"秒",-1)),ct={class:"box address-setting"},vt=u(()=>i("h4",{class:"title"},"地址设置：",-1)),_t={class:"container"},ft={class:"item"},Vt=u(()=>i("div",{class:"label"},"地址类型：",-1)),bt={class:"content"},gt={class:"diy-addr"},yt={class:"m-t-10"},Ct={class:"tips"},wt={class:"item"},xt=u(()=>i("div",{class:"label"},"过滤省份：",-1)),St={class:"content"},Ut={class:"item"},ht=u(()=>i("div",{class:"label"},"指定省份：",-1)),Dt={class:"content"},kt={class:"item name-pass"},Et={class:"label"},At={class:"content"},Lt={class:"item addr-pass"},$t={class:"label"},zt={class:"content"},It={class:"box chat-setting"},Nt=u(()=>i("h4",{class:"title"},"假聊设置",-1)),Pt={class:"container"},Bt={class:"item"},Tt={class:"label"},Ft={class:"content"},Ot={class:"item"},Rt=u(()=>i("div",{class:"label"},"发送数量：",-1)),Kt={class:"content"},jt=u(()=>i("span",{class:"tips"},"条",-1)),Gt={class:"item"},Jt={class:"content",style:{"margin-left":"20px"}},Mt=u(()=>i("p",{class:"tips"}," 提示：也可以在订单管理，【与此商家聊天】进行手工聊天 ",-1)),Xt=u(()=>i("p",{class:"tips able-select"},[r(" 地址格式："),i("span",{class:"danger"},"姓名|手机号|省|市|区|详细地址 "),r(" 用“|”隔开！一个一行 ")],-1)),qt=u(()=>i("p",{style:{color:"var(--el-text-color-secondary)"}}," 如果此处地址使用完了，会循环调用，请及时更新 ",-1)),Ht=G({__name:"orderSetting",setup(b){const t=ae();$(()=>t.pay,d=>{d&&(d.isNew===void 0&&(d.isNew=!0),t.setOrderSetting("pay",{...d}))},{deep:!0}),$(()=>t.visit,d=>{if(d){const e=JSON.stringify(d);t.setOrderSetting("visit",JSON.parse(e))}},{deep:!0}),$(()=>t.address,d=>{d&&t.setOrderSetting("address",{...d})},{deep:!0}),$(()=>t.chat,d=>{d&&t.setOrderSetting("chat",{...d})},{deep:!0});function S(d,e){d&&d>t.visit[e].max&&(t.visit[e].max=d)}const A=B({provinceList:[]});ie({pid:0}).then(d=>{console.log(d.data),A.provinceList=d.data});const g=J(),n=B({dialog_import:!1,dialog_list:!1,addressList:[],selection:[],addrInput:"",listPagination:{page:1,limit:100,total:0}}),c=B({importAddr:!1,delSelected:!1,delAddrAll:!1});function U(d){var e;switch(d){case"all":{n.addressList.forEach(p=>{var m;(m=g.value)==null||m.toggleRowSelection(p,!0)});break}case"reverse":{const p=new Set(n.selection.map(m=>m.id));n.addressList.forEach(m=>{var _,h;p.has(m.id)?(_=g.value)==null||_.toggleRowSelection(m,!1):(h=g.value)==null||h.toggleRowSelection(m,!0)});break}case"clear":(e=g.value)==null||e.clearSelection()}}function w(){const{limit:d,page:e}=n.listPagination;ze({limit:d,page:e}).then(p=>{n.listPagination.total=p.total,n.addressList=p.list})}w();async function v(d){(typeof d=="string"||d.length>1)&&await P({title:"警告",type:"warning",message:"确认要删除吗？",showCancelButton:!0});let e;typeof d=="string"?(c.delAddrAll=!0,e=Ie()):(c.delSelected=!0,e=Ne({ids:d.map(p=>p.id).join(",")})),e.then(()=>{w()}).finally(()=>{c.delSelected=!1,c.delAddrAll=!1})}async function q(){const{addrInput:d}=n,e=d.split(`
`).filter(_=>_),p=[],m=[];e.forEach(_=>{const[h,L,I,y,V,N]=_.split("|");h&&L&&I&&y&&V&&N&&/^1[3-9]\d{9}$/.test(L)?p.push({name:h,phone:L,province:I,city:y,district:V,address:N}):m.push(_)}),p.length?(T.success({message:`本次已成功解析并导入${p.length}条数据`,type:"success"}),c.importAddr=!0,Pe(p).then(()=>{w()}).finally(()=>{c.importAddr=!1})):T.warning({message:"写入的数据没有分析出有效地址",grouping:!0}),n.addrInput=m.join(`
`)}async function H(){const d=await xe({properties:["openFile"],filters:[{extensions:["txt"],name:"txt"}]});d.code&&!d.data.length||(await Promise.allSettled(d.data.map(e=>Se({url:e}).then(p=>(n.addrInput+=`
`+p,p)))),n.dialog_import=!0)}async function Q(){await P({message:"确定要重置吗?",type:"warning",title:"提示",showCancelButton:!0}),Be("reset"),ye().then(()=>{T.success("清除成功")})}async function W(){const d=t.address.filterStr.split(/[,，]/).filter(p=>p);let e=[];await P({title:"选择地址",message:K(j,{onChange:p=>e=p,list:A.provinceList,title:"name",valueKey:"name",defaultValue:d}),showCancelButton:!0}),t.address.filterStr=e.join(",")}async function Y(){const d=(t.address.appoint_address||"").split(/[,，]/).filter(p=>p);let e=[];await P({title:"选择地址",message:K(j,{onChange:p=>e=p,list:A.provinceList,title:"name",valueKey:"name",defaultValue:d}),showCancelButton:!0}),t.address.appoint_address=e.join(",")}return(d,e)=>{const p=Ae,m=Ee,_=de,h=re,L=ne,I=ue,y=pe,V=me,N=ce,f=$e,O=ve,D=be,k=Le,R=ge,Z=ke,x=De,ee=he,te=oe("ds-pagination");return E(),z("div",Te,[i("div",Fe,[Oe,i("div",Re,[a(N,{"label-width":"100px"},{default:o(()=>[a(_,{label:"何时付款："},{default:o(()=>[a(m,{modelValue:s(t).pay.chance,"onUpdate:modelValue":e[0]||(e[0]=l=>s(t).pay.chance=l)},{default:o(()=>[a(p,{border:"",label:"immediate"},{default:o(()=>[r("下单立即付款")]),_:1}),a(p,{border:"",label:"delay"},{default:o(()=>[r("先下单再付款")]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"付款方式："},{default:o(()=>[a(m,{modelValue:s(t).pay.type,"onUpdate:modelValue":e[1]||(e[1]=l=>s(t).pay.type=l)},{default:o(()=>[i("div",Ke,[a(p,{border:"",label:"auto"},{default:o(()=>[r("支付宝自动付款")]),_:1}),je])]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"同时支付："},{default:o(()=>[a(L,{style:{width:"100px"},modelValue:s(t).pay.winCount,"onUpdate:modelValue":e[2]||(e[2]=l=>s(t).pay.winCount=l)},{default:o(()=>[(E(),z(F,null,M(10,l=>a(h,{label:l,value:l},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),Ge,Je]),_:1}),a(_,{label:"使用新版支付:"},{default:o(()=>[a(I,{modelValue:s(t).pay.isNew,"onUpdate:modelValue":e[3]||(e[3]=l=>s(t).pay.isNew=l)},null,8,["modelValue"]),Me]),_:1}),a(_,{label:"支付密码："},{default:o(()=>[i("p",null,[a(y,{placeholder:"请输入支付宝数字密码",type:"password","show-password":!0,style:{width:"200px"},class:"m-r-10",modelValue:s(t).pay.zfb_pass,"onUpdate:modelValue":e[4]||(e[4]=l=>s(t).pay.zfb_pass=l)},null,8,["modelValue"]),a(V,{underline:!1,onClick:Q,type:"danger"},{default:o(()=>[r("更换支付宝账号")]),_:1})]),Xe]),_:1})]),_:1})])]),i("div",qe,[He,i("div",Qe,[i("p",null,[a(s(C),{modelValue:s(t).visit.compare.active,"onUpdate:modelValue":e[5]||(e[5]=l=>s(t).visit.compare.active=l)},{default:o(()=>[r("货比几家：")]),_:1},8,["modelValue"]),a(f,{precision:0,disabled:!s(t).visit.compare.active,controls:!1,modelValue:s(t).visit.compare.min,"onUpdate:modelValue":e[6]||(e[6]=l=>s(t).visit.compare.min=l),min:0,onChange:e[7]||(e[7]=l=>S(l,"compare"))},null,8,["disabled","modelValue"]),We,a(f,{precision:0,disabled:!s(t).visit.compare.active,controls:!1,modelValue:s(t).visit.compare.max,"onUpdate:modelValue":e[8]||(e[8]=l=>s(t).visit.compare.max=l),min:s(t).visit.compare.min},null,8,["disabled","modelValue","min"]),Ye]),i("p",null,[a(s(C),{modelValue:s(t).visit.visitDetails.active,"onUpdate:modelValue":e[9]||(e[9]=l=>s(t).visit.visitDetails.active=l)},{default:o(()=>[r("浏览详情：")]),_:1},8,["modelValue"]),a(f,{precision:0,disabled:!s(t).visit.visitDetails.active,controls:!1,modelValue:s(t).visit.visitDetails.min,"onUpdate:modelValue":e[10]||(e[10]=l=>s(t).visit.visitDetails.min=l),min:0,onChange:e[11]||(e[11]=l=>S(l,"visitDetails"))},null,8,["disabled","modelValue"]),Ze,a(f,{precision:0,disabled:!s(t).visit.visitDetails.active,controls:!1,modelValue:s(t).visit.visitDetails.max,"onUpdate:modelValue":e[12]||(e[12]=l=>s(t).visit.visitDetails.max=l),min:s(t).visit.visitDetails.min},null,8,["disabled","modelValue","min"]),et]),i("p",null,[a(s(C),{modelValue:s(t).visit.visitCount.active,"onUpdate:modelValue":e[13]||(e[13]=l=>s(t).visit.visitCount.active=l)},{default:o(()=>[r("访客数量：")]),_:1},8,["modelValue"]),a(f,{precision:0,disabled:!s(t).visit.visitCount.active,controls:!1,modelValue:s(t).visit.visitCount.min,"onUpdate:modelValue":e[14]||(e[14]=l=>s(t).visit.visitCount.min=l),min:0,max:30,onChange:e[15]||(e[15]=l=>S(l,"visitCount"))},null,8,["disabled","modelValue"]),tt,a(f,{precision:0,disabled:!s(t).visit.visitCount.active,controls:!1,modelValue:s(t).visit.visitCount.max,"onUpdate:modelValue":e[16]||(e[16]=l=>s(t).visit.visitCount.max=l),min:s(t).visit.visitCount.min,max:30},null,8,["disabled","modelValue","min"]),lt]),i("p",null,[a(s(C),{modelValue:s(t).visit.orderDelay.active,"onUpdate:modelValue":e[17]||(e[17]=l=>s(t).visit.orderDelay.active=l)},{default:o(()=>[a(O,{content:"单个任务的下单间隔，不同任务之间不间隔"},{default:o(()=>[r("下单间隔：")]),_:1})]),_:1},8,["modelValue"]),a(f,{precision:0,disabled:!s(t).visit.orderDelay.active,controls:!1,modelValue:s(t).visit.orderDelay.min,"onUpdate:modelValue":e[18]||(e[18]=l=>s(t).visit.orderDelay.min=l),onChange:e[19]||(e[19]=l=>S(l,"orderDelay")),min:0},null,8,["disabled","modelValue"]),st,a(f,{precision:0,disabled:!s(t).visit.orderDelay.active,controls:!1,modelValue:s(t).visit.orderDelay.max,"onUpdate:modelValue":e[20]||(e[20]=l=>s(t).visit.orderDelay.max=l),min:s(t).visit.orderDelay.min},null,8,["disabled","modelValue","min"]),at]),a(O,{"popper-class":"ordersetting-viewsetting-paydelay-tooltip"},{content:o(()=>[it,ot,dt,nt]),default:o(()=>[i("p",null,[a(s(C),{modelValue:s(t).visit.payDelay.active,"onUpdate:modelValue":e[21]||(e[21]=l=>s(t).visit.payDelay.active=l)},{default:o(()=>[r("付款间隔：")]),_:1},8,["modelValue"]),a(f,{onChange:e[22]||(e[22]=l=>S(l,"payDelay")),precision:0,disabled:!s(t).visit.payDelay.active,controls:!1,modelValue:s(t).visit.payDelay.min,"onUpdate:modelValue":e[23]||(e[23]=l=>s(t).visit.payDelay.min=l),min:0},null,8,["disabled","modelValue"]),rt,a(f,{precision:0,disabled:!s(t).visit.payDelay.active,controls:!1,modelValue:s(t).visit.payDelay.max,"onUpdate:modelValue":e[24]||(e[24]=l=>s(t).visit.payDelay.max=l),min:s(t).visit.payDelay.min},null,8,["disabled","modelValue","min"]),ut])]),_:1}),i("p",null,[a(s(C),{modelValue:s(t).visit.confirm.active,"onUpdate:modelValue":e[25]||(e[25]=l=>s(t).visit.confirm.active=l)},{default:o(()=>[r("收货间隔：")]),_:1},8,["modelValue"]),a(f,{precision:1,disabled:!s(t).visit.confirm.active,controls:!1,modelValue:s(t).visit.confirm.min,"onUpdate:modelValue":e[26]||(e[26]=l=>s(t).visit.confirm.min=l),min:0,max:30,onChange:e[27]||(e[27]=l=>S(l,"confirm"))},null,8,["disabled","modelValue"]),pt,a(f,{precision:1,disabled:!s(t).visit.confirm.active,controls:!1,modelValue:s(t).visit.confirm.max,"onUpdate:modelValue":e[28]||(e[28]=l=>s(t).visit.confirm.max=l),min:s(t).visit.confirm.min,max:30},null,8,["disabled","modelValue","min"]),mt])])]),i("div",ct,[vt,i("div",_t,[i("div",ft,[Vt,i("div",bt,[a(m,{class:"addr-type",modelValue:s(t).address.type,"onUpdate:modelValue":e[29]||(e[29]=l=>s(t).address.type=l)},{default:o(()=>[a(p,{border:"",label:"random"},{default:o(()=>[r("系统随机真实地址【无重复】")]),_:1}),a(p,{border:"",label:"diy"},{default:o(()=>[r("自定义地址")]),_:1})]),_:1},8,["modelValue"]),fe(i("div",gt,[a(V,{type:"primary",onClick:H},{default:o(()=>[r("文件导入")]),_:1}),a(V,{type:"success",disabled:!1,onClick:e[30]||(e[30]=l=>n.dialog_import=!0)},{default:o(()=>[r("手动输入")]),_:1}),a(V,{disabled:!1,onClick:e[31]||(e[31]=l=>n.dialog_list=!0)},{default:o(()=>[r("查看地址")]),_:1}),i("p",yt,[i("span",Ct,"已导入"+X(n.listPagination.total)+"条地址",1)])],512),[[Ve,s(t).address.type==="diy"]])])]),s(t).address.type==="random"?(E(),z(F,{key:0},[i("div",wt,[xt,i("div",St,[a(y,{disabled:!0,class:"addr-filter",modelValue:s(t).address.filterStr,"onUpdate:modelValue":e[32]||(e[32]=l=>s(t).address.filterStr=l),placeholder:"有指定省份优先使用指定省份"},null,8,["modelValue"]),a(D,{underline:!1,class:"m-l-8",type:"primary",onClick:W},{default:o(()=>[r("选择")]),_:1})])]),i("div",Ut,[ht,i("div",Dt,[a(y,{disabled:!0,class:"addr-filter",modelValue:s(t).address.appoint_address,"onUpdate:modelValue":e[33]||(e[33]=l=>s(t).address.appoint_address=l),placeholder:"设置指定省份会让过滤省份失效"},null,8,["modelValue"]),a(D,{underline:!1,class:"m-l-8",type:"primary",onClick:Y},{default:o(()=>[r("选择")]),_:1})])])],64)):_e("",!0),i("div",kt,[i("div",Et,[a(s(C),{label:"姓名暗号：",size:"default",modelValue:s(t).address.nameCode_active,"onUpdate:modelValue":e[34]||(e[34]=l=>s(t).address.nameCode_active=l)},null,8,["modelValue"])]),i("div",At,[a(m,{modelValue:s(t).address.nameCode_position,"onUpdate:modelValue":e[35]||(e[35]=l=>s(t).address.nameCode_position=l),disabled:!s(t).address.nameCode_active},{default:o(()=>[a(k,{label:"before"},{default:o(()=>[r("加前")]),_:1}),a(k,{label:"after"},{default:o(()=>[r("加后")]),_:1})]),_:1},8,["modelValue","disabled"]),a(y,{modelValue:s(t).address.nameCode_str,"onUpdate:modelValue":e[36]||(e[36]=l=>s(t).address.nameCode_str=l),disabled:!s(t).address.nameCode_active},null,8,["modelValue","disabled"])])]),i("div",Lt,[i("div",$t,[a(s(C),{label:"地址暗号：",size:"default",modelValue:s(t).address.addr_active,"onUpdate:modelValue":e[37]||(e[37]=l=>s(t).address.addr_active=l)},null,8,["modelValue"])]),i("div",zt,[a(m,null,{default:o(()=>[a(m,{modelValue:s(t).address.addr_position,"onUpdate:modelValue":e[38]||(e[38]=l=>s(t).address.addr_position=l),disabled:!s(t).address.addr_active},{default:o(()=>[a(k,{label:"before"},{default:o(()=>[r("加前")]),_:1}),a(k,{label:"after"},{default:o(()=>[r("加后")]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),a(y,{modelValue:s(t).address.addr_str,"onUpdate:modelValue":e[39]||(e[39]=l=>s(t).address.addr_str=l),disabled:!s(t).address.addr_active},null,8,["modelValue","disabled"])])])])]),i("div",It,[Nt,i("div",Pt,[i("div",Bt,[i("div",Tt,[a(s(C),{modelValue:s(t).chat.active,"onUpdate:modelValue":e[40]||(e[40]=l=>s(t).chat.active=l),label:"开启假聊：",size:"default"},null,8,["modelValue"])]),i("div",Ft,[a(m,{modelValue:s(t).chat.type,"onUpdate:modelValue":e[41]||(e[41]=l=>s(t).chat.type=l)},{default:o(()=>[a(k,{label:"random"},{default:o(()=>[r("随机发送")]),_:1}),a(k,{label:"order"},{default:o(()=>[r("固定发送")]),_:1})]),_:1},8,["modelValue"])])]),i("div",Ot,[Rt,i("div",Kt,[a(f,{min:1,precision:0,modelValue:s(t).chat.num,"onUpdate:modelValue":e[42]||(e[42]=l=>s(t).chat.num=l)},null,8,["modelValue"]),jt])]),i("div",Gt,[i("div",Jt,[a(y,{modelValue:s(t).chat.content,"onUpdate:modelValue":e[43]||(e[43]=l=>s(t).chat.content=l),type:"textarea",rows:10,resize:"none",placeholder:"换行隔开每一条消息"},null,8,["modelValue"]),Mt])])])]),a(R,{modelValue:n.dialog_import,"onUpdate:modelValue":e[46]||(e[46]=l=>n.dialog_import=l),title:"导入地址",width:480,class:"import-address-dialog dialog-480"},{footer:o(()=>[a(V,{onClick:e[45]||(e[45]=l=>n.dialog_import=!1)},{default:o(()=>[r("取消")]),_:1}),a(V,{type:"primary",onClick:q,loading:c.importAddr,disabled:c.importAddr},{default:o(()=>[r("确定")]),_:1},8,["loading","disabled"])]),default:o(()=>[Xt,a(y,{modelValue:n.addrInput,"onUpdate:modelValue":e[44]||(e[44]=l=>n.addrInput=l),type:"textarea",placeholder:"请输入",rows:20,resize:"none"},null,8,["modelValue"]),qt]),_:1},8,["modelValue"]),a(R,{modelValue:n.dialog_list,"onUpdate:modelValue":e[58]||(e[58]=l=>n.dialog_list=l),top:"110px",title:"我的地址",width:800,class:"address-list-dialog"},{footer:o(()=>[a(V,{onClick:e[57]||(e[57]=l=>n.dialog_list=!1)},{default:o(()=>[r("关闭")]),_:1})]),default:o(()=>[a(Z,{class:"table-header",size:16},{default:o(()=>[a(D,{onClick:e[47]||(e[47]=l=>U("all")),type:"primary"},{default:o(()=>[r("全选")]),_:1}),a(D,{onClick:e[48]||(e[48]=l=>U("reverse"))},{default:o(()=>[r("反选")]),_:1}),a(D,{onClick:e[49]||(e[49]=l=>U("clear"))},{default:o(()=>[r("取消选择")]),_:1}),a(V,{type:"danger",loading:c.delSelected,plain:"",disabled:!n.selection.length||c.delSelected,onClick:e[50]||(e[50]=l=>v(n.selection))},{default:o(()=>[r("删除选中")]),_:1},8,["loading","disabled"]),a(V,{type:"danger",disabled:!n.listPagination.total,onClick:e[51]||(e[51]=l=>v("all"))},{default:o(()=>[r("删除所有")]),_:1},8,["disabled"])]),_:1}),a(ee,{ref_key:"addrTableEl",ref:g,data:n.addressList,height:500,onSelectionChange:e[52]||(e[52]=l=>n.selection=l)},{default:o(()=>[a(x,{label:"",type:"selection",width:"50"}),a(x,{label:"序号",type:"index",width:"80"}),a(x,{label:"姓名",prop:"name"}),a(x,{label:"手机号",prop:"phone",width:"100"}),a(x,{label:"省",prop:"province"}),a(x,{label:"市",prop:"city"}),a(x,{label:"区",prop:"district"}),a(x,{label:"地址",prop:"address"}),a(x,{width:"80",label:"操作",prop:""},{default:o(l=>[i("p",null,[a(D,{type:"danger",underline:!1,onClick:Qt=>v([l.row])},{default:o(()=>[r("删除")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),a(te,{selection:n.selection,"current-page":n.listPagination.page,"onUpdate:current-page":e[53]||(e[53]=l=>n.listPagination.page=l),"page-size":n.listPagination.limit,"onUpdate:page-size":e[54]||(e[54]=l=>n.listPagination.limit=l),total:n.listPagination.total,onSizeChange:e[55]||(e[55]=l=>w()),onCurrentChange:e[56]||(e[56]=l=>w())},null,8,["selection","current-page","page-size","total"])]),_:1},8,["modelValue"])])}}}),ol=Ue(Ht,[["__scopeId","data-v-147ad9e9"]]);export{ol as default};
