import{d as Ee,a5 as Me,s as Ve,b as R,o as b,i as o,h as a,k as s,j as l,a6 as _e,H as m,e as i,a7 as ye,a8 as lt,J as Ye,U as nt,Q as be,R as De,n as it,T as Y,a9 as fe,aa as rt,ab as vt,m as Ne,t as re,c as dt,ac as wt,ad as ut,ae as pt,a2 as We,a3 as Ke,G as ct,af as Ge,S as gt,ag as It,X as He,C as qe,ah as Vt,ai as Dt,aj as St,ak as E,al as Ct,am as Ze,q as $t,an as Lt,ao as Pt,ap as xt,E as Et,aq as Nt,ar as Ut,as as Tt,L as zt,at as Ue,au as Te,K as Gt,P as Mt,av as jt,aw as At,M as Bt,ax as Rt,ay as Ft,az as Ot,aA as et,aB as Ht,F as qt,aC as ze,aD as Jt,aE as Yt,aF as Oe,Z as Wt,aG as Kt,aH as Qt,aI as Xt,aJ as Zt,aK as tt,N as eo}from"./index-CmETSh6Y.js";/* empty css                   */import{E as mt,a as _t}from"./el-table-column-XN33CJP9.js";import"./el-tooltip-l0sNRNKZ.js";import{E as ft,a as kt,b as to}from"./el-radio-DoZFrPxL.js";import{E as bt,u as oo}from"./el-config-provider-DRHX7QJ5.js";import{E as so}from"./el-date-picker-W7Ity7Gv.js";/* empty css                          */import{E as ao}from"./el-input-number-V2-U9i7h.js";import{E as lo,a as ht,o as ot,b as st}from"./el-progress-C10IkdN4.js";import{E as Qe}from"./el-space-mudxGYIP.js";import{a as no,u as io}from"./current-Auvi1tS_.js";import{d as je}from"./dayjs.min-Bj8ADfnT.js";import{u as Xe}from"./task-BrHiRyMn.js";import{g as yt,d as at,a as Je}from"./goods-CLH-7jHQ.js";import{E as ro}from"./el-empty-Bk3X6ZAS.js";import{i as uo}from"./extra-BEOWe5WS.js";import"./address-DsxfsM69.js";import"./autoApply-BvulaZhm.js";const po={class:"task-log"},co={class:"m-l-5 primary"},go={class:"m-t-10 m-b-10"},mo={class:"log-list"},_o={class:"time"},fo={class:"msg"},ko=Ee({__name:"taskLog",props:{visible:{type:Boolean}},setup(I){const he=I,F=Xe(),r=Me();return Ve([()=>F.logList.length,()=>he.visible],()=>{var u,x;let se=((x=(u=r.value)==null?void 0:u.wrapRef)==null?void 0:x.scrollHeight)||0;se&&vt(()=>{var L;(L=r.value)==null||L.setScrollTop(se<0?0:se)})},{immediate:!0}),(se,u)=>{const x=bt,L=Ye,ae=nt;return b(),R("div",po,[o(x,null,{default:s(()=>[l(" 有"+m(i(F).loading.size)+"个任务在执行。请求池："+m(i(F).requestPool.size)+"。 当前队列状态 ",1),a("span",co,m(i(F).taskStatusDes),1),_e(a("span",{class:"m-l-5"}," 上一次计时器执行时间 "+m(i(lt)(i(F).pointExecTime)),513),[[ye,i(F).pointExecTime]])]),_:1}),a("p",go,[o(L,{underline:!1,type:"primary",onClick:u[0]||(u[0]=h=>i(F).logList=[])},{default:s(()=>[l("清空日志")]),_:1})]),o(ae,{height:"383px",ref_key:"taskLogScroll",ref:r},{default:s(()=>[a("div",mo,[(b(!0),R(be,null,De(i(F).logList,h=>(b(),R("p",{class:it([h.type])},[a("span",_o,m(i(je)(h.time).format("MM-DD HH:mm:ss"))+"： ",1),a("span",fo,[h.task_id?(b(),Y(L,{key:0,underline:!1,onClick:C=>i(rt)(h.task_id)},{default:s(()=>[l("任务"+m(h.task_id),1)]),_:2},1032,["onClick"])):fe("",!0),l(" "+m(h.msg),1)])],2))),256))])]),_:1},512)])}}}),bo=Ne(ko,[["__scopeId","data-v-fa2623bf"]]),$e=I=>(We("data-v-a55dee8a"),I=I(),Ke(),I),ho={class:"task-details able-select"},yo={class:"task-info"},vo=$e(()=>a("span",{class:"label"},"正在执行：",-1)),wo={class:"value success"},Io=$e(()=>a("span",{class:"label"},"正在排队：",-1)),Vo={class:"value"},Do=$e(()=>a("span",{class:"label"},"下次可执行：",-1)),So={class:"value"},Co=$e(()=>a("span",{class:"label"},"已完成：",-1)),$o={class:"primary"},Lo={class:"record-item"},Po=$e(()=>a("span",{class:"label"},"执行位置:",-1)),xo={class:"value"},Eo=$e(()=>a("span",{class:"label"},"当前请求次数：",-1)),No={class:"value"},Uo={class:"label"},To={class:"label"},zo={class:"label"},Go={class:"label"},Mo={key:0,class:"history"},jo=$e(()=>a("h5",null,"执行历史：",-1)),Ao={class:"label"},Bo={class:"label"},Ro={class:"label"},Fo={class:"success"},Oo={class:"label"},Ho={class:"res"},qo={class:"label"},Jo={key:0,class:"res"},Yo={class:"label"},Wo={class:"danger"},Ko={key:1,class:"res"},Qo={class:"label"},Xo=Ee({__name:"taskDetails",props:{taskDetailsState:null},setup(I){const he=I,F=Xe(),r=re({type:"finish",checkError:!1}),se=dt(()=>{const u=he.taskDetailsState.taskItem;switch(r.type){case"pending":return[...u.pending.values()].sort((x,L)=>x.sort-L.sort);case"loading":return[...u.loading.values()].sort((x,L)=>x.sort-L.sort);case"finish":{const x=[...u.finish.values()].sort((L,ae)=>L.sort-ae.sort);return r.checkError?x.filter(L=>!!L.history.find(ae=>{var h;return!ae.res.code&&!((h=ae.res.data)!=null&&h.order_sn)})):x}}});return(u,x)=>{var g,ne,G,ee,ce,W,D,n,V;const L=wt,ae=Qe,h=lo,C=kt,T=ft,v=ut,O=pt,z=nt;return b(),R("div",ho,[a("div",yo,[a("h5",null,[l(" 任务ID："+m((g=I.taskDetailsState.taskItem)==null?void 0:g.task_id)+" ",1),i(F).paused.has(((ne=I.taskDetailsState.taskItem)==null?void 0:ne.task_id)||"")?(b(),Y(L,{key:0,type:"warning"},{default:s(()=>[l("已暂停")]),_:1})):fe("",!0),i(F).loading.has(((G=I.taskDetailsState.taskItem)==null?void 0:G.task_id)||"")?(b(),Y(L,{key:1,type:"success"},{default:s(()=>[l("执行中")]),_:1})):fe("",!0)]),a("p",null,[o(ae,null,{default:s(()=>{var w,H,ge;return[a("span",null,[vo,l(),a("span",wo,m((w=I.taskDetailsState.taskItem)==null?void 0:w.loading.size),1)]),a("span",null,[Io,l(),a("span",Vo,m((H=I.taskDetailsState.taskItem)==null?void 0:H.pending.size),1)]),a("span",null,[Do,l(),a("span",So,[l(m(i(lt)(I.taskDetailsState.taskItem.nextExecTime))+" ",1),i(F).paused.has(((ge=I.taskDetailsState.taskItem)==null?void 0:ge.task_id)||"")?(b(),Y(L,{key:0,type:"warning"},{default:s(()=>[l("已暂停")]),_:1})):fe("",!0)])])]}),_:1})]),a("p",null,[Co,l(),a("span",null,[a("span",$o,m((ee=I.taskDetailsState.taskItem)==null?void 0:ee.complete_num),1),l("/"+m((ce=I.taskDetailsState.taskItem)==null?void 0:ce.order_num),1)]),(W=I.taskDetailsState.taskItem)!=null&&W.complete_num&&((D=I.taskDetailsState.taskItem)!=null&&D.order_num)?(b(),Y(h,{key:0,percentage:Math.round(((n=I.taskDetailsState.taskItem)==null?void 0:n.complete_num)/((V=I.taskDetailsState.taskItem)==null?void 0:V.order_num)*100)},null,8,["percentage"])):fe("",!0)])]),o(ae,null,{default:s(()=>[o(T,{modelValue:r.type,"onUpdate:modelValue":x[0]||(x[0]=w=>r.type=w)},{default:s(()=>[o(C,{label:"pending"},{default:s(()=>{var w;return[l("排队中("+m((w=I.taskDetailsState.taskItem)==null?void 0:w.pending.size)+")",1)]}),_:1}),o(C,{label:"loading"},{default:s(()=>{var w;return[l("正在执行 ("+m((w=I.taskDetailsState.taskItem)==null?void 0:w.loading.size)+")",1)]}),_:1}),o(C,{label:"finish"},{default:s(()=>{var w;return[l("已结束 ("+m((w=I.taskDetailsState.taskItem)==null?void 0:w.finish.size)+")",1)]}),_:1})]),_:1},8,["modelValue"]),o(v,{label:"仅显示已结束返回成功但无订单信息",modelValue:r.checkError,"onUpdate:modelValue":x[1]||(x[1]=w=>r.checkError=w)},null,8,["modelValue"])]),_:1}),o(z,{height:"450"},{default:s(()=>[(b(!0),R(be,null,De(i(se),w=>(b(),R("div",Lo,[a("p",null,[Po,l(),a("span",xo,m(w.sort),1),l(),o(O,{direction:"vertical"}),l(),Eo,l(),a("span",No,m(w.requestCount),1)]),a("p",null,[a("span",Uo,"小号位置:"+m(w.account_site),1),l(),o(O,{direction:"vertical"}),a("span",To,"小号："+m(w.account),1),l(),o(O,{direction:"vertical"}),a("span",zo,"标识："+m(w.id),1),o(O,{direction:"vertical"}),a("span",Go,"order_site标识："+m(w.order_site),1)]),w.history.length?(b(),R("div",Mo,[jo,(b(!0),R(be,null,De(w.history,H=>{var ge,X;return b(),R("div",{class:it(["history-item",{error:!H.res.code&&!((ge=H.res.data)!=null&&ge.order_sn)}])},[a("p",null,[a("span",Ao,"小号位置:"+m(H.account_site),1),l(),o(O,{direction:"vertical"}),a("span",Bo,"小号："+m(H.account),1),l(),o(O,{direction:"vertical"}),a("span",Ro,[l("请求次序："),a("strong",Fo,m(H.requestCount),1)]),o(O,{direction:"vertical"}),a("span",Oo,"order_site标识："+m(H.order_site),1)]),a("p",Ho,[a("span",qo,[l("结果： "),H.res.code?H.res.code?(b(),Y(L,{key:1,type:"danger"},{default:s(()=>[l("失败")]),_:1})):fe("",!0):(b(),Y(L,{key:0,type:"success"},{default:s(()=>[l("成功")]),_:1}))])]),H.res.code?(b(),R("p",Jo,[a("span",Yo,[l("错误信息: "),a("span",Wo,m(H.res.msg),1)])])):(b(),R("p",Ko,[a("span",Qo,"订单编号："+m((X=H.res.data)==null?void 0:X.order_sn),1)]))],2)}),256))])):fe("",!0)]))),256))]),_:1})])}}}),Zo=Ne(Xo,[["__scopeId","data-v-a55dee8a"]]),es=I=>(We("data-v-b58c0ada"),I=I(),Ke(),I),ts={class:"goods-log"},os=es(()=>a("span",null,"商品ID：",-1)),ss={class:"goods-info-box"},as={class:"right"},ls={class:"title"},ns={class:"desc"},is={class:"table-foolter"},rs=Ee({__name:"goodsLog",props:{selectGoodsRecord:null,visible:{type:Boolean}},emits:["selectGoods"],setup(I,{emit:he}){const F=I,r=re({goodsId:""}),se=re({goodsLogList:[],selection:[]}),u=re({page:1,limit:20,total:0}),x=Me();function L(){var ne;const{goodsId:T}=r,{page:v,limit:O}=u,z=It.service({text:"加载中--",target:(ne=x.value)==null?void 0:ne.$el});let g=String(T);if(g.startsWith("https://")){const G=/goods_id=(\d+)/,ee=g.match(G);ee&&(g=ee[1])}yt({page:v,limit:O,goods_id:g?Number(g):void 0}).then(G=>{console.log(G),se.goodsLogList=G.list,u.total=G.total}).finally(()=>{z.close()})}function ae(T){at(T.goods_id).then(v=>{L()})}Ve(()=>F.visible,T=>{T&&L()},{immediate:!0});function h(T=se.selection){at(T.map(v=>v.goods_id).join(",")).then(v=>{L()})}async function C(){await He({type:"warning",title:"提示",message:"确定要删除所有吗？",showCancelButton:!0}),await qe.invoke("controller.goods.deleteAll"),L()}return(T,v)=>{const O=Ge,z=gt,g=Qe,ne=ro,G=_t,ee=ht,ce=Ye,W=mt,D=ct("ds-pagination");return b(),R("div",ts,[o(g,{class:"table-header"},{default:s(()=>[os,o(O,{modelValue:r.goodsId,"onUpdate:modelValue":v[0]||(v[0]=n=>r.goodsId=n)},null,8,["modelValue"]),o(z,{type:"primary",onClick:L},{default:s(()=>[l("搜索")]),_:1})]),_:1}),o(W,{border:!0,height:"450",data:se.goodsLogList,ref_key:"goodsLogTableRef",ref:x,onSelectionChange:v[1]||(v[1]=n=>{se.selection=n})},{empty:s(()=>[o(ne,{description:"暂无记录"})]),default:s(()=>[o(G,{type:"selection",width:"45"}),o(G,{label:"商品信息",prop:"",width:"500"},{default:s(n=>[a("div",ss,[o(ee,{src:n.row.goods_img},null,8,["src"]),a("div",as,[a("p",ls,m(n.row.goods_name),1),a("p",ns,[o(g,null,{default:s(()=>[a("span",null,"ID："+m(n.row.goods_id),1),a("span",null,"店铺ID："+m(n.row.mallId),1)]),_:2},1024)])])])]),_:1}),o(G,{label:"最近更新时间",prop:"add_time",width:"160",sortable:""},{default:s(n=>[l(m(i(je)(Number(n.row.add_time)).format("YYYY-MM-DD HH:mm:ss")),1)]),_:1}),o(G,{label:"操作"},{default:s(n=>[o(g,null,{default:s(()=>[o(ce,{underline:!1,type:"primary",onClick:V=>he("selectGoods",n.row.goods_id)},{default:s(()=>[l("选择")]),_:2},1032,["onClick"]),o(ce,{underline:!1,type:"danger",onClick:V=>ae(n.row)},{default:s(()=>[l("删除")]),_:2},1032,["onClick"])]),_:2},1024),a("p",null,[o(ce,{underline:!1,type:"primary",onClick:V=>I.selectGoodsRecord(n.row)},{default:s(()=>[l("本地解析")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),a("footer",is,[o(z,{type:"danger",plain:"",disabled:!se.selection.length,onClick:v[2]||(v[2]=n=>h())},{default:s(()=>[l("删除选中")]),_:1},8,["disabled"]),o(z,{type:"danger",plain:"",onClick:v[3]||(v[3]=n=>C())},{default:s(()=>[l("删除全部")]),_:1})]),o(D,{total:u.total,"current-page":u.page,"onUpdate:current-page":v[4]||(v[4]=n=>u.page=n),"page-size":u.limit,"onUpdate:page-size":v[5]||(v[5]=n=>u.limit=n),onCurrentChange:v[6]||(v[6]=n=>L()),onSizeChange:v[7]||(v[7]=n=>L())},null,8,["total","current-page","page-size"])])}}}),ds=Ne(rs,[["__scopeId","data-v-b58c0ada"]]),us=Ee({__name:"wechatAnalyze",props:{modelValue:{type:Boolean}},emits:["update:modelValue","setGoodsInfo","couponList"],setup(I,{expose:he,emit:F}){const r=re({tableMap:new Map,logList:[]});function se(h){var ge,X;const C=h.store.initDataObj.goods,T=h.store.initDataObj.mall,{goodsName:v,thumbUrl:O,minNormalPrice:z,minGroupPrice:g,goodsID:ne,skus:G,combineGroups:ee,groupTypes:ce,activity:W}=C,{mallName:D,mallId:n}=T,V={goods_id:Number(ne),mallName:D,mallId:n,goods_name:v,activity_id:(W==null?void 0:W.activityID)||0,groupPrice:Number((g/1).toFixed(2)),normalPrice:Number((z/1).toFixed(2)),goods_img:O,skus:G.map(P=>{const{skuId:ve,thumbUrl:Ae,specs:Be,normalPrice:Re,groupPrice:Fe}=P;return{skuId:ve,spec:Be.map(d=>d.spec_value).join("-"),skuImg:Ae.replaceAll("/","/"),groupPrice:Number((Number(Fe)/1).toFixed(2)),normalPrice:Number((Number(Re)/1).toFixed(2))}}),group_order_ids:ee.combineGroupList.map(P=>P.groupOrderId),group_id:{multiple:[]},coupon_code:""};ce.forEach(P=>{Number(P.requireNum)<=1?V.group_id.alone=P.groupID:V.group_id.multiple.push(P.groupID)});let w=[];const H=(X=(ge=h.store.initDataObj.oakData.subSections.discountPopSection)==null?void 0:ge.data)==null?void 0:X.mallPromoList;return H&&H.length&&(w=H),{goodsInfo:V,couponList:w}}function u(h){const C=h.goods,T=h.sku,v=h.mall_entrance.mall_data,{unselect_normal_save_price:O,min_group_price:z}=h.price,{goods_id:g,mall_id:ne,goods_name:G,thumb_url:ee,group:ce}=C,{mall_name:W}=v,D={multiple:[]};return ce.forEach(V=>{V.customer_num<=1?D.alone=V.group_id:D.multiple.push(V.group_id)}),{goods_id:Number(g),goods_name:G,goods_img:ee,mallId:ne,mallName:W,activity_id:0,groupPrice:Number((z/100).toFixed(2)),normalPrice:Number((O/100).toFixed(2)),coupon_code:"",group_id:D,group_order_ids:[],skus:T.map(V=>{const{sku_id:w,specs:H,thumb_url:ge,normal_price:X,group_price:P}=V;return{skuId:w,spec:H.map(ve=>ve.spec_value).join("-"),skuImg:ge,groupPrice:Number((Number(P)/100).toFixed(2)),normalPrice:Number((Number(X)/100).toFixed(2))}})}}function x(h,C){console.log(C);const{type:T,str:v}=C;try{const O=JSON.parse(v);let z,g=[];switch(T){case"wechat-page":{z=u(O);break}case"wechat-url":{const ne=se(O);z=ne.goodsInfo,g=ne.couponList;break}}z&&(r.tableMap.set(z.goods_id,z),L("save",z),L("load",z,g),Dt(`addLog('成功解析商品-${z.goods_id}')`),r.logList.push({goods_id:z.goods_id,msg:`${z.goods_id}-已成功解析`}))}catch(O){console.log(O)}}qe.off("analyzeController.goodsDetails",x),qe.on("analyzeController.goodsDetails",x);function L(h,C,T){if(C)switch(h){case"load":{F("setGoodsInfo",C),T&&F("couponList",T);break}case"save":{Je(C);break}}}function ae(h){switch(h){case"start":return r.tableMap.clear(),r.logList.length=0,Ct().then(C=>(console.log(C),C.success?(E.success("已启动"),Promise.resolve(C)):(E.warning("启动失败"+C.msg.toString()),Promise.reject(C))));case"close":return St().then(C=>C==="success"?(E.success({message:"已停止",grouping:!0}),F("update:modelValue",!1),Promise.resolve(C)):(E.warning({grouping:!0,message:C}),Promise.reject(C)))}}return he({proxyAction:ae}),(h,C)=>{const T=Vt;return b(),Y(T,{size:"default"})}}}),ps=Ne(us,[["__scopeId","data-v-603c5b85"]]),oe=I=>(We("data-v-3643726f"),I=I(),Ke(),I),cs={class:"index no-padding"},gs={class:"left"},ms=oe(()=>a("span",null,"任务设置",-1)),_s={class:"right"},fs={class:"config-box"},ks={class:"img-box able-select"},bs=oe(()=>a("span",null,null,-1)),hs={class:"infos"},ys={class:"name"},vs={class:"other-infos"},ws={class:"item"},Is=oe(()=>a("span",{class:"label"},"商品ID：",-1)),Vs={class:"value"},Ds={class:"item"},Ss=oe(()=>a("span",{class:"label"},"店铺名称：",-1)),Cs={class:"value"},$s={class:"item"},Ls=oe(()=>a("span",{class:"label"},"拼团价格：",-1)),Ps={class:"value"},xs={class:"item"},Es=oe(()=>a("span",{class:"label"},"独买价格：",-1)),Ns={class:"value"},Us={class:"sku"},Ts=oe(()=>a("span",{class:"label"},"商品规格：",-1)),zs=oe(()=>a("span",null,"至",-1)),Gs={class:"bottom"},Ms=oe(()=>a("br",null,null,-1)),js=oe(()=>a("br",null,null,-1)),As=oe(()=>a("br",null,null,-1)),Bs=oe(()=>a("br",null,null,-1)),Rs={class:"action-box"},Fs={class:"btns"},Os=oe(()=>a("strong",null,"请先确认好下单的店铺已登录。未登录可前往【店铺管理】-【添加店铺】进行添加。添加完成后继续执行任务！！",-1)),Hs=oe(()=>a("span",{class:"m-l-5"},"元",-1)),qs=oe(()=>a("span",{class:"m-l-5"},"折",-1)),Js=oe(()=>a("div",{class:"tips"},[a("p",null,"注意："),a("p",null,"供货管理折扣设置不高于9折后下单，可低于1折改价格且不影响最低价 "),a("p",null,"其他方式下单不能低于1折改价且影响报活动最低价"),a("p",{class:"danger"},"修改值同时也会影响正在进行的订单")],-1)),Ys={class:"table-header"},Ws={class:"btns"},Ks={key:0},Qs={key:1},Xs=oe(()=>a("span",{class:"text-overflow",title:"下一次执行时间(预计)"},"下一次执行时间",-1)),Zs={class:"table-footer"},ea={class:"table-configs"},ta={title:"执行下单时允许任务同时进行的最大值;当数据为0时表示不限制;改变时会影响正在下单的任务"},oa=oe(()=>a("p",{class:"f-s-12",style:{color:"var(--el-text-color-secondary)"}},null,-1)),sa=Ee({__name:"index",setup(I){const he=Me(),F=new Set,r=re({wechatAnalyzeShow:!1,goodsInfo:void 0,goodsLogVisible:!1,id:""});async function se(d){const e={...d};e.skus=JSON.parse(d.skus),e.group_order_ids=JSON.parse(d.group_order_ids),e.group_id=JSON.parse(d.group_id),e.group_id.alone?(r.goodsInfo=e,r.goodsLogVisible=!1,n.couponList=[]):(r.goodsLogVisible=!1,await et({mallId:d.mallId,goods_id:Number(d.goods_id)}),await new Promise((c,p)=>{let k=0;V.getGroup_id=!0;const f=()=>{const S=e.skus[k++];if(!S){E.warning({message:"初始化拼团id失败",grouping:!0}),V.getGroup_id=!1,p();return}Oe({goods_id:d.goods_id,sku_id:S.skuId},{showErrorMsg:!1,loading:!1}).then(y=>{const q={alone:y.data,multiple:[y.data]};e.group_id=q,V.getGroup_id=!1,c(!0)}).catch(y=>{f()})};f()}),r.goodsInfo=e,n.couponList=[],Je(r.goodsInfo))}const u=Xe(),x=re({delay:5,active:!1});Ve(()=>u.pageListData.unStart.length,d=>{ae()},{immediate:!0});function L(){const{active:d,tid:e}=x;d?ae():(x.tid=void 0,e&&clearTimeout(e))}function ae(){console.log("autoStartTask");const{tid:d,delay:e,active:c}=x;d||!c||(x.tid=setTimeout(()=>{x.tid=void 0;const p=u.pageListData.unStart.filter(k=>k.start_time&&k.start_time<=Date.now());if(p.length){const k=u.getEnumStatus();u.status===k.PAUSED&&(u.status=k.IDLE),X("start",p,!1)}ae()},e*1e3))}const h=re({value:"random",visible:!1,min_price:0,max_price:1,list:[{label:"指定规格",value:"1"},{label:"随机规格",value:"random"},{label:"最高价格",value:"3"},{label:"最高价格随机",value:"random-3"},{label:"最低价格",value:"4"},{label:"最低价格随机",value:"random-4"},{label:"指定价格随机",value:"random-5"}]});function C(d=h.value){var e,c;if(r.goodsInfo&&r.goodsInfo.skus.length){const p=(e=r.goodsInfo)==null?void 0:e.skus;switch(d){case"1":return p.find(k=>String(k.skuId)==n.sku);case"3":{let k;return p.forEach(f=>{(!k||Number(k.groupPrice)<Number(f.groupPrice))&&(k=f)}),k}case"4":{let k;return p.forEach(f=>{(!k||Number(k.groupPrice)>Number(f.groupPrice))&&(k=f)}),k}default:return{skuId:0,skuImg:"",spec:((c=h.list.find(f=>f.value===d))==null?void 0:c.label)||"",groupPrice:0,normalPrice:0}}}}const T=re({dialog:!1,type:"1"}),v=re({dialog:!1,input:""});function O(){const{input:d}=v;if(!/^http/.test(d))return E.warning({message:"请输入合法的链接",grouping:!0});Ht(d).then(e=>{const c=new URLSearchParams(e.data.split("?")[1]),p={};for(const[y,q]of c.entries())Reflect.set(p,y,q);console.log(p);const{cpsSign:k,pid:f,goods_id:S}=p;k&&f&&S?(n.cpsSign=k,n.pid=f,r.id=S,w()):(E.warning("获取信息错误"),D.taskCreate.type="paidan"),v.dialog=!1}).catch(e=>{E.warning(e.msg),console.log(e)})}function z(d){d==="jinbao"?v.dialog=!0:d==="wanrentuan"&&(n.mode="open_group"),d=="pifa"?n.spell_num>=2||(n.spell_num=2):n.spell_num==2&&(n.spell_num=1)}const g=re({task:void 0,logDialog:!1,taskList:[],selection:[],pagination:{page:1,limit:100},status:0,tableLoading:!1,labelList:[{label:"未开始",value:0,count:0},{label:"进行中",value:1,count:0},{label:"已暂停",value:2,count:0},{label:"已完成",value:3,count:0}],action_open_log:!1}),ne=dt(()=>{switch(g.status){case 0:return u.pageListData.unStart;case 1:return u.pageListData.loading;case 2:return u.pageListData.paused;case 3:return u.completeList}}),G=Ze.throttle(function(){const{status:d}=g,{page:e,limit:c}=g.pagination;g.tableLoading=!0,u.getTaskList({page:e,limit:c}).finally(()=>{g.tableLoading=!1})},1e3);G();const ee=no(),ce=io(),W=oo();W.total||W.getList();const D=$t();Ve(()=>D.taskCreate,d=>{if(d){const e={...d};e.setting=[...e.setting||[]],D.setOrderSetting("taskCreate",e)}},{deep:!0});const n=re({order_num:1,spell_num:2,start_site:1,mode:"open_group",keyword:"",coupon:"",use_coupon:0,start_time:0,cpsSign:"",pid:"",sku:"",couponList:[]});Lt("start_site",Pt(n,"start_site")),Ve(()=>W.list.length,(d,e)=>{d&&!e&&D.getAnySetting("start_site").then(c=>{c&&(n.start_site=c)})},{immediate:!0}),Ve(()=>n.start_site,d=>{D.setAnySetting("start_site",d)}),Ve(()=>ee.infos,d=>{var e,c,p;if(d&&ee.goods&&ee.mall){const{goodsName:k,thumbUrl:f,minNormalPrice:S,minGroupPrice:y,sideSalesTip:q,goodsID:N,skus:M,combineGroups:J,groupTypes:j,activity:_}=ee.goods,{mallName:K,mallId:de}=ee.mall,te={goods_id:N,mallName:K,mallId:de,goods_name:k,activity_id:(_==null?void 0:_.activityID)||0,groupPrice:y,normalPrice:S,goods_img:f,skus:M.map(A=>{const{skuId:we,thumbUrl:me,specs:U,normalPrice:ue,groupPrice:ie}=A;return{skuId:we,spec:U.map(Q=>Q.spec_value).join("-"),skuImg:me,groupPrice:Number(ie),normalPrice:Number(ue)}}),group_order_ids:J.combineGroupList.map(A=>A.groupOrderId),group_id:{multiple:[]},coupon_code:""};j.forEach(A=>{Number(A.requireNum)<=1?te.group_id.alone=A.groupID:te.group_id.multiple.push(A.groupID)}),r.goodsInfo=te,r.goodsInfo.skus.length||(n.sku="");const le=(p=(c=(e=ee.infos)==null?void 0:e.store.initDataObj.oakData.subSections.discountPopSection)==null?void 0:c.data)==null?void 0:p.mallPromoList;le&&le.length&&(console.log(le),n.couponList=le)}else r.goodsInfo||(n.sku="")},{immediate:!0}),Ve(()=>r.goodsInfo,d=>{var e,c;if(d){if(!d.coupon_code){const p=new Set(D.taskCreate.setting);n.use_coupon=0,p.has("coupon")&&(p.delete("coupon"),D.taskCreate.setting=[...p])}n.sku=((e=C())==null?void 0:e.skuId)||((c=d.skus[0])==null?void 0:c.skuId)||"",F.has(Number(d.goods_id))&&(Je(d),F.delete(Number(d.goods_id)))}});const V=re({goodsDetails_pifa:!1,goodsDetails_web:!1,goodsDetails_mall:!1,getGroup_id:!1,addTask:!1});async function w(d=!0,e="local"){var p,k;if(e==="wechat")r.id=r.id||"";else if(r.id){if(r.id.toString().length<3)return E.warning("id太短")}else return E.warning("请输入商品id");let c=String(r.id);if(c.startsWith("https://")){const f=/goods_id=(\d+)/,S=c.match(f);S&&(c=S[1])}switch(c=c.trim(),n.couponList=[],e){case"local":{ce.changeUrl(Xt(c));break}case"web":{if(await uo()){E.warning({message:"-检测到虚拟机环境",grouping:!0});return}V.goodsDetails_web=!0,Qt({goods_id:c},{showErrorMsg:!1}).then(async S=>{var y;if(console.log(S),(y=S.data)!=null&&y.goods_id){const{goods_id:q,goods_name:N,mallId:M,mallName:J,groupPrice:j,normalPrice:_,goods_img:K,skus:de,group_id:te}=S.data,le={goods_name:N,goods_id:q,activity_id:0,mallId:M,mallName:J,groupPrice:Number(j),normalPrice:Number(_),goods_img:K,skus:de.map(A=>{const{skuId:we,thumbUrl:me,specs:U,normalPrice:ue,groupPrice:ie}=A;return{skuId:we,spec:U.map(Q=>Q.spec_value).join("-"),skuImg:me,groupPrice:Number(ie),normalPrice:Number(ue)}}),coupon_code:"",group_id:te,group_order_ids:[]};console.log(le),r.goodsInfo=le}else return Promise.reject({msg:"没有获取到商品信息"})}).catch(S=>{console.log(S),E.error({message:S.msg,grouping:!0})}).finally(()=>{V.goodsDetails_web=!1});break}case"pifa":{V.goodsDetails_pifa=!0,Kt({goods_id:c}).then(async f=>{const{goodsId:S,goodsName:y,imageUrl:q,minWholesalePrice:N,maxWholesalePrice:M,goodsSkuInfos:J,mallCard:j}=f.result,_={goods_id:S,goods_name:y,goods_img:q,mallName:j.mallName,mallId:j.realMallId,normalPrice:M/100,groupPrice:N/100,activity_id:0,skus:J.map(te=>({skuId:te.skuId,spec:te.skuSpecs.map(A=>A.specValue).join(","),skuImg:te.thumbUrl,groupPrice:te.groupPrice/100,normalPrice:te.wholesalePrice/100})),coupon_code:"",group_order_ids:[],group_id:{multiple:[]}},K={goods_id:S,sku_id:_.skus[0].skuId},de=await Oe(K,{showErrorMsg:!1});if(typeof de.data!="string"||!de.data)return Promise.reject({errorMsg:"获取拼团ID失败"});n.mode="open_group",_.group_id.alone=Number(de.data),_.group_id.multiple=[Number(de.data)],r.goodsInfo=_}).catch(f=>{const S=f.msg||f.errorMsg||f.error_msg;E.error("获取taskAction："+(S||"网络超时,请再次尝试"))}).finally(()=>{V.goodsDetails_pifa=!1});break}case"databse":{const{list:f}=await yt({page:1,limit:20,goods_id:c});if(f.length){se(f[0]);return}break}case"wechat":{r.wechatAnalyzeShow||(rt("https://mobile.yangkeduo.com/goods.html?goods_id="+c),(p=he.value)==null||p.proxyAction("start").then(()=>{}));break}case"mall":try{const f=qt();if(!f.tableList.length){E.warning({message:"没有店铺列表信息",grouping:!0});return}const S=Me();await He({title:"选择店铺",showCancelButton:!0,message:()=>ze("div",{},[ze("p",{},`请选择商品ID${c}所在店铺`),ze(Te,{modelValue:S.value,"onUpdate:modelValue":_=>{S.value=_}},{default:()=>f.tableList.map(_=>ze(Ue,{label:_.mallName,value:_.mallId,disabled:!f.checkAvailable(_)}))})])});const y=f.tableList.find(_=>_.mallId===S.value);if(!y)return E.warning({message:"没有找到店铺",grouping:!0}),Promise.reject();V.goodsDetails_mall=!0;const M=((k=(await Jt(y,{goods_id_list:[c]})).result)==null?void 0:k.goods_list).find(_=>_.id==Number(c));if(!M)return E.warning({message:"没有找到商品",grouping:!0}),V.goodsDetails_mall=!1,Promise.reject();const J={goods_name:M.goods_name,goods_id:M.id,goods_img:M.thumb_url,activity_id:0,mallId:y.mallId,mallName:y.mallName,groupPrice:M.sku_group_price[0]/100,normalPrice:M.sku_price[0]/100,skus:M.sku_list.filter(_=>_.isOnsale).map(_=>({skuId:_.skuId,spec:_.spec,skuImg:_.skuThumbUrl,groupPrice:_.groupPrice/100,normalPrice:_.normalPrice/100})),group_id:{multiple:[]},group_order_ids:[],coupon_code:""},j=async()=>Yt(async _=>Oe({goods_id:J.goods_id,sku_id:J.skus[0].skuId},{showErrorMsg:!1}),3).then(_=>{const K=_.data;if(K)J.group_id={alone:Number(K),multiple:[Number(K)]};else return Promise.reject()});await Promise.allSettled([j().catch(async()=>et({mallId:J.mallId,goods_id:Number(J.goods_id)})).then(async()=>(await Wt(1500),j())).catch(_=>(console.log(_),E.warning({message:"获取拼团ID失败",grouping:!0}),Promise.reject()))]),V.goodsDetails_mall=!1,J.group_id.alone&&(r.goodsInfo=J)}catch{}}d&&F.add(Number(c))}function H(d){var c;const{type:e}=D.taskCreate;return d.disabled({order_type:e,isGroupOrder:!!((c=r.goodsInfo)!=null&&c.group_order_ids.length)})}async function ge(){var Le,xe;if(V.addTask)return;if(!r.goodsInfo)return E.warning({message:"没有商品信息",grouping:!0});const{skus:d}=r.goodsInfo,{order_num:e,spell_num:c,start_site:p,coupon:k,start_time:f,keyword:S,sku:y,cpsSign:q,pid:N,use_coupon:M}=n,{setting:J,type:j}=D.taskCreate;let{mode:_}=n;if(!j)return E.warning({message:"请选择下单类型",grouping:!0});if(j=="keyword"&&!S)return E.warning({message:"请先输入下单关键词",grouping:!0});const{group_id:K,group_order_ids:de,mallName:te,mallId:le,goods_id:A,goods_name:we,activity_id:me,coupon_code:U}=r.goodsInfo;if(_==="random"){const pe=st.filter(Pe=>!H(Pe)),Ie=Zt(0,pe.length);_=((Le=pe[Ie])==null?void 0:Le.value)||"open_group"}let ue;switch(_){case"spell_group":{ue=K.multiple[0];break}case"open_group":{ue=K.multiple[0];break}case"alone":{ue=K.alone||"";break}default:ue=K.multiple[0];break}if(j==="wanrentuan"){if(!K.alone)return E.warning({message:"查询到万人团需要的参数缺失，不可创建!",grouping:!0});ue=K.alone}const ie=C();if(!ie)return E.warning("请选择sku");let Q=d;{const{value:pe,max_price:Ie,min_price:Pe}=h,ke=new Map;d.forEach(B=>{const Z=ke.get(B.groupPrice);Z?Z.push(B):ke.set(B.groupPrice,[B])});const t=Math.max(...ke.keys()),$=Math.min(...ke.keys());switch(pe){case"1":{const B=d.find(Z=>String(Z.skuId)==n.sku);Q=B?[B]:[];break}case"random":{Q=[...d];break}case"3":{const B=d.find(Z=>Z.groupPrice<=$);Q=B?[B]:[];break}case"4":{const B=d.find(Z=>Z.groupPrice>=t);Q=B?[B]:[];break}case"random-3":{Q=ke.get(t)||[];break}case"random-4":{Q=ke.get($)||[];break}case"random-5":Q=[...ke.keys()].filter(Z=>Z>=Pe&&Z<=Ie).map(Z=>ke.get(Z)||[]).flat()}}if(!Q.length)return E.warning({message:"SKU筛选后没有可用的商品",grouping:!0});const Se={order_num:e,spell_num:c,start_site:p,type:j,mode:_,coupon:k,setting:J.join(","),start_time:f,goods_id:A,goods_name:we,price:_==="alone"?ie.normalPrice:ie.groupPrice,sku:ie==null?void 0:ie.skuId,skus:JSON.stringify(Q.map(({skuId:pe,spec:Ie})=>({skuId:pe,spec:Ie})).slice(0,30)),sku_spec:ie==null?void 0:ie.spec,shop_id:le||0,group_id:ue,keyword:S,activity_id:me,group_order_id:de[0],shop_name:te,cps_sign:j==="jinbao"?q:"",duo_duo_pid:j==="jinbao"?N:"",use_coupon:M,coupon_code:U,coupon_type:((xe=n.couponList.find(pe=>pe.sn==U))==null?void 0:xe.tagDesc)=="店铺关注券"?"mall":"goods"};j=="pifa"&&Se.spell_num<=1&&(E.warning({message:"批发下单每次至少需要两单,已自动调整",grouping:!0}),Se.spell_num=2),V.addTask=!0;const Ce=Ze.cloneDeep(Se);console.log(Ce),tt(Ce).then(()=>{n.start_site=(p+e)%W.list.length,E.success("创建成功"),G()}).catch(pe=>{console.log(pe)}).finally(()=>{V.addTask=!1})}async function X(d,e=g.selection,c=!0){if(!e.length)return E.warning({message:"请先选择需要操作的任务",grouping:!0});let p,k="";switch(d){case"reStart":{k="恢复执行",p=e.filter(y=>u.paused.has(y.task_id));break}case"pause":{k="暂停",p=e.filter(y=>u.loading.has(y.task_id));break}case"start":{k="开始",p=e.filter(y=>!u.loading.has(y.task_id)&&!u.paused.has(y.task_id));break}case"delete":{k="删除",p=e.filter(y=>!u.loading.has(y.task_id));break}default:return E.warning("错误的行为")}if(!p.length)return E.warning({message:`没有可以${k}的任务`,grouping:!0});c&&await He({title:"提示",type:"warning",message:`确定要对选中的任务执行${k}操作吗?`,showCancelButton:!0});const{limit:f,page:S}=g.pagination;u.taskAction(d,p,{limit:f,page:1}),d!=="delete"&&g.action_open_log&&(g.logDialog=!0)}const P=re({row:{},copyDialog:!1,copyBtnLoading:!1}),ve=re({dialog:!1});function Ae(d){const{task_id:e}=d,c=u.loading.get(e)||u.paused.get(e)||u.finish.get(e);if(!c)return E.warning({message:"没有找到任务详情数据",grouping:!0});ve.dialog=!0,ve.taskItem=c}function Be(d){var c,p;const e=((c=u.loading.get(d.task_id))==null?void 0:c.nextExecTime)||((p=u.paused.get(d.task_id))==null?void 0:p.nextExecTime);return e?je(e).format("HH:mm:ss"):"-"}function Re(d){P.row={...d},P.row.start_site=n.start_site,P.copyDialog=!0}function Fe(){const{order_num:d,spell_num:e,start_site:c,type:p,mode:k,coupon:f,setting:S,start_time:y,goods_id:q,goods_name:N,price:M,sku:J,sku_spec:j,shop_id:_,group_id:K,keyword:de,group_order_id:te,shop_name:le,cps_sign:A,duo_duo_pid:we,activity_id:me,use_coupon:U,coupon_code:ue,auto_change_price:ie,auto_supply:Q,skus:Se,coupon_type:Ce}=P.row;P.copyBtnLoading=!0,tt({order_num:d,spell_num:e,start_site:c,type:p,mode:k,coupon:f,setting:S,start_time:y,goods_id:q,goods_name:N,price:M,sku:J,sku_spec:j,shop_id:_,group_id:K,keyword:de,group_order_id:te,shop_name:le,cps_sign:A,duo_duo_pid:we,activity_id:me,use_coupon:U,coupon_code:ue,skus:Se,coupon_type:Ce}).then(()=>{u.getTaskList(),E.success("创建成功"),n.start_site=(c+d)%W.list.length,P.copyDialog=!1}).finally(()=>{P.copyBtnLoading=!1})}return(d,e)=>{var Ce,Le,xe,pe,Ie,Pe,ke;const c=Et,p=gt,k=Qe,f=zt,S=ht,y=ao,q=Gt,N=Mt,M=pt,J=to,j=ft,_=ut,K=jt,de=At,te=so,le=Bt,A=Ye,we=bt,me=kt,U=_t,ue=mt,ie=ct("ds-pagination"),Q=xt("blur"),Se=Ot;return b(),R("div",cs,[o(ps,{ref_key:"wechatAnalyzeEl",ref:he,modelValue:r.wechatAnalyzeShow,"onUpdate:modelValue":e[0]||(e[0]=t=>r.wechatAnalyzeShow=t),onSetGoodsInfo:e[1]||(e[1]=t=>{r.goodsInfo=t,n.couponList=[]}),onCouponList:e[2]||(e[2]=t=>n.couponList=t)},null,8,["modelValue"]),a("h5",null,[a("div",gs,[ms,o(i(Ge),{placeholder:"输入商品ID或链接",modelValue:r.id,"onUpdate:modelValue":e[4]||(e[4]=t=>r.id=t),onKeyup:eo(w,["enter"]),onClick:e[5]||(e[5]=()=>{i(Ut)().then(t=>{r.id=t,w(!1,"databse")})})},{append:s(()=>[o(p,{type:"primary",onClick:e[3]||(e[3]=t=>w())},{default:s(()=>[o(c,{class:"m-r-5"},{default:s(()=>[o(i(Nt))]),_:1}),l("解析")]),_:1})]),_:1},8,["modelValue","onKeyup"]),o(p,{loading:V.goodsDetails_web,disabled:V.goodsDetails_web,onClick:e[6]||(e[6]=t=>w(!0,"web"))},{default:s(()=>[l("云端解析")]),_:1},8,["loading","disabled"]),o(p,{loading:V.goodsDetails_pifa,disabled:V.goodsDetails_pifa,onClick:e[7]||(e[7]=t=>w(!0,"pifa"))},{default:s(()=>[l("批发解析")]),_:1},8,["loading","disabled"]),o(p,{onClick:e[8]||(e[8]=t=>w(!1,"wechat"))},{default:s(()=>[l("微信解析")]),_:1}),o(p,{onClick:e[9]||(e[9]=t=>w(!0,"mall")),loading:V.goodsDetails_mall},{default:s(()=>[l("店铺解析")]),_:1},8,["loading"]),_e(o(p,{loading:V.getGroup_id,plain:"",type:"primary",icon:i(Tt),onClick:e[10]||(e[10]=()=>{r.goodsLogVisible=!0})},null,8,["loading","icon"]),[[Q]])]),a("div",_s,[o(k)])]),o(f,{title:"商品读取记录",modelValue:r.goodsLogVisible,"onUpdate:modelValue":e[13]||(e[13]=t=>r.goodsLogVisible=t),width:864,top:"100px",class:"read-log"},{footer:s(()=>[o(p,{onClick:e[12]||(e[12]=t=>r.goodsLogVisible=!1)},{default:s(()=>[l("关闭")]),_:1})]),default:s(()=>[o(ds,{visible:r.goodsLogVisible,"select-goods-record":se,onSelectGoods:e[11]||(e[11]=t=>{r.id=t,w(!1),r.goodsLogVisible=!1})},null,8,["visible"])]),_:1},8,["modelValue"]),a("div",fs,[a("div",ks,[o(S,{src:((Le=(Ce=r.goodsInfo)==null?void 0:Ce.skus.find(t=>t.skuId==n.sku))==null?void 0:Le.skuImg)||((xe=r.goodsInfo)==null?void 0:xe.goods_img),"preview-src-list":[((Ie=(pe=r.goodsInfo)==null?void 0:pe.skus.find(t=>t.skuId==n.sku))==null?void 0:Ie.skuImg)||((Pe=r.goodsInfo)==null?void 0:Pe.goods_img)||""]},{error:s(()=>[bs]),_:1},8,["src","preview-src-list"]),a("div",hs,[a("p",ys,m(((ke=r.goodsInfo)==null?void 0:ke.goods_name)||"-"),1),a("p",vs,[o(k,{size:24},{default:s(()=>{var t,$,B,Z;return[a("span",ws,[Is,a("span",Vs,m(((t=r.goodsInfo)==null?void 0:t.goods_id)||"-"),1)]),a("span",Ds,[Ss,a("span",Cs,m((($=r.goodsInfo)==null?void 0:$.mallName)||"-"),1)]),a("span",$s,[Ls,a("span",Ps,"￥"+m(((B=r.goodsInfo)==null?void 0:B.groupPrice)||"-"),1)]),a("span",xs,[Es,a("span",Ns,"￥"+m(((Z=r.goodsInfo)==null?void 0:Z.normalPrice)||"-"),1)])]}),_:1})]),a("p",Us,[Ts,o(i(Te),{modelValue:h.value,"onUpdate:modelValue":e[14]||(e[14]=t=>h.value=t),style:{width:"120px","flex-grow":"0"},onChange:e[15]||(e[15]=()=>{var t;n.sku=((t=C())==null?void 0:t.skuId)||""}),placement:"bottom"},{default:s(()=>[(b(!0),R(be,null,De(h.list,t=>(b(),Y(i(Ue),{key:t.value,value:t.value,label:t.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"]),h.value.includes("random")?h.value==="random-5"?(b(),Y(k,{key:1,class:"m-l-20"},{default:s(()=>[o(y,{modelValue:h.min_price,"onUpdate:modelValue":e[18]||(e[18]=t=>h.min_price=t),placeholder:"最小值",controls:!1,precision:2,min:0},null,8,["modelValue"]),zs,o(y,{modelValue:h.max_price,"onUpdate:modelValue":e[19]||(e[19]=t=>h.max_price=t),placeholder:"最大值",controls:!1,precision:2,min:0},null,8,["modelValue"])]),_:1})):fe("",!0):(b(),Y(i(Te),{key:0,"popper-class":"sku-select-popper",placement:"bottom-end",placeholder:"请选择",modelValue:n.sku,"onUpdate:modelValue":e[16]||(e[16]=t=>n.sku=t),onChange:e[17]||(e[17]=t=>h.value="1")},{default:s(()=>{var t;return[(b(!0),R(be,null,De(((t=r.goodsInfo)==null?void 0:t.skus)||[],$=>(b(),Y(i(Ue),{key:$.skuId,value:$.skuId,label:$.spec+`-拼团价${$.groupPrice}-单买价${$.normalPrice}`},null,8,["value","label"]))),128))]}),_:1},8,["modelValue"]))])])]),a("div",Gs,[o(le,{inline:!0},{default:s(()=>[o(p,{type:"primary",onClick:e[20]||(e[20]=t=>ge()),class:"btn",loading:V.addTask},{default:s(()=>[o(q,{href:"icon-circle-plus-filled",style:{"margin-right":"4px"}}),l("添加任务")]),_:1},8,["loading"]),o(N,{label:"订单数量："},{default:s(()=>[o(y,{modelValue:n.order_num,"onUpdate:modelValue":e[21]||(e[21]=t=>n.order_num=t),precision:0,min:1},null,8,["modelValue"])]),_:1}),o(M,{direction:"vertical"}),o(N,{label:"每次购买："},{default:s(()=>[o(y,{modelValue:n.spell_num,"onUpdate:modelValue":e[22]||(e[22]=t=>n.spell_num=t),precision:0,min:1},null,8,["modelValue"])]),_:1}),o(M,{direction:"vertical"}),o(N,{label:"小号开始位置："},{default:s(()=>[o(y,{precision:0,min:1,max:i(W).total||1,placeholder:`1-${i(W).total+1}`,modelValue:n.start_site,"onUpdate:modelValue":e[23]||(e[23]=t=>n.start_site=t)},null,8,["max","placeholder","modelValue"])]),_:1}),Ms,o(N,{label:"下单类型："},{default:s(()=>[o(j,{modelValue:i(D).taskCreate.type,"onUpdate:modelValue":e[24]||(e[24]=t=>i(D).taskCreate.type=t),onChange:z},{default:s(()=>[(b(!0),R(be,null,De(i(ot).filter(t=>t.showInCreate!==!1),t=>(b(),R(be,null,[t.disabled?fe("",!0):(b(),Y(J,{key:0,border:"",label:t.value,title:t.tooltipContent},{default:s(()=>[l(m(t.label),1)]),_:2},1032,["label","title"]))],64))),256))]),_:1},8,["modelValue"])]),_:1}),js,o(N,{label:"下单方式："},{default:s(()=>[o(j,{modelValue:n.mode,"onUpdate:modelValue":e[25]||(e[25]=t=>n.mode=t)},{default:s(()=>[(b(!0),R(be,null,De(i(st),({label:t,value:$,disabled:B})=>{var Z;return b(),Y(J,{border:"",label:$,disabled:B({order_type:i(D).taskCreate.type,isGroupOrder:!!((Z=r.goodsInfo)!=null&&Z.group_order_ids.length)})},{default:s(()=>[l(m(t),1)]),_:2},1032,["label","disabled"])}),256)),o(J,{border:"",label:"random"},{default:s(()=>[l("随机")]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(M,{direction:"vertical"}),o(N,{label:"下单关键词："},{default:s(()=>[o(i(Ge),{modelValue:n.keyword,"onUpdate:modelValue":e[26]||(e[26]=t=>n.keyword=t),disabled:i(D).taskCreate.type!=="keyword",placeholder:"请输入"},null,8,["modelValue","disabled"])]),_:1}),As,o(N,{label:"其他设置："},{default:s(()=>[o(K,{modelValue:i(D).taskCreate.setting,"onUpdate:modelValue":e[27]||(e[27]=t=>i(D).taskCreate.setting=t)},{default:s(()=>{var t;return[o(_,{label:"collect"},{default:s(()=>[l("商品收藏")]),_:1}),o(_,{label:"attention"},{default:s(()=>[l("店铺关注")]),_:1}),o(_,{label:"coupon",disabled:!((t=r.goodsInfo)!=null&&t.coupon_code),title:"需要有可领优惠券且必须关注店铺"},{default:s(()=>[l("领取优惠券")]),_:1},8,["disabled"])]}),_:1},8,["modelValue"])]),_:1}),o(M,{direction:"vertical"}),o(N,{label:"使用优惠券"},{default:s(()=>{var t;return[o(de,{modelValue:n.use_coupon,"onUpdate:modelValue":e[28]||(e[28]=$=>n.use_coupon=$),title:"需要有可领优惠券",disabled:!((t=r.goodsInfo)!=null&&t.coupon_code),"active-value":1,"inactive-value":0},null,8,["modelValue","disabled"])]}),_:1}),r.goodsInfo&&n.couponList.length?(b(),R(be,{key:0},[o(M,{direction:"vertical"}),o(N,{label:"优惠券列表"},{default:s(()=>[o(i(Te),{modelValue:r.goodsInfo.coupon_code,"onUpdate:modelValue":e[29]||(e[29]=t=>r.goodsInfo.coupon_code=t),onChange:e[30]||(e[30]=()=>{n.use_coupon=1})},{default:s(()=>[(b(!0),R(be,null,De(n.couponList,t=>{var $;return b(),R(be,null,[t.batchSn?(b(),Y(i(Ue),{key:0,label:`${Number(t.discount/100).toFixed(0)}元-`+t.tagDesc+"-"+((($=t.richRulesDesc)==null?void 0:$.map(B=>B.txt).join("-"))||""),value:t.sn},null,8,["label","value"])):fe("",!0)],64)}),256))]),_:1},8,["modelValue"])]),_:1})],64)):fe("",!0),Bs,o(N,{label:"开始时间："},{default:s(()=>[o(te,{modelValue:n.start_time,"onUpdate:modelValue":e[31]||(e[31]=t=>n.start_time=t),style:{width:"292px"},type:"datetime",placeholder:"选择开始时间(可不选)","value-format":"x"},null,8,["modelValue"])]),_:1})]),_:1}),a("div",Rs,[o(_,{size:"default",label:"自动设供货",title:"仅对 '批发下单' 有效",modelValue:i(D).taskCreate.auto_supply,"onUpdate:modelValue":e[32]||(e[32]=t=>i(D).taskCreate.auto_supply=t)},null,8,["modelValue"]),o(_,{size:"default",label:"自动改价",modelValue:i(D).taskCreate.auto_change_price,"onUpdate:modelValue":e[33]||(e[33]=t=>i(D).taskCreate.auto_change_price=t),onChange:e[34]||(e[34]=t=>t&&(T.dialog=!0))},null,8,["modelValue"]),o(_,{size:"default",modelValue:x.active,"onUpdate:modelValue":e[35]||(e[35]=t=>x.active=t),onChange:L,title:"自动开始的任务到时间自动开始,手动开始的任务无效"},{default:s(()=>[l("自动开始")]),_:1},8,["modelValue"]),o(_,{size:"default",modelValue:g.action_open_log,"onUpdate:modelValue":e[36]||(e[36]=t=>g.action_open_log=t),label:"自动打开日志"},null,8,["modelValue"]),a("div",Fs,[o(p,{type:"primary",onClick:e[37]||(e[37]=t=>g.logDialog=!0)},{default:s(()=>[l("打开日志")]),_:1}),o(p,{underline:!1,type:"primary",plain:"",onClick:e[38]||(e[38]=t=>i(u).finish.clear()),title:"清除已完成的任务缓存数据"},{default:s(()=>[l("清除任务缓存")]),_:1})])])])]),o(f,{modelValue:T.dialog,"onUpdate:modelValue":e[43]||(e[43]=t=>T.dialog=t),width:480,"append-to-body":!0,title:"0.01改销量或折扣",class:"change_sale_or_discount_dialog dialog-480"},{footer:s(()=>[o(p,{type:"primary",onClick:e[42]||(e[42]=t=>T.dialog=!1)},{default:s(()=>[l("确定")]),_:1})]),default:s(()=>[o(we,{type:"info",closable:!1},{default:s(()=>[Os,a("p",null,[o(A,{underline:!1,type:"primary",onClick:e[39]||(e[39]=()=>{T.dialog=!1,d.$router.push("/Home/storeManage")})},{default:s(()=>[l("添加店铺>>")]),_:1})])]),_:1}),o(le,{class:"m-t-10",size:"default"},{default:s(()=>[o(N,{label:"批发类型改价"},{default:s(()=>[o(y,{controls:!1,size:"default",precision:2,min:.01,modelValue:i(D).taskCreate.pifaTargetPrice,"onUpdate:modelValue":e[40]||(e[40]=t=>i(D).taskCreate.pifaTargetPrice=t)},null,8,["min","modelValue"]),Hs]),_:1}),o(N,{label:"其他类型改价"},{default:s(()=>[o(y,{controls:!1,size:"default",precision:2,min:1,max:9.99,modelValue:i(D).taskCreate.othersTargetDiscount,"onUpdate:modelValue":e[41]||(e[41]=t=>i(D).taskCreate.othersTargetDiscount=t)},null,8,["max","modelValue"]),l(),qs]),_:1})]),_:1}),Js]),_:1},8,["modelValue"]),a("div",Ys,[o(k,null,{default:s(()=>[o(j,{size:"default",modelValue:g.status,"onUpdate:modelValue":e[44]||(e[44]=t=>g.status=t)},{default:s(()=>[o(me,{label:0},{default:s(()=>[l("未开始("+m(i(u).pageListData.unStart.length)+")",1)]),_:1}),o(me,{label:1},{default:s(()=>[l("进行中("+m(i(u).pageListData.loading.length)+")",1)]),_:1}),o(me,{label:2},{default:s(()=>[l("已暂停("+m(i(u).pageListData.paused.length)+")",1)]),_:1}),o(me,{label:3},{default:s(()=>[l("已完成("+m(i(u).completeTotal)+")",1)]),_:1})]),_:1},8,["modelValue"]),o(p,{underline:!1,type:"primary",onClick:e[45]||(e[45]=t=>i(G)()),title:"刷新列表"},{default:s(()=>[o(c,{class:"m-r-5"},{default:s(()=>[o(i(Rt))]),_:1}),l("刷新")]),_:1})]),_:1}),a("div",Ws,[_e((b(),Y(p,{size:"default",type:"success",onClick:e[46]||(e[46]=t=>X("start"))},{default:s(()=>[o(q,{href:"icon-play-filled",style:{"margin-right":"4px"}}),l("开始任务")]),_:1})),[[ye,g.status===0],[Q]]),_e((b(),Y(p,{size:"default",type:"success",onClick:e[47]||(e[47]=t=>X("reStart"))},{default:s(()=>[o(q,{href:"icon-play-filled",style:{"margin-right":"4px"}}),l("继续任务")]),_:1})),[[ye,g.status===2],[Q]]),_e((b(),Y(p,{size:"default",icon:i(Ft),type:"danger",plain:"",onClick:e[48]||(e[48]=t=>X("delete"))},{default:s(()=>[l("删除任务")]),_:1},8,["icon"])),[[ye,g.status!==1],[Q]]),_e((b(),Y(p,{size:"default",onClick:e[49]||(e[49]=t=>X("pause"))},{default:s(()=>[l("暂停任务")]),_:1})),[[ye,g.status==1],[Q]])])]),_e((b(),Y(ue,{border:!0,data:i(ne),onSelectionChange:e[50]||(e[50]=t=>g.selection=t),stripe:!0},{default:s(()=>[o(U,{type:"selection",width:"40"}),o(U,{type:"index",width:"50",label:"序号"}),o(U,{label:"开始时间",prop:"start_time",so:"",rtable:"",width:"150"},{default:s(t=>[t.row.start_time?(b(),R("span",Ks,m(i(je)(t.row.start_time).format("YYYY-MM-DD HH:mm:ss")),1)):(b(),R("span",Qs,"手动开始"))]),_:1}),o(U,{label:"下一次执行时间"},{header:s(()=>[Xs]),default:s(t=>[l(m(Be(t.row)),1)]),_:1}),o(U,{width:"70",label:"下单数量",prop:"order_num"}),o(U,{width:"70",label:"已团数量",prop:"complete_num"}),o(U,{width:"70",label:"起始位置",prop:"start_site"}),o(U,{width:"70",label:"当前位置",prop:"account_site"}),o(U,{label:"商品ID",width:"105",prop:"goods_id"}),o(U,{label:"店铺名",width:"105",prop:"shop_name","show-overflow-tooltip":""}),o(U,{label:"商品信息",width:"105",prop:"","show-overflow-tooltip":""},{default:s(t=>[l(m(t.row.goods_name+"-"+t.row.sku_spec),1)]),_:1}),o(U,{label:"下单类型",prop:"type"},{default:s(t=>{var $;return[l(m(($=i(ot).find(B=>B.value==t.row.type))==null?void 0:$.label),1)]}),_:1}),o(U,{width:"70",label:"每单件数",prop:"spell_num"}),o(U,{label:"优惠券",prop:"use_coupon"},{default:s(t=>[a("span",null,m(t.row.use_coupon?"是":"否"),1)]),_:1}),o(U,{label:"商品规格",prop:"sku_spec",width:"100","show-overflow-tooltip":""}),o(U,{label:"价格(元)",prop:"price"}),o(U,{label:"商品收藏",prop:""},{default:s(t=>{var $;return[l(m(($=t.row.setting)!=null&&$.includes("collect")?"是":"否"),1)]}),_:1}),o(U,{label:"店铺关注",prop:""},{default:s(t=>{var $;return[l(m(($=t.row.setting)!=null&&$.includes("attention")?"是":"否"),1)]}),_:1}),o(U,{fixed:"right",label:"操作",prop:"",width:"200",align:"center"},{default:s(t=>[_e(o(A,{underline:!1,onClick:$=>X("pause",[t.row])},{default:s(()=>[o(q,{href:"icon-pause-filled"}),l(" 暂停 ")]),_:2},1032,["onClick"]),[[ye,i(u).loading.has(t.row.task_id)]]),_e(o(A,{underline:!1,type:"success",onClick:$=>X("start",[t.row])},{default:s(()=>[o(q,{href:"icon-play-filled"}),l(" 开始 ")]),_:2},1032,["onClick"]),[[ye,!i(u).loading.has(t.row.task_id)&&!i(u).paused.has(t.row.task_id)&&g.status!==3]]),_e(o(A,{underline:!1,type:"success",onClick:$=>X("reStart",[t.row])},{default:s(()=>[o(q,{href:"icon-play-filled"}),l(" 恢复 ")]),_:2},1032,["onClick"]),[[ye,i(u).paused.has(t.row.task_id)]]),_e(o(A,{underline:!1,type:"danger",onClick:$=>X("delete",[t.row])},{default:s(()=>[o(q,{href:"icon-delete-filled"}),l(" 删除 ")]),_:2},1032,["onClick"]),[[ye,!i(u).loading.has(t.row.task_id)]]),o(A,{underline:!1,onClick:$=>Re(t.row)},{default:s(()=>[o(q,{href:"icon-circle-plus-filled"}),l(" 复制 ")]),_:2},1032,["onClick"]),i(u).paused.has(t.row.task_id)||i(u).loading.has(t.row.task_id)||i(u).finish.has(t.row.task_id)?(b(),Y(A,{key:0,underline:!1,onClick:$=>Ae(t.row)},{default:s(()=>[l("查看任务状态")]),_:2},1032,["onClick"])):fe("",!0)]),_:1})]),_:1},8,["data"])),[[Se,g.tableLoading]]),o(f,{modelValue:ve.dialog,"onUpdate:modelValue":e[51]||(e[51]=t=>ve.dialog=t),title:"任务详情","append-to-body":!0,class:"task-details-dialog"},{default:s(()=>[o(Zo,{"task-details-state":ve},null,8,["task-details-state"])]),_:1},8,["modelValue"]),a("footer",Zs,[a("div",ea,[o(k,{size:20},{default:s(()=>[a("span",ta,[l(" 任务最大： "),o(y,{style:{width:"50px"},modelValue:i(u).requestCountMax,"onUpdate:modelValue":e[52]||(e[52]=t=>i(u).requestCountMax=t),min:0,precision:0,controls:!1},null,8,["modelValue"])])]),_:1})]),_e(o(ie,{total:i(u).completeTotal,"pager-count":3,"current-page":g.pagination.page,"onUpdate:current-page":e[53]||(e[53]=t=>g.pagination.page=t),"page-size":g.pagination.limit,"onUpdate:page-size":e[54]||(e[54]=t=>g.pagination.limit=t),onSizeChange:e[55]||(e[55]=t=>i(G)()),onCurrentChange:e[56]||(e[56]=t=>i(G)())},null,8,["total","current-page","page-size"]),[[ye,g.status===3]])]),o(f,{title:"任务日志",width:864,modelValue:g.logDialog,"onUpdate:modelValue":e[60]||(e[60]=t=>g.logDialog=t),class:"task-log-dialog"},{footer:s(()=>[o(p,{type:"success",disabled:!i(u).paused.size,onClick:e[57]||(e[57]=t=>X("reStart",i(u).pageListData.paused))},{default:s(()=>[l("恢复全部请求")]),_:1},8,["disabled"]),o(p,{type:"danger",disabled:!i(u).loading.size,onClick:e[58]||(e[58]=t=>X("pause",i(u).pageListData.loading))},{default:s(()=>[l("暂停全部请求")]),_:1},8,["disabled"]),o(p,{onClick:e[59]||(e[59]=t=>g.logDialog=!1)},{default:s(()=>[l("关闭窗口")]),_:1})]),default:s(()=>[o(bo,{visible:g.logDialog},null,8,["visible"])]),_:1},8,["modelValue"]),o(f,{title:"多多进宝【请在商家后台开通】",width:480,class:"dialog-480",modelValue:v.dialog,"onUpdate:modelValue":e[63]||(e[63]=t=>v.dialog=t)},{footer:s(()=>[o(p,{onClick:e[62]||(e[62]=()=>{i(D).taskCreate.type="paidan",v.dialog=!1})},{default:s(()=>[l("取消")]),_:1}),o(p,{type:"primary",onClick:O},{default:s(()=>[l("确定")]),_:1})]),default:s(()=>[o(le,{"label-position":"top"},{default:s(()=>[o(N,{label:"推广链接"},{default:s(()=>[o(i(Ge),{modelValue:v.input,"onUpdate:modelValue":e[61]||(e[61]=t=>v.input=t)},null,8,["modelValue"])]),_:1})]),_:1}),oa]),_:1},8,["modelValue"]),o(f,{modelValue:P.copyDialog,"onUpdate:modelValue":e[70]||(e[70]=t=>P.copyDialog=t),title:"复制任务",width:480,class:"dialog-480"},{footer:s(()=>[o(p,{onClick:e[68]||(e[68]=t=>P.copyDialog=!1)},{default:s(()=>[l("取消")]),_:1}),o(p,{type:"primary",loading:P.copyBtnLoading,disabled:P.copyBtnLoading,onClick:e[69]||(e[69]=t=>Fe())},{default:s(()=>[l("创建")]),_:1},8,["loading","disabled"])]),default:s(()=>[o(le,{"label-position":"top"},{default:s(()=>[o(N,{label:"下单数量",prop:""},{default:s(()=>[o(y,{modelValue:P.row.order_num,"onUpdate:modelValue":e[64]||(e[64]=t=>P.row.order_num=t),precision:0,min:1},null,8,["modelValue"])]),_:1}),o(N,{label:"每次购买："},{default:s(()=>[o(y,{modelValue:P.row.spell_num,"onUpdate:modelValue":e[65]||(e[65]=t=>P.row.spell_num=t),precision:0,min:P.row.type==="pifa"?2:1},null,8,["modelValue","min"])]),_:1}),o(N,{label:"小号开始位置："},{default:s(()=>[o(y,{precision:0,min:1,max:i(W).total||1,placeholder:`1-${i(W).total+1}`,modelValue:P.row.start_site,"onUpdate:modelValue":e[66]||(e[66]=t=>P.row.start_site=t)},null,8,["max","placeholder","modelValue"])]),_:1}),o(N,{label:"开始时间："},{default:s(()=>[o(te,{modelValue:P.row.start_time,"onUpdate:modelValue":e[67]||(e[67]=t=>P.row.start_time=t),style:{width:"292px"},type:"datetime",placeholder:"选择开始时间","value-format":"x"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),Ia=Ne(sa,[["__scopeId","data-v-3643726f"]]);export{Ia as default};
